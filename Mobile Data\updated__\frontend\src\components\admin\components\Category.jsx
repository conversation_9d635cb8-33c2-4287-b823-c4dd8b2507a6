import React, { useState } from "react";

const Category = () => {
  // Dummy data for dropdowns
  const labs = [
    { _id: "lab1", name: "Chemistry Lab" },
    { _id: "lab2", name: "Physics Lab" },
  ];
  const labSections = [
    { _id: "section1", name: "Organic", labId: "lab1" },
    { _id: "section2", name: "Inorganic", labId: "lab1" },
    { _id: "section3", name: "Optics", labId: "lab2" },
  ];
  const categories = [
    { _id: "cat1", name: "Chemicals" },
    { _id: "cat2", name: "Equipment" },
  ];

  const initialForm = {
    lab: "",
    labSection: "",
    parentCategory: "",
    name: "",
    slug: "",
    description: "",
    metaTitle: "",
    metaDescription: "",
    order: 0,
    isActive: true,
    icon: null,
  };

  const [form, setForm] = useState(initialForm);
  const [categoryList, setCategoryList] = useState([]);
  const [editingId, setEditingId] = useState(null); // Keep track if we are editing

  // Reset form after submission or cancel
  const resetForm = () => {
    setForm(initialForm);
    setEditingId(null);
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setForm((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFileChange = (e) => {
    setForm((prev) => ({
      ...prev,
      icon:
        e.target.files && e.target.files.length > 0 ? e.target.files[0] : null,
    }));
  };

  // Handle add or update
  const handleSubmit = (e) => {
    e.preventDefault();
    if (editingId) {
      // Update mode
      setCategoryList((prev) =>
        prev.map((cat) =>
          cat.id === editingId ? { ...form, id: editingId } : cat
        )
      );
    } else {
      // Add mode
      const newCategory = { ...form, id: Date.now() };
      setCategoryList((prev) => [...prev, newCategory]);
    }
    resetForm();
  };

  // "Edit" handler
  const handleEdit = (cat) => {
    setForm({
      lab: cat.lab,
      labSection: cat.labSection,
      parentCategory: cat.parentCategory,
      name: cat.name,
      slug: cat.slug,
      description: cat.description,
      metaTitle: cat.metaTitle,
      metaDescription: cat.metaDescription,
      order: cat.order,
      isActive: cat.isActive,
      icon: cat.icon,
    });
    setEditingId(cat.id);
  };

  // "Delete" handler
  const handleDelete = (id) => {
    setCategoryList((prev) => prev.filter((cat) => cat.id !== id));
    if (editingId === id) resetForm();
  };

  // Display helpers
  const getLabName = (id) => labs.find((l) => l._id === id)?.name || "";
  const getLabSectionName = (id) =>
    labSections.find((ls) => ls._id === id)?.name || "";
  const getParentCategoryName = (id) =>
    categories.find((cat) => cat._id === id)?.name || "";

  return (
    <div className="max-w-7xl mx-auto p-8">
      {/* FORM */}
      <form
        className="bg-white p-8 rounded shadow space-y-6"
        onSubmit={handleSubmit}
      >
        <h2 className="text-2xl font-bold mb-4">
          {editingId ? "Edit" : "Add"} Category
        </h2>
        {/* ... form fields unchanged ... */}

        {/* Lab */}
        <div>
          <label className="block font-medium mb-1">
            Lab<span className="text-red-500">*</span>
          </label>
          <select
            className="block w-full border border-gray-300 px-3 py-2 rounded"
            name="lab"
            value={form.lab}
            onChange={handleChange}
            required
          >
            <option value="">Select Lab</option>
            {labs.map((l) => (
              <option key={l._id} value={l._id}>
                {l.name}
              </option>
            ))}
          </select>
        </div>
        {/* Lab Section */}
        <div>
          <label className="block font-medium mb-1">
            Lab Section<span className="text-red-500">*</span>
          </label>
          <select
            className="block w-full border border-gray-300 px-3 py-2 rounded"
            name="labSection"
            value={form.labSection}
            onChange={handleChange}
            required
          >
            <option value="">Select Lab Section</option>
            {labSections
              .filter((ls) => ls.labId === form.lab)
              .map((ls) => (
                <option key={ls._id} value={ls._id}>
                  {ls.name}
                </option>
              ))}
          </select>
        </div>
        {/* Parent Category */}
        <div>
          <label className="block font-medium mb-1">Parent Category</label>
          <select
            className="block w-full border border-gray-300 px-3 py-2 rounded"
            name="parentCategory"
            value={form.parentCategory}
            onChange={handleChange}
          >
            <option value="">None (Top-level)</option>
            {categories.map((cat) => (
              <option key={cat._id} value={cat._id}>
                {cat.name}
              </option>
            ))}
          </select>
        </div>
        {/* Name */}
        <div>
          <label className="block font-medium mb-1">
            Name<span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="name"
            value={form.name}
            onChange={handleChange}
            className="block w-full border border-gray-300 px-3 py-2 rounded"
            required
          />
        </div>
        {/* Slug */}
        <div>
          <label className="block font-medium mb-1">Slug</label>
          <input
            type="text"
            name="slug"
            value={form.slug}
            onChange={handleChange}
            className="block w-full border border-gray-300 px-3 py-2 rounded"
          />
        </div>
        {/* Description */}
        <div>
          <label className="block font-medium mb-1">Description</label>
          <textarea
            name="description"
            value={form.description}
            onChange={handleChange}
            className="block w-full border border-gray-300 px-3 py-2 rounded"
          />
        </div>
        {/* Icon */}
        <div>
          <label className="block font-medium mb-1">Icon</label>
          <input
            type="file"
            name="icon"
            accept="image/*"
            onChange={handleFileChange}
            className="block w-full"
          />
          {form.icon && (
            <img
              src={
                typeof form.icon === "string"
                  ? form.icon
                  : URL.createObjectURL(form.icon)
              }
              alt="Icon Preview"
              className="w-16 h-16 mt-2 object-cover rounded"
            />
          )}
        </div>
        {/* Order */}
        <div>
          <label className="block font-medium mb-1">Order</label>
          <input
            type="number"
            name="order"
            value={form.order}
            min={0}
            onChange={handleChange}
            className="block w-full border border-gray-300 px-3 py-2 rounded"
          />
        </div>
        {/* Is Active */}
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            name="isActive"
            checked={form.isActive}
            onChange={handleChange}
            className="rounded"
            id="isActive"
          />
          <label htmlFor="isActive" className="font-medium">
            Is Active
          </label>
        </div>
        {/* Meta Title */}
        <div>
          <label className="block font-medium mb-1">Meta Title</label>
          <input
            type="text"
            name="metaTitle"
            value={form.metaTitle}
            onChange={handleChange}
            className="block w-full border border-gray-300 px-3 py-2 rounded"
          />
        </div>
        {/* Meta Description */}
        <div>
          <label className="block font-medium mb-1">Meta Description</label>
          <textarea
            name="metaDescription"
            value={form.metaDescription}
            onChange={handleChange}
            className="block w-full border border-gray-300 px-3 py-2 rounded"
          />
        </div>
        <div className="flex gap-4">
          <button
            type="submit"
            className="bg-blue-600 text-white py-2 px-6 rounded hover:bg-blue-700 transition"
          >
            {editingId ? "Update" : "Save"}
          </button>
          {editingId && (
            <button
              type="button"
              onClick={resetForm}
              className="bg-gray-300 text-gray-700 py-2 px-6 rounded hover:bg-gray-400 transition"
            >
              Cancel
            </button>
          )}
        </div>
      </form>

      {/* TABLE */}
      <div className="mt-12">
        <h3 className="text-xl font-semibold mb-4">Category List</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full border border-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-2 border text-left">Name</th>
                <th className="p-2 border text-left">Lab</th>
                <th className="p-2 border text-left">Lab Section</th>
                <th className="p-2 border text-left">Parent</th>
                <th className="p-2 border text-left">Order</th>
                <th className="p-2 border text-left">Status</th>
                <th className="p-2 border text-left">Icon</th>
                <th className="p-2 border text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {categoryList.length === 0 && (
                <tr>
                  <td colSpan={8} className="p-4 text-center text-gray-500">
                    No categories added yet.
                  </td>
                </tr>
              )}
              {categoryList.map((cat) => (
                <tr key={cat.id} className="border-b hover:bg-gray-50">
                  <td className="p-2 border">{cat.name}</td>
                  <td className="p-2 border">{getLabName(cat.lab)}</td>
                  <td className="p-2 border">
                    {getLabSectionName(cat.labSection)}
                  </td>
                  <td className="p-2 border">
                    {getParentCategoryName(cat.parentCategory) || "—"}
                  </td>
                  <td className="p-2 border">{cat.order}</td>
                  <td className="p-2 border">
                    <span
                      className={`inline-block px-2 py-0.5 rounded text-xs font-semibold
                    ${
                      cat.isActive
                        ? "bg-green-100 text-green-800"
                        : "bg-gray-200 text-gray-600"
                    }`}
                    >
                      {cat.isActive ? "Active" : "Inactive"}
                    </span>
                  </td>
                  <td className="p-2 border">
                    {cat.icon ? (
                      <img
                        src={
                          typeof cat.icon === "string"
                            ? cat.icon
                            : URL.createObjectURL(cat.icon)
                        }
                        alt="Icon"
                        className="w-8 h-8 object-cover rounded"
                      />
                    ) : (
                      <span className="text-gray-400">None</span>
                    )}
                  </td>
                  <td className="p-2 border space-x-2">
                    <button
                      className="bg-yellow-400 hover:bg-yellow-500 text-white py-1 px-3 rounded"
                      onClick={() => handleEdit(cat)}
                    >
                      Edit
                    </button>
                    <button
                      className="bg-red-500 hover:bg-red-600 text-white py-1 px-3 rounded"
                      onClick={() => handleDelete(cat.id)}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Category;
