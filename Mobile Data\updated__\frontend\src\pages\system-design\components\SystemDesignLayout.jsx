import React from 'react';
import { Footer } from '../../../components/layout';
import CourseLayoutDark from '../../../components/layout/CourseLayoutDark';

const SystemDesignLayout = ({ children, isSidebarOpen, toggleSidebar }) => {
  return (
    <CourseLayoutDark 
      isSidebarOpen={isSidebarOpen} 
      toggleSidebar={toggleSidebar}
      title="System Design"
    >
      <style dangerouslySetInnerHTML={{ __html: `
        @keyframes slideInRight {
          from { transform: translateX(50px); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes fadeUpContent {
          from { transform: translateY(10px); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }
        .content-animate {
          animation: slideInRight 0.3s ease-out forwards;
        }
        .content-fade-up {
          animation: fadeUpContent 0.3s ease-out forwards;
        }
      `}} />
      
      {/* Main Content */}
      <main className="flex-1 relative z-10 content-fade-up">
        {children}
      </main>
      
      {/* Footer */}
      <Footer />
    </CourseLayoutDark>
  );
};

export default SystemDesignLayout;