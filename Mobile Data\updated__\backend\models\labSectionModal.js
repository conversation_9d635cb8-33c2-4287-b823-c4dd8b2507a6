import mongoose from "mongoose";

const labSectionSchema = new mongoose.Schema(
  {
    lab: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Lab",
      required: true,
    },

    title: {
      type: String,
      required: [true, "Section title is required"],
      trim: true,
      maxlength: 100,
    },

    slug: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      match: /^[a-z0-9-]+$/, 
    },

    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },

    icon: {
      public_id: { type: String },
      secure_url: { type: String },
    },

    order: {
      type: Number,
      default: 0,
      min: 0,
      index: true, 
    },

    isVisible: {
      type: Boolean,
      default: true,
    },

    meta: {
      createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
      lastEditedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    },

    isPremiumOnly: {
      type: Boolean,
      default: false,
    },

    visibilityScope: {
      type: String,
      enum: ["public", "private", "enrolled_only"],
      default: "public",
    },

    deletedAt: {
      type: Date,
      default: null,
      select: false, 
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      versionKey: false,
      transform: function (doc, ret) {
        delete ret._id;
        delete ret.deletedAt;
        return ret;
      },
    },
    toObject: { virtuals: true },
  }
);

labSectionSchema.index({ lab: 1 }, { unique: true });

const LabSection = mongoose.model("LabSection", labSectionSchema);
export default LabSection;
