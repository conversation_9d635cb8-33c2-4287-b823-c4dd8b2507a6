import express from "express";
import Question from "../models/Question.js";

const router = express.Router();

router.post("/", async (req, res) => {
    // console.log("Request received to add question");
    // console.log("Request Body:", req.body); 
  
    try {
      const { title, description, difficulty, tags } = req.body;
  
      if (!tags || typeof tags !== "string") {
        return res.status(400).json({ error: "Tags must be a comma-separated string" });
      }
  
      const newQuestion = new Question({
        title,
        description,
        difficulty,
        tags: tags.split(",").map(tag => tag.trim()), // Convert string to array
      });
  
      await newQuestion.save();
      res.status(201).json(newQuestion);
    } catch (error) {
      console.error("Error saving question:", error);
      res.status(500).json({ error: error.message });
    }
  });
  

// Get all questions
router.get("/", async (req, res) => {
  try {
    console.log("req to fetch ques")
    const questions = await Question.find().sort({ createdAt: -1 });
    res.status(200).json(questions);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get a single question by ID
router.get("/:id", async (req, res) => {
  try {
    const question = await Question.findById(req.params.id);
    if (!question) return res.status(404).json({ error: "Question not found" });
    res.status(200).json(question);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Update a question
router.put("/:id", async (req, res) => {
  console.log("req received");
  try {
    const { title, description, difficulty, tags } = req.body;
    

    const formattedTags = Array.isArray(tags) ? tags.map(tag => tag.trim()) : tags.split(",").map(tag => tag.trim());

    const updatedQuestion = await Question.findByIdAndUpdate(
      req.params.id,
      { title, description, difficulty, tags: formattedTags },
      { new: true }
    );

    res.status(200).json(updatedQuestion);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});


// Delete a question
router.delete("/:id", async (req, res) => {
  try {
    console.log("delete req recieved")
    await Question.findByIdAndDelete(req.params.id);
    res.status(200).json({ message: "Question deleted successfully" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;