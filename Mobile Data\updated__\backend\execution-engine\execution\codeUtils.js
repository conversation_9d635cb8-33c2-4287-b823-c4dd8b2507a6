import fs from "fs/promises";
import path from "path";
import { randomUUID } from "crypto";

const TEMP_DIR = path.resolve("temp");

const getFileExtension = (language) => {
  switch (language) {
    case "cpp":
      return "cpp";
    case "python":
      return "py";
    case "java":
      return "java";
    case "javascript":
      return "js";
    default:
      throw new Error("Unsupported language");
  }
};

export const writeTempFile = async (code, language) => {
  try {
    const ext = getFileExtension(language);
    const fileName = `${randomUUID()}.${ext}`;
    const filePath = path.join(TEMP_DIR, fileName);

    await fs.mkdir(TEMP_DIR, { recursive: true }); 
    await fs.writeFile(filePath, code);

    return filePath;
  } catch (error) {
    console.error(error.message);
    throw new Error("Internal Error !!");
  }
};

export const deleteTempFile = async (filePath) => {
  try {
    await fs.unlink(filePath);
  } catch (err) {
    console.log(err.message)
    console.warn("File Not Found", filePath);
  }
};
