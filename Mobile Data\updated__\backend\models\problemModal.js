import mongoose from "mongoose";

const LANGUAGE_OPTIONS = {
  DSA: ["java", "javascript", "python", "cpp"],
  Frontend: ["html", "css", "js", "reactjs", "vuejs", "angularjs"],
  Backend: ["nodejs", "django", "spring boot"],
  Database: ["mysql", "mongodb", "postgresql"],
};

const problemSchema = new mongoose.Schema(
  {
    lab: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Lab",
      required: true,
    },
    labSection: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LabSection",
      required: true,
      index: true,
    },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Category",
      required: true,
    },
    title: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      unique: true,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    inputDescription: String,
    outputDescription: String,
    constraints: String,
    examples: [
      {
        input: String,
        output: String,
        explanation: String,
      },
    ],
    starterCode: {
      type: Map,
      of: String,
    },
    solutionCode: {
      type: Map,
      of: String,
    },
    mainCodeTemplate: {
      type: Map,
      of: String,
    },
    allowedOptions: {
      type: [String],
    },
    difficulty: {
      type: String,
      enum: ["easy", "medium", "hard"],
      required: true,
    },
    timeLimit: {
      type: Number,
      default: 1000,
    },
    memoryLimit: {
      type: Number,
      default: 256,
    },
    testCases: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "TestCase",
      },
    ],
    tags: [String],
    isActive: {
      type: Boolean,
      default: true,
    },
    isPremium: {
      type: Boolean,
      default: false,
    },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  { timestamps: true }
);

problemSchema.pre("save", function (next) {
  if (!this.slug && this.title) {
    this.slug = this.title.toLowerCase().replace(/\s+/g, "-");
  }
  next();
});

problemSchema.pre("save", async function (next) {
  if (this.isModified("lab") || this.isNew) {
    await this.populate("lab");

    const labType = this.lab.labType;

    if (labType && LANGUAGE_OPTIONS[labType]) {
      this.allowedOptions = LANGUAGE_OPTIONS[labType];
    } else {
      this.allowedOptions = [];
    }
  }

  next();
});

const Problem = mongoose.model("Problem", problemSchema);
export default Problem;
