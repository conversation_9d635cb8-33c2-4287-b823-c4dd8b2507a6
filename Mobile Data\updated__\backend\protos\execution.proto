syntax = "proto3";

package executor;

// The service definition
service Executor {
  // Sends code to be executed
  rpc ExecuteCode (ExecutionRequest) returns (ExecutionResponse);
}

// Request message
message ExecutionRequest {
  string code = 1;
  string language = 2;
  string input = 3;
  int32 timeout = 4; // in milliseconds
}

// Response message
message ExecutionResponse {
  string output = 1;
  string error = 2;
  int32 time = 3;    // execution time in ms
  int32 memory = 4;  // memory usage in KB (optional for now)
}
