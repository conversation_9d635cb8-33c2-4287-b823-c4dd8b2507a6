import express from "express";
import { isAuthenticated } from "../middleware/auth.js";
import {
  createContent,
  deleteContent,
  getAllContents,
  getContentById,
  getRelatedContent,
  incrementView,
  searchContent,
  toggleBookmark,
  toggleLike,
  updateContent,
  updateContentVersion,
} from "../controllers/content.js";
import { upload } from "../utils/multerConfig.js";

const contentRouter = express.Router();

contentRouter.post(
  "/create",
  isAuthenticated,
  upload.fields([
    { name: "thumbnail", maxCount: 1 },
    { name: "attachments", maxCount: 5 },
  ]),
  createContent
);

contentRouter.get("/search-content", isAuthenticated, searchContent);

contentRouter.get("/all", isAuthenticated, getAllContents);


contentRouter.put(
  "/update/:contentId",
  isAuthenticated,
  upload.fields([
    { name: "thumbnail", maxCount: 1 },
    { name: "attachments", maxCount: 5 },
  ]),
  updateContent
);


contentRouter.get("/:id", isAuthenticated, getContentById);

contentRouter.patch("/:id", isAuthenticated, deleteContent);

contentRouter.patch("/increment/:id", isAuthenticated, incrementView);

contentRouter.patch("/like/:id", isAuthenticated, toggleLike);

contentRouter.patch("/bookmark/:id", isAuthenticated, toggleBookmark);

contentRouter.patch("/version/:id", isAuthenticated, updateContentVersion);

contentRouter.patch("/related-content/:id", isAuthenticated, getRelatedContent);

export default contentRouter;
