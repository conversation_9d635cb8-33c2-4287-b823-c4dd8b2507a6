import path from "path";
import fs from "fs";
import os from "os";
import crypto from "crypto";
import containerPool from "../execution/containerPool.js";

const writeTempFile = (content, extension = ".java") => {
  let filename;

  if (/public\s+class\s+Main/.test(content)) {
    filename = "Main.java";
  } else {
    filename = `Main${crypto.randomUUID().replace(/-/g, "")}${extension}`;
  }

  const fullPath = path.join(os.tmpdir(), filename);
  fs.writeFileSync(fullPath, content, "utf-8");
  return fullPath;
};

async function streamFile(container, hostPath, destName) {
  const data = fs.readFileSync(hostPath);

  const exec = await container.exec({
    Cmd: ["sh", "-c", `tee /app/${destName} > /dev/null`],
    AttachStdin: true,
    AttachStdout: true,
    AttachStderr: true,
  });

  const stream = await exec.start({ hijack: true, stdin: true });

  await new Promise((resolve, reject) => {
    stream.on("error", reject);
    stream.on("close", resolve);
    stream.on("end", resolve);
    stream.write(data, (err) => {
      if (err) return reject(err);
      stream.end();
    });
  });

  await new Promise((res) => setTimeout(res, 100));

  const inspect = await exec.inspect();

  if (inspect.ExitCode !== 0) {
    throw new Error(
      `File streaming failed at /app/${destName}, exit code: ${inspect.ExitCode}`
    );
  }
}

// Java executor
export default {
  execute: async (codeContent, input = "", { timeout = 5000 } = {}) => {
    const codePath = writeTempFile(codeContent, ".java");
    const inputPath = input ? writeTempFile(input, ".txt", "input.txt") : null;
    const javaFileName = path.basename(codePath);
    const className = javaFileName.replace(".java", "");

    let containerObj;

    try {
      containerObj = await containerPool.getContainer("java");
      const c = containerObj.container;

      await streamFile(c, codePath, javaFileName);
      if (inputPath) {
        await streamFile(c, inputPath, "input.txt");
      }

      const cmd = inputPath
        ? `javac /app/${javaFileName} && java -cp /app ${className} < /app/input.txt`
        : `javac /app/${javaFileName} && java -cp /app ${className}`;

      const exec = await c.exec({
        Cmd: ["sh", "-c", cmd],
        AttachStdout: true,
        AttachStderr: true,
      });

      const stream = await exec.start({ hijack: true, stdin: false });

      let out = "",
        err = "";

      await new Promise((resolve, reject) => {
        c.modem.demuxStream(
          stream,
          {
            write: (chunk) => {
              out += chunk.toString();
            },
          },
          {
            write: (chunk) => {
              err += chunk.toString();
            },
          }
        );
        stream.on("end", resolve);
        stream.on("error", reject);
      });

      return {
        output: out.trim(),
        error: err.trim() || null,
      };
    } catch (e) {
      return {
        output: "",
        error: e.message,
      };
    } finally {
      try {
        if (fs.existsSync(codePath)) fs.unlinkSync(codePath);
        if (inputPath && fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
      } catch {}
      if (containerObj) containerPool.releaseContainer(containerObj);
    }
  },
};
