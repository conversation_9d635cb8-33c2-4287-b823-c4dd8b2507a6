import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Star, 
  Settings, 
  Play, 
  Send, 
  RotateCcw, 
  Maximize2, 
  Sun, 
  Moon,
  Eye,
  EyeOff,
  Bookmark,
  BookmarkCheck
} from 'lucide-react';
import ProblemDescription from './LeetCodePlatform/ProblemDescription';
import CodeEditorSection from './LeetCodePlatform/CodeEditorSection';
import SubmissionsPanel from './LeetCodePlatform/SubmissionsPanel';
import EditorialPanel from './LeetCodePlatform/EditorialPanel';
import DiscussionPanel from './LeetCodePlatform/DiscussionPanel';

const LeetCodePlatform = () => {
  // Theme and UI state
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showFigmaDesign, setShowFigmaDesign] = useState(false);
  
  // Problem and editor state
  const [selectedLanguage, setSelectedLanguage] = useState('javascript');
  const [code, setCode] = useState('');
  const [activeTab, setActiveTab] = useState('description');
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isCodeEditorVisible, setIsCodeEditorVisible] = useState(true);
  
  // Timer state
  const [timer, setTimer] = useState(0);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  
  // Output and execution state
  const [output, setOutput] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample problem data
  const currentProblem = {
    id: 1,
    title: "Two Sum",
    difficulty: "Easy",
    description: "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.",
    examples: [
      {
        input: "nums = [2,7,11,15], target = 9",
        output: "[0,1]",
        explanation: "Because nums[0] + nums[1] = 2 + 7 = 9, we return [0, 1]."
      },
      {
        input: "nums = [3,2,4], target = 6", 
        output: "[1,2]",
        explanation: "Because nums[1] + nums[2] = 2 + 4 = 6, we return [1, 2]."
      }
    ],
    constraints: [
      "2 ≤ nums.length ≤ 10⁴",
      "-10⁹ ≤ nums[i] ≤ 10⁹", 
      "-10⁹ ≤ target ≤ 10⁹",
      "Only one valid answer exists."
    ],
    starterCode: {
      javascript: `/**
 * @param {number[]} nums
 * @param {number} target
 * @return {number[]}
 */
var twoSum = function(nums, target) {
    // Write your solution here
    
};`,
      python: `def two_sum(nums, target):
    """
    :type nums: List[int]
    :type target: int
    :rtype: List[int]
    """
    # Write your solution here
    pass`,
      java: `class Solution {
    public int[] twoSum(int[] nums, int target) {
        // Write your solution here
        
    }
}`,
      cpp: `class Solution {
public:
    vector<int> twoSum(vector<int>& nums, int target) {
        // Write your solution here
        
    }
};`
    }
  };

  // Timer effect
  useEffect(() => {
    let interval;
    if (isTimerRunning) {
      interval = setInterval(() => {
        setTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning]);

  // Initialize code when language changes
  useEffect(() => {
    setCode(currentProblem.starterCode[selectedLanguage] || '');
  }, [selectedLanguage]);

  // Format timer display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle code execution
  const handleRunCode = async () => {
    setIsRunning(true);
    setOutput('Running code...\n');
    
    // Simulate code execution
    setTimeout(() => {
      setOutput(`✅ Code executed successfully!
      
Test Case 1: 
Input: nums = [2,7,11,15], target = 9
Expected: [0,1]
Output: [0,1] ✓

Test Case 2:
Input: nums = [3,2,4], target = 6  
Expected: [1,2]
Output: [1,2] ✓

Runtime: 64 ms
Memory: 15.3 MB`);
      setIsRunning(false);
    }, 2000);
  };

  // Handle code submission
  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    // Simulate submission
    setTimeout(() => {
      setOutput(`🎉 Accepted!

Runtime: 64 ms, faster than 85.2% of JavaScript submissions.
Memory Usage: 15.3 MB, less than 76.8% of JavaScript submissions.

Test cases passed: 57/57`);
      setIsSubmitting(false);
    }, 3000);
  };

  // Reset code to starter template
  const handleReset = () => {
    setCode(currentProblem.starterCode[selectedLanguage] || '');
    setOutput('');
  };

  // Clear console output
  const handleClearOutput = () => {
    setOutput('');
  };

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Toggle timer
  const toggleTimer = () => {
    setIsTimerRunning(!isTimerRunning);
  };

  // Toggle bookmark
  const toggleBookmark = () => {
    setIsBookmarked(!isBookmarked);
  };

  const tabs = [
    { id: 'description', label: 'Description', icon: '📝' },
    { id: 'submissions', label: 'Submissions', icon: '📥' },
    { id: 'editorial', label: 'Editorial', icon: '🧠' },
    { id: 'discussion', label: 'Discussion', icon: '💬' }
  ];

  const languages = [
    { value: 'javascript', label: 'JavaScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'cpp', label: 'C++' }
  ];

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'} transition-colors duration-300`}>
      {/* Header */}
      <div className={`border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'} px-4 py-3`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold">LeetCode Platform</h1>
            
            {/* Timer */}
            <div className="flex items-center gap-2">
              <Clock size={16} />
              <span className="font-mono text-sm">{formatTime(timer)}</span>
              <button
                onClick={toggleTimer}
                className={`px-2 py-1 text-xs rounded ${
                  isTimerRunning 
                    ? 'bg-red-500 text-white' 
                    : 'bg-green-500 text-white'
                }`}
              >
                {isTimerRunning ? 'Pause' : 'Start'}
              </button>
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* Bookmark */}
            <button
              onClick={toggleBookmark}
              className={`p-2 rounded-lg transition-colors ${
                isBookmarked 
                  ? 'text-yellow-500 hover:text-yellow-600' 
                  : isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-600 hover:text-gray-700'
              }`}
            >
              {isBookmarked ? <BookmarkCheck size={20} /> : <Bookmark size={20} />}
            </button>

            {/* Figma Design Toggle */}
            <button
              onClick={() => setShowFigmaDesign(!showFigmaDesign)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                showFigmaDesign
                  ? 'bg-purple-600 text-white'
                  : isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {showFigmaDesign ? <EyeOff size={16} /> : <Eye size={16} />}
              <span className="ml-2">{showFigmaDesign ? 'Hide' : 'Show'} Design</span>
            </button>

            {/* Code Editor Toggle */}
            <button
              onClick={() => setIsCodeEditorVisible(!isCodeEditorVisible)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                isCodeEditorVisible
                  ? isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              <span className="mr-2">💻</span>
              <span>{isCodeEditorVisible ? 'Hide' : 'Show'} Code Editor</span>
            </button>

            {/* Theme Toggle */}
            <button
              onClick={() => setIsDarkMode(!isDarkMode)}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'text-gray-400 hover:text-gray-300' 
                  : 'text-gray-600 hover:text-gray-700'
              }`}
            >
              {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
            </button>

            {/* Fullscreen Toggle */}
            <button
              onClick={toggleFullscreen}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'text-gray-400 hover:text-gray-300' 
                  : 'text-gray-600 hover:text-gray-700'
              }`}
            >
              <Maximize2 size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Figma Design Overlay */}
      {showFigmaDesign && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center"
          onClick={() => setShowFigmaDesign(false)}
        >
          <div className="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Figma Design Preview</h3>
            <div className="bg-gray-100 rounded-lg h-64 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-2">🎨</div>
                <p>Figma design would be embedded here</p>
                <p className="text-sm mt-2">Interactive UI mockup preview</p>
              </div>
            </div>
            <button
              onClick={() => setShowFigmaDesign(false)}
              className="mt-4 w-full bg-gray-900 text-white py-2 rounded-lg hover:bg-gray-800 transition-colors"
            >
              Close Preview
            </button>
          </div>
        </motion.div>
      )}

      {/* Main Content */}
      <div className={`flex flex-col ${isFullscreen ? 'h-screen' : 'h-[calc(100vh-73px)]'}`}>
        {/* Top Section - Problem Info (Dynamic height based on code editor visibility) */}
        <div className={`${
          isCodeEditorVisible
            ? (isFullscreen ? 'h-2/5' : 'h-2/5')
            : 'flex-1'
        } ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} ${
          isCodeEditorVisible ? 'border-b' : ''
        } overflow-hidden`}>
          <div className="h-full flex flex-col">
            {/* Tab Navigation */}
            <div className={`flex justify-between items-center border-b ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}>
              <div className="flex">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`px-4 py-3 text-sm font-medium transition-colors ${
                      activeTab === tab.id
                        ? isDarkMode
                          ? 'text-blue-400 border-b-2 border-blue-400 bg-gray-900'
                          : 'text-blue-600 border-b-2 border-blue-600 bg-white'
                        : isDarkMode
                          ? 'text-gray-400 hover:text-gray-300'
                          : 'text-gray-600 hover:text-gray-700'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Code Editor Status Indicator */}
              {!isCodeEditorVisible && (
                <div className="flex items-center gap-2 px-4">
                  <div className={`w-2 h-2 rounded-full bg-orange-500 animate-pulse`}></div>
                  <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    Code Editor Hidden
                  </span>
                </div>
              )}
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-auto">
              {activeTab === 'description' && (
                <ProblemDescription problem={currentProblem} isDarkMode={isDarkMode} />
              )}
              {activeTab === 'submissions' && (
                <SubmissionsPanel isDarkMode={isDarkMode} />
              )}
              {activeTab === 'editorial' && (
                <EditorialPanel isDarkMode={isDarkMode} />
              )}
              {activeTab === 'discussion' && (
                <DiscussionPanel isDarkMode={isDarkMode} />
              )}
            </div>
          </div>
        </div>

        {/* Bottom Section - Code Editor (60% height when visible) */}
        {isCodeEditorVisible && (
          <div className="flex-1 flex flex-col">
            <CodeEditorSection
              code={code}
              setCode={setCode}
              selectedLanguage={selectedLanguage}
              setSelectedLanguage={setSelectedLanguage}
              languages={languages}
              output={output}
              isRunning={isRunning}
              isSubmitting={isSubmitting}
              onRunCode={handleRunCode}
              onSubmit={handleSubmit}
              onReset={handleReset}
              onClearOutput={handleClearOutput}
              onHideEditor={() => setIsCodeEditorVisible(false)}
              isDarkMode={isDarkMode}
            />
          </div>
        )}

        {/* Bottom Action Bar when Code Editor is Hidden */}
        {!isCodeEditorVisible && (
          <motion.div
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className={`fixed bottom-0 left-0 right-0 z-30 border-t ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            } shadow-lg`}
          >
            <div className="flex items-center justify-between px-6 py-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-lg">💻</span>
                  <span className="font-medium">Ready to code?</span>
                </div>

                {/* Language Selector */}
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className={`px-3 py-2 rounded-lg border text-sm ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                >
                  {languages.map((lang) => (
                    <option key={lang.value} value={lang.value}>
                      {lang.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center gap-3">
                {/* Quick Actions */}
                <button
                  onClick={() => {
                    setIsCodeEditorVisible(true);
                    // Auto-focus on editor after a short delay
                    setTimeout(() => {
                      const editor = document.querySelector('.monaco-editor');
                      if (editor) editor.focus();
                    }, 100);
                  }}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  <span>🚀</span>
                  Start Coding
                </button>

                <button
                  onClick={() => setIsCodeEditorVisible(true)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                    isDarkMode
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  <Eye size={16} />
                  Show Editor
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default LeetCodePlatform;
