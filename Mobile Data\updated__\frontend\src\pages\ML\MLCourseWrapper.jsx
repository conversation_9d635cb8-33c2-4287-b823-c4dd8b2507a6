import React, { useState } from 'react';
import MLLayout from './components/MLLayout';
import MachineLearning from './MachineLearning';
import InternshipApply from "../../components/InternshipApply";

const MLCourseWrapper = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <MLLayout isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar}>
      <MachineLearning />
    </MLLayout>
  );
};

export default MLCourseWrapper;
