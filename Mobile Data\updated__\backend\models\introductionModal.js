import mongoose from "mongoose";

const introductionSchema = new mongoose.Schema(
  {
    lab: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Lab",
      required: true,
    },
    section: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LabSection",
      required: true,
    },

    courseOverview: {
      type: String,
      required: true,
    },

    whyLearning: {
      type: String,
      required: true,
    },

    whatYouWillLearn: {
      type: String,
      required: true,
    },

    keyTopics: [
      {
        type: String,
        required: true,
      },
    ],

    toolsAndTechnologies: [
      {
        name: {
          type: String,
          required: true,
        },
        icon: {
          public_id: {
            type: String,
            default: "",
          },
          secure_url: {
            type: String,
            default: "",
          },
        },
      },
    ],

    conceptAndSkills: {
      description: {
        type: String,
        required: true,
      },
      points: [
        {
          type: String,
          required: true,
        },
      ],
    },

    careerImpact: {
      type: String,
      required: true,
    },

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    lastEditedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
);

const Introduction = mongoose.model("Introduction", introductionSchema);
export default Introduction;
