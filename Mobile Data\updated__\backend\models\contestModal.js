import mongoose from "mongoose";

const contestSchema = new mongoose.Schema(
  {
    name: { type: String, required: true, trim: true },
    description: { type: String },

    startTime: { type: Date, required: true },
    endTime: { type: Date, required: true },
    registrationOpen: { type: Date },
    registrationClose: { type: Date },

    problems: [{ type: mongoose.Schema.Types.ObjectId, ref: "Problem" }],
    participants: [{ type: mongoose.Schema.Types.ObjectId, ref: "User" }],

    allowedLanguages: {
      type: [String],
      enum: ["cpp", "java", "javascript", "python"],
      default: ["cpp", "java", "javascript", "python"],
    },

    maxParticipants: { type: Number },

    isActive: { type: Boolean, default: true },
    leaderboardVisible: { type: Boolean, default: true },

    rules: { type: String },
    prizeDetails: { type: String },

    organizer: { type: mongoose.Schema.Types.ObjectId, ref: "User" },

    bannerImage: {
      public_id: String,
      secure_url: String,
    },

    sponsorDetails: {
      name: String,
      logo: {
        public_id: String,
        secure_url: String,
      },
      website: String,
    },

    status: {
      type: String,
      enum: ["upcoming", "ongoing", "ended", "canceled"],
      default: "upcoming",
    },

    customFields: {
      type: mongoose.Schema.Types.Mixed,
    },
  },
  { timestamps: true }
);

const Contest = mongoose.model("Contest", contestSchema);
export default Contest;
