import React from "react";
import { motion } from "framer-motion";

const PythonNavigation = ({ mainContent, currentContent, isSidebarOpen, toggleSidebar }) => {
  const navItems = [
    { id: "Introduction", icon: "fas fa-book-open", label: "Introduction" },
    { id: "Chapter", icon: "fas fa-book", label: "Chapters" },
    { id: "CaseStudy", icon: "fas fa-laptop-code", label: "Case Studies" },
    { id: "FAQS", icon: "fas fa-question-circle", label: "FAQs" }
  ];

  return (
    <div className={`${isSidebarOpen ? "w-64" : "w-20"} transition-all duration-300 ease-in-out bg-white dark:bg-gray-900 h-full shadow-lg fixed left-0 top-0 pt-20 z-10`}>
      <div className="absolute top-3 right-3">
        <button 
          onClick={toggleSidebar}
          className="text-gray-500 dark:text-gray-400 hover:text-primary focus:outline-none"
        >
          <i className={`fas ${isSidebarOpen ? "fa-chevron-left" : "fa-chevron-right"} text-lg`}></i>
        </button>
      </div>
      
      <nav className="flex flex-col h-full">
        {navItems.map((item) => (
          <motion.button
            key={item.id}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => mainContent(item.id)}
            className={`flex items-center p-4 ${
              currentContent === item.id
                ? "bg-gradient-to-r from-primary to-secondary text-white"
                : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
            } ${
              isSidebarOpen ? "justify-start" : "justify-center"
            } transition-all duration-300 ease-in-out`}
          >
            <i className={`${item.icon} ${isSidebarOpen ? "mr-3" : ""} text-xl`}></i>
            {isSidebarOpen && <span>{item.label}</span>}
          </motion.button>
        ))}
      </nav>
    </div>
  );
};

export default PythonNavigation;
