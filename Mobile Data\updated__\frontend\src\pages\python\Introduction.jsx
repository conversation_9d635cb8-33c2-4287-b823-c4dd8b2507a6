import React, { useState } from "react";
import { motion } from "framer-motion";

const Introduction = ({ showPremiumOverlay }) => {
  const [showMore, setShowMore] = useState(false);

  const toggleReadMore = () => {
    setShowMore(!showMore);
  };

  // CSS styles for animations
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.95); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    @keyframes rotate180 {
      from { transform: rotate(0deg); }
      to { transform: rotate(180deg); }
    }
    
    @keyframes bounce {
      0%, 100% { transform: translateX(0); }
      50% { transform: translateX(5px); }
    }
    
    .section-animate {
      opacity: 0;
      animation: fadeIn 0.8s forwards;
    }
    
    .card-animate {
      opacity: 0;
      animation: scaleIn 0.6s forwards;
    }
    
    .hover-lift {
      transition: transform 0.3s ease;
    }
    .hover-lift:hover {
      transform: translateY(-5px);
    }
    
    .hover-scale {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .hover-scale:hover {
      transform: scale(1.05);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .hover-scale:active {
      transform: scale(0.95);
    }
    
    .arrow-bounce {
      animation: bounce 1.5s infinite;
    }
    
    .delay-1 { animation-delay: 0.1s; }
    .delay-2 { animation-delay: 0.3s; }
    .delay-3 { animation-delay: 0.5s; }
    .delay-4 { animation-delay: 0.7s; }
  `;

  return (
    <div className="py-8">
      {/* Add our CSS animations */}
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Header Section */}
        <div className="text-center mb-16 section-animate delay-1">
          <div
            className="inline-block px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg"
          >
            🐍 Course Introduction
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
            Python Programming{" "}
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Mastery
            </span>
          </h2>
          
          <p className="text-xl text-blue-100/80 max-w-3xl mx-auto leading-relaxed">
            Master Python programming with comprehensive video-based lessons and real-world applications
          </p>
        </div>

        {/* Main Content Card */}
        <div
          className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md p-8 md:p-12 mb-12 card-animate delay-2"
        >
          <div className="space-y-8 section-animate delay-3">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div className="space-y-6">
                <p className="text-lg text-blue-100/90 leading-relaxed">
                  In this comprehensive course, we will guide you through the essential concepts of
                  Python programming using engaging video-based lessons. Gaining a solid grasp of
                  core programming principles is crucial for mastering Python and excelling in
                  technical interviews.
                </p>
                
                <p className="text-blue-100/80 leading-relaxed">
                  Every programming language has its own unique aspects, and more complex programs
                  often deviate from standard coding approaches. However, in Python programming
                  interviews, you're not expected to know every detail. Instead, you'll need to
                  showcase your knowledge of common programming principles and best practices.
                </p>
                
                <p className="text-blue-100/80 leading-relaxed">
                  Beyond just understanding concepts, this course will teach you the thought process
                  behind Python programming. We will explore the key questions to ask during the
                  coding phase and how to assess different approaches.
                </p>
              </div>
              
              <div className="relative">
                <div
                  className="bg-gradient-to-br from-[#1e293b]/20 to-[#0f172a]/20 backdrop-blur-sm p-6 rounded-2xl border border-white/10 hover-scale"
                >
                  <img
                    src="images/image.png"
                    alt="Python Programming"
                    className="w-full h-auto rounded-xl shadow-lg"
                  />
                  <div className="absolute -top-3 -right-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white p-3 rounded-full shadow-lg">
                    <span className="text-2xl">🐍</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Enhanced Features Grid */}
            <div
              className="grid md:grid-cols-3 gap-6 mt-12 section-animate delay-4"
            >
              {[
                {
                  icon: "🎯",
                  title: "Interview Ready",
                  description: "Master the programming concepts most commonly tested in interviews",
                  color: "from-blue-500 to-cyan-500",
                },
                {
                  icon: "🏗️",
                  title: "Real-World Projects",
                  description: "Apply your knowledge with practical examples and case studies",
                  color: "from-purple-500 to-pink-500",
                },
                {
                  icon: "🚀",
                  title: "Career Growth",
                  description: "Build the skills needed for advancing your software development career",
                  color: "from-green-500 to-teal-500",
                },
              ].map((feature, index) => (
                <div
                  key={index}
                  className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm p-6 rounded-2xl border border-white/10 shadow-lg hover:shadow-xl transition-all duration-300 hover-lift"
                >
                  <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center text-white text-2xl mb-4`}>
                    {feature.icon}
                  </div>
                  <h4 className="text-lg font-semibold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>{feature.title}</h4>
                  <p className="text-blue-100/80 text-sm leading-relaxed">{feature.description}</p>
                </div>
              ))}
            </div>

            {/* Expandable Content Section */}
            <div
              
              className="mt-12 text-center"
            >
              <motion.button
                onClick={toggleReadMore}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <span className="mr-2">📖</span>
                {showMore ? "Show Less" : "Read More"}
                <motion.span
                  animate={{ rotate: showMore ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="ml-2"
                >
                  ↓
                </motion.span>
              </motion.button>

              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{
                  height: showMore ? "auto" : 0,
                  opacity: showMore ? 1 : 0,
                }}
                transition={{ duration: 0.5 }}
                className="overflow-hidden"
              >
                {showMore && (
                  <motion.div
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="mt-8 space-y-6 text-left bg-gradient-to-br from-[#1e293b]/20 to-[#0f172a]/20 backdrop-blur-sm p-8 rounded-2xl border border-white/10"
                  >
                    <p className="text-blue-100/90 leading-relaxed">
                      Understanding Python programming involves mastering both theoretical knowledge
                      and practical skills. This course covers various programming paradigms such as
                      object-oriented, functional, and procedural programming. It also explores the
                      trade-offs between these paradigms to help you choose the best approach for
                      different scenarios.
                    </p>
                    
                    <p className="text-blue-100/90 leading-relaxed">
                      We will dive deep into the principles of scalability, reliability, and
                      performance optimization. The course includes real-world case studies that
                      illustrate how these principles are applied in Python applications.
                      Additionally, you'll gain hands-on experience with tools and technologies
                      commonly used in Python programming.
                    </p>
                    
                    <p className="text-blue-100/90 leading-relaxed">
                      By engaging with this course, you will be equipped with the knowledge to write
                      robust and scalable Python code. You'll also have the ability to analyze and
                      critique existing codebases, providing valuable insights into their strengths
                      and weaknesses. This comprehensive approach ensures that you are well-prepared
                      for both interviews and practical applications in your career.
                    </p>
                  </motion.div>
                )}
              </motion.div>
            </div>
          </div>
        </div>

        {/* Call to Action Section */}
        <div
          
          className="text-center"
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.button
              onClick={showPremiumOverlay}
              className="inline-flex items-center px-12 py-6 bg-gradient-to-r from-green-600 to-blue-600 text-white font-bold text-xl rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-300 group"
            >
              <span className="mr-3 text-2xl">🚀</span>
              Start Your Python Journey
              <motion.span
                animate={{ x: [0, 5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
                className="ml-3 group-hover:translate-x-2 transition-transform"
              >
                →
              </motion.span>
            </motion.button>
          </motion.div>
          
          <p className="mt-4 text-blue-100/70 text-sm">
            Join thousands of developers mastering Python programming
          </p>
        </div>
      </div>
    </div>
  );
};

export default Introduction;
