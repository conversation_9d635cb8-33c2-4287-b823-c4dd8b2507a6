import React from "react";
import { motion } from "framer-motion";

const SQLLiveClasses = ({ onBackToCourse, showPremiumOverlay }) => {
  const interviewTopics = [
    {
      title: "SQL Interview Fundamentals",
      description: "Common SQL questions asked in technical interviews",
      date: "Next Session: Tomorrow, 7:00 PM",
      icon: "📊"
    },
    {
      title: "Database Design Principles",
      description: "Normalization, indexing, and schema design",
      date: "Next Session: Wednesday, 6:30 PM",
      icon: "🏗️"
    },
    {
      title: "Advanced SQL Techniques",
      description: "Window functions, CTEs, and performance optimization",
      date: "Next Session: Friday, 7:00 PM",
      icon: "🚀"
    },
    {
      title: "SQL Problem Solving",
      description: "Tackle complex SQL challenges with efficient solutions",
      date: "Next Session: Saturday, 11:00 AM",
      icon: "🧩"
    }
  ];

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-900/70 to-red-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">SQL Interview Preparation</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Ace Your SQL Interviews</h3>
          <p className="text-gray-300">
            Prepare for SQL technical interviews with our specialized sessions. Learn query optimization, 
            database design principles, and practice with mock interviews.
          </p>
        </div>
        
        {/* Interview Topics */}
        <div className="space-y-4">
          {interviewTopics.map((topic, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-start p-4 border border-gray-700/30 bg-gray-800/30 rounded-lg hover:bg-gray-700/40 transition-colors cursor-pointer"
              onClick={showPremiumOverlay}
            >
              <div className="text-3xl mr-4">{topic.icon}</div>
              <div>
                <h4 className="font-medium text-white">{topic.title}</h4>
                <p className="text-sm text-gray-300 mb-1">{topic.description}</p>
                <p className="text-xs text-blue-300 font-medium">{topic.date}</p>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Interview Preparation Resources */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-8 bg-gray-800/30 border border-gray-700/30 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold text-white mb-2">SQL Interview Question Bank</h3>
          <p className="text-gray-300 mb-4">
            Access our curated collection of SQL interview questions from top tech companies.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
            <div className="bg-gray-900/50 border border-gray-700/30 p-3 rounded-lg">
              <h4 className="font-medium text-white text-sm mb-1">Joins & Relationships</h4>
              <p className="text-xs text-gray-400">15 questions</p>
            </div>
            <div className="bg-gray-900/50 border border-gray-700/30 p-3 rounded-lg">
              <h4 className="font-medium text-white text-sm mb-1">Aggregation Functions</h4>
              <p className="text-xs text-gray-400">12 questions</p>
            </div>
            <div className="bg-gray-900/50 border border-gray-700/30 p-3 rounded-lg">
              <h4 className="font-medium text-white text-sm mb-1">Subqueries & CTEs</h4>
              <p className="text-xs text-gray-400">10 questions</p>
            </div>
            <div className="bg-gray-900/50 border border-gray-700/30 p-3 rounded-lg">
              <h4 className="font-medium text-white text-sm mb-1">Window Functions</h4>
              <p className="text-xs text-gray-400">8 questions</p>
            </div>
          </div>
          <button 
            onClick={showPremiumOverlay}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            Access Question Bank
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default SQLLiveClasses;