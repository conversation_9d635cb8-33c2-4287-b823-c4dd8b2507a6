import React from 'react';
import { Download, RotateCcw, Play, Globe } from 'lucide-react';

const ActionButtons = ({
  files,
  activeFile,
  onExportProject,
  onResetProject,
  onRunProject,
  onDeployProject,
  isDarkMode
}) => {
  const handleExportProject = () => {
    const projectData = {
      files,
      activeFile,
      timestamp: new Date().toISOString()
    };
    const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'upcoding-project.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    if (onExportProject) onExportProject();
  };

  const handleResetProject = () => {
    if (confirm('Reset all files to default? This cannot be undone.')) {
      if (onResetProject) onResetProject();
    }
  };

  const handleDeployProject = () => {
    alert('🚀 Project deployed successfully!\n\nYour project is now live at:\nhttps://your-project.upcoding.dev');
    if (onDeployProject) onDeployProject();
  };

  return (
    <div className={`flex items-center justify-between px-4 py-3 border-t ${
      isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
    }`}>
      <div className="flex items-center gap-3">
        {/* Project Actions */}
        <button
          onClick={handleExportProject}
          className={`flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors ${
            isDarkMode
              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <Download size={16} />
          Export Project
        </button>

        {/* Reset Button */}
        <button
          onClick={handleResetProject}
          className={`flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors ${
            isDarkMode
              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <RotateCcw size={16} />
          Reset Project
        </button>
      </div>

      <div className="flex items-center gap-3">
        {/* Run Project */}
        <button
          onClick={onRunProject}
          className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium bg-green-600 text-white hover:bg-green-700 transition-colors"
        >
          <Play size={16} />
          Run Project
        </button>

        {/* Deploy Simulation */}
        <button
          onClick={handleDeployProject}
          className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors"
        >
          <Globe size={16} />
          Deploy
        </button>
      </div>
    </div>
  );
};

export default ActionButtons;
