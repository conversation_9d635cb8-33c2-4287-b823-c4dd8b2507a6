import { configureStore } from "@reduxjs/toolkit";
import authSlice from "./authSlice";
import labSectionReducer from "./labsectionSlice";
import labReducer from "./labSlice";

const store = configureStore({
  reducer: {
    auth: authSlice,
    lab: labReducer,
    labSection: labSectionReducer,
  },
  //   middleware: (getDefaultMiddleware) =>
  //     getDefaultMiddleware().concat(userSlice.middleware).concat(adminApi.middleware),
});

// const initializeApp = async()=>{
//     await store.dispatch(userSlice.endpoints.loadUser.initiate({},{forceRefetch:true}));
// }

// initializeApp();

export default store;
