import { useEffect, useState } from "react";
import axiosInstance from "../../../utils/axiosInstance";
import { useDispatch, useSelector } from "react-redux";

const LabSection = ({ theme }) => {
  const [entries, setEntries] = useState([]);
  const dispatch = useDispatch();
  const labs = useSelector((state) => state.lab.labs);
  console.log(labs, "kkkkkkkkkk");

  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  //   const [labs, setLabs] = useState([]);

  const [formData, setFormData] = useState({
    _id: null,
    labId: "",
    title: "",
    description: "",
    image: null,
    slug: "",
    isVisible: "true",
    isPremium: "false",
    visibility: "",
  });

  const inputClass = `w-full mt-1 border rounded px-3 py-2 ${
    theme === "dark"
      ? "bg-gray-700 text-white border-gray-600 placeholder-gray-300"
      : "bg-white text-gray-900 border-gray-300 placeholder-gray-500"
  }`;

  useEffect(() => {
    const fetchLabSection = async () => {
      if (!formData.labId) {
        setEntries([]);
        return;
      }
      try {
        const res = await axiosInstance.get(
          `http://localhost:8000/api/v1/labsection/all/${formData.labId}`
        );
        dispatch(setLabSection(res.data.entries));
        setEntries(Array.isArray(res.data.entries) ? res.data.entries : []);
        // entries(Array.isArray(res.data.sections) ? res.data.sections : []);
      } catch (error) {
        console.error("Failed fetching lab sections", error);
      }
    };
    fetchLabSection();
  }, [formData.labId]);

  const handleChange = (e) => {
    const { name, value, type, files } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "file" ? files[0] : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (!formData.labId || !formData.title) {
        alert("Please select a lab and provide a title.");
        return;
      }

      const formPayload = new FormData();
      formPayload.append("lab", formData.labId);
      formPayload.append("title", formData.title);
      formPayload.append("description", formData.description);
      formPayload.append("slug", formData.slug);
      formPayload.append("isVisible", formData.isVisible === "true");
      formPayload.append("isPremium", formData.isPremium === "true");
      formPayload.append("visibility", formData.visibility);

      if (formData.image instanceof File) {
        formPayload.append("icon", formData.image);
      }

      let res;
      if (isEditing) {
        if (!formData._id) {
          alert("Invalid section ID for update.");
          return;
        }

        res = await axiosInstance.put(
          `http://localhost:8000/api/v1/labsection/update/${formData._id}`,
          formPayload,
          { headers: { "Content-Type": "multipart/form-data" } }
        );
        alert("Lab Section updated successfully!");

        entries((prevEntries) =>
          prevEntries.map((entry) =>
            entry._id === formData._id ? res.data.section : entry
          )
        );
      } else {
        res = await axiosInstance.post(
          "http://localhost:8000/api/v1/labsection/create",
          formPayload,
          { headers: { "Content-Type": "multipart/form-data" } }
        );
        alert("Lab Section created successfully!");
        entries((prev) => [...prev, res.data.section]);
      }

      setFormData({
        _id: null,
        labId: "",
        title: "",
        description: "",
        image: null,
        slug: "",
        isVisible: "true",
        isPremium: "false",
        visibility: "",
      });
      setIsEditing(false);
      setEditIndex(null);
    } catch (err) {
      console.error("Error:", err);
      alert("Failed to submit lab section data.");
    }
  };

  const handleEdit = (index) => {
    const entryToEdit = entries[index];
    setFormData({
      ...entryToEdit,
      labId:
        typeof entryToEdit.lab === "object"
          ? entryToEdit.lab._id || ""
          : entryToEdit.lab || "",
      _id: entryToEdit._id,
      image: null,
      isVisible: entryToEdit.isVisible?.toString() || "true",
      isPremium: entryToEdit.isPremium?.toString() || "false",
    });
    setIsEditing(true);
    setEditIndex(index);
  };

  const handleDelete = async (index) => {
    const entryToDelete = entries[index];
    if (!entryToDelete?._id) return alert("Invalid section ID for deletion.");
    if (!window.confirm("Are you sure you want to delete this section?"))
      return;

    try {
      await axiosInstance.delete(
        `http://localhost:8000/api/v1/labsection/delete/${entryToDelete._id}`
      );
      alert("Lab Section deleted successfully!");
      setEntries((prev) =>
        prev.filter((entry) => entry._id !== entryToDelete._id)
      );

      if (isEditing && editIndex === index) {
        setIsEditing(false);
        setEditIndex(null);
        setFormData({
          _id: null,
          labId: "",
          title: "",
          description: "",
          image: null,
          slug: "",
          isVisible: "true",
          isPremium: "false",
          visibility: "",
        });
      }
    } catch (error) {
      console.error("Failed to delete lab section:", error);
      alert("Failed to delete lab section. Try again.");
    }
  };

  return (
    <div
      className={`p-6 rounded shadow-md mb-8 ${
        theme === "dark" ? "bg-gray-800 text-white" : "bg-white text-gray-900"
      }`}
    >
      <form
        onSubmit={handleSubmit}
        className="max-w-7xl mx-auto p-6 rounded-lg shadow-md space-y-4"
      >
        <h2 className="text-xl font-semibold mb-4">Lab Section Form</h2>

        <div>
          <label className="block mb-1">Lab</label>
          <select
            name="labId"
            value={formData.labId}
            onChange={handleChange}
            className={inputClass}
            required
          >
            <option value="">Select a Lab</option>
            {labs.map((lab) => (
              <option key={lab._id} value={lab._id}>
                {lab.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block mb-1">Title</label>
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleChange}
            className={inputClass}
            required
          />
        </div>

        <div>
          <label className="block mb-1">Description</label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            className={inputClass}
            rows="4"
          />
        </div>

        <div>
          <label className="block mb-1">Image (Icon)</label>
          <input
            type="file"
            name="image"
            onChange={handleChange}
            accept="image/*"
            className={inputClass}
          />
          {formData.image instanceof File && (
            <img
              src={URL.createObjectURL(formData.image)}
              alt="Preview"
              className="h-20 w-20 mt-2 rounded object-contain"
            />
          )}
          {!formData.image &&
            isEditing &&
            entries[editIndex]?.icon?.secure_url && (
              <img
                src={entries[editIndex].icon.secure_url}
                alt="Existing Icon"
                className="h-20 w-20 mt-2 rounded object-contain"
              />
            )}
        </div>

        <div>
          <label className="block mb-1">Slug</label>
          <input
            type="text"
            name="slug"
            value={formData.slug || ""}
            onChange={handleChange}
            className={inputClass}
            placeholder="Optional"
          />
        </div>

        <div>
          <label className="block mb-1">Is Visible</label>
          <select
            name="isVisible"
            value={formData.isVisible}
            onChange={handleChange}
            className={inputClass}
          >
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
        </div>

        <div>
          <label className="block mb-1">Is Premium</label>
          <select
            name="isPremium"
            value={formData.isPremium}
            onChange={handleChange}
            className={inputClass}
          >
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
        </div>

        <div>
          <label className="block mb-1">Visibility</label>
          <input
            type="text"
            name="visibility"
            value={formData.visibility}
            onChange={handleChange}
            className={inputClass}
            placeholder="Optional visibility info"
          />
        </div>

        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          {isEditing ? "Update" : "Submit"}
        </button>
      </form>

      <div className="mt-10">
        <h3 className="text-lg font-semibold mb-4">Submitted Lab Sections</h3>
        {entries.length === 0 ? (
          <p className="text-gray-500">No lab sections available.</p>
        ) : (
          <div className="overflow-auto">
            <table className="min-w-full table-auto border border-gray-300">
              <thead
                className={
                  theme === "dark"
                    ? "bg-gray-700 text-white"
                    : "bg-gray-100 text-black"
                }
              >
                <tr>
                  <th className="border px-4 py-2">Lab ID</th>
                  <th className="border px-4 py-2">Title</th>
                  <th className="border px-4 py-2">Description</th>
                  <th className="border px-4 py-2">Slug</th>
                  <th className="border px-4 py-2">Visible</th>
                  <th className="border px-4 py-2">Premium</th>
                  <th className="border px-4 py-2">Visibility</th>
                  <th className="border px-4 py-2">Image</th>
                  <th className="border px-4 py-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {entries.map((entry, index) => {
                  return (
                    <tr key={entry._id || index}>
                      <td className="border px-4 py-2">{entry._id}</td>
                      <td className="border px-4 py-2">{entry.title}</td>
                      <td className="border px-4 py-2">{entry.description}</td>
                      <td className="border px-4 py-2">{entry.slug}</td>
                      <td className="border px-4 py-2">
                        {entry.isVisible ? "Yes" : "No"}
                      </td>
                      <td className="border px-4 py-2">
                        {entry.isPremium ? "Yes" : "No"}
                      </td>
                      <td className="border px-4 py-2">{entry.visibility}</td>
                      <td className="border px-4 py-2">
                        {entry.icon?.secure_url ? (
                          <img
                            src={entry.icon.secure_url}
                            alt="Uploaded"
                            className="h-12 w-12 object-cover rounded"
                          />
                        ) : (
                          "No Image"
                        )}
                      </td>
                      <td className="border px-4 py-2 space-x-2">
                        <button
                          onClick={() => handleEdit(index)}
                          className="text-blue-600 hover:underline"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(index)}
                          className="text-red-600 hover:underline"
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default LabSection;
