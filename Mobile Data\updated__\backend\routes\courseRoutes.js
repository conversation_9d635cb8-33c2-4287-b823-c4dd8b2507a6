import express from "express";
import {
  createCourse,
  getAllCourses,
  //   getCourseBySlug,
  updateCourse,
  deleteCourse,
} from "../controllers/coarseController.js";

const router = express.Router();

router.post("/", createCourse); // Create a course
router.get("/", getAllCourses); // Get all courses
// router.get("/courses/:slug", getCourseBySlug); // Get a course by slug
router.put("/:slug", updateCourse); // Update a course by slug
router.delete("/:slug", deleteCourse); // Delete a course by slug

export default router;
