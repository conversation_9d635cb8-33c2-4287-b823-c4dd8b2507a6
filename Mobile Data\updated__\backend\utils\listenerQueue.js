// import { executeCode } from "../execution-engine/execution/executor.js";
// import { updateJobData } from "../utils/rabbitMQ.js";

// const processJob = async (job) => {
//   try {
//     console.log(`New Job Received ${job.id}`);

//     if (!job.data || !job.data.code) {
//       console.error("Job code missing in message");
//       return;
//     }

//     const { code, language, input: rawInput } = job.data;
//     const timeout = job.timeout;

//     // Normalize input
//     let lines = (rawInput || "").trim().split("\n");
//     console.log("Raw input lines", lines);

//     if (lines.length > 2) {
//       lines = lines.slice(0, 2);
//       console.log("Trimmed input lines to two", lines);
//     }

//     if (lines.length === 1) {
//       const parts = lines[0].trim().split(/\s+/);
//       const target = parts.pop() || "";
//       lines = [parts.join(" "), target];
//       console.log("Split single line input into two", lines);
//     }

//     const execInput = lines.join("\n");
//     const result = await executeCode({
//       code,
//       language,
//       input: execInput,
//       timeout,
//     });
//     await updateJobData({
//       ...job,
//       output: result.output,
//       error: result.error,
//       time: result.time,
//       memory: result.memory,
//     });
//   } catch (error) {
//     console.error(error);

//     try {
//       await updateJobData({
//         ...job,
//         output: "",
//         error: error.message,
//         time: null,
//         memory: null,
//       });
//     } catch (error) {
//       console.error(error);
//     }
//   }
// };

// export default processJob;
