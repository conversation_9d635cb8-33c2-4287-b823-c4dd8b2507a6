/* Mobile Responsiveness Fixes */

/* Ensure consistent navbar height across all devices */
.navbar-fixed {
  height: 80px !important;
  z-index: 60 !important;
}

/* Sidebar responsive behavior */
.sidebar-responsive {
  z-index: 50 !important;
}

/* Mobile overlay for sidebar */
.mobile-overlay {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 45;
  display: none;
}

/* Content area responsive adjustments */
.content-responsive {
  transition: all 0.3s ease;
  padding-top: 80px;
}

/* Mobile-specific styles */
@media (max-width: 1023px) {
  .mobile-overlay.show {
    display: block;
  }
  
  .sidebar-responsive {
    width: 100% !important;
    max-width: 280px !important;
  }
  
  .content-responsive {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 1rem;
  }
  
  /* Fix any text overflow on mobile */
  .text-responsive {
    font-size: 14px;
    line-height: 1.4;
  }
  
  /* Ensure buttons are touch-friendly */
  .btn-mobile {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }
  
  /* Fix course section spacing on mobile */
  .course-section-mobile {
    padding: 1rem 0.5rem;
    margin: 0;
  }
  
  /* Profile page mobile fixes */
  .profile-mobile {
    padding: 1rem;
    overflow-x: hidden;
  }
}

/* Tablet-specific adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .content-responsive {
    padding: 1.5rem;
  }
  
  .sidebar-responsive {
    max-width: 320px;
  }
}

/* Desktop-specific styles */
@media (min-width: 1024px) {
  .mobile-overlay {
    display: none !important;
  }
  
  .content-responsive.sidebar-open {
    margin-left: 280px;
    width: calc(100% - 280px);
  }
}

/* Fix z-index layering issues */
.z-navbar { z-index: 60; }
.z-sidebar { z-index: 50; }
.z-overlay { z-index: 45; }
.z-content { z-index: 10; }

/* Smooth transitions for all responsive elements */
.transition-responsive {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Fix any horizontal scroll issues */
.no-horizontal-scroll {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Ensure touch targets are accessible */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Fix course cards on mobile */
@media (max-width: 768px) {
  .course-card-mobile {
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .course-grid-mobile {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1rem;
  }
}

/* Fix profile sidebar on mobile */
@media (max-width: 768px) {
  .profile-sidebar-mobile {
    position: fixed;
    top: 80px;
    left: 0;
    height: calc(100vh - 80px);
    width: 100%;
    max-width: 280px;
    z-index: 50;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .profile-sidebar-mobile.open {
    transform: translateX(0);
  }
  
  .profile-content-mobile {
    width: 100%;
    padding: 1rem;
    margin-left: 0;
  }
}

/* Ensure all interactive elements are properly sized for mobile */
.mobile-friendly {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* Fix any layout shifts */
.layout-stable {
  contain: layout style;
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
  .focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }
  
  .sr-only-mobile {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }
}

/* Fix any content that might be cut off */
.safe-area {
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
}

/* Ensure proper scrolling behavior */
.scroll-smooth {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Fix any issues with fixed positioning on mobile */
@supports (-webkit-touch-callout: none) {
  .ios-fix {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}
