import React from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";

const learningPaths = [
  {
    title: "DevOps Engineer",
    courses: "17 Courses",
    hours: "350h",
    image: "/images/dsa-logo.png",
    path: "/devops-path",
    description: "Master containerization, CI/CD, and cloud infrastructure",
  },
  {
    title: "Kubernetes Administrator",
    courses: "12 Courses",
    hours: "250h",
    image: "/images/interviewlogo.png",
    path: "/kubernetes-path",
    description: "Learn container orchestration and cluster management",
  },
  {
    title: "System Administrator",
    courses: "13 Courses",
    hours: "340h",
    image: "/images/systemdesignlogo-.png",
    path: "/system-path",
    description: "Master system administration and infrastructure management",
  },
];

const LearningPathsSection = ({ itemVariants }) => {
  return (
    <section className="py-20 bg-[#1b2433]">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium mb-6 border border-blue-500/30">
            <span className="mr-2">🎯</span>
            Learning Paths
          </div>

          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Choose Your Career Path
          </h2>

          <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Select a specialized learning path designed to take you from beginner 
            to expert in your chosen field with structured, comprehensive courses.
          </p>
        </motion.div>

        {/* Learning Paths Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
        >
          {learningPaths.map((path, index) => (
            <PathCard key={index} path={path} index={index} />
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <Link
            to="/learning-paths"
            className="inline-flex items-center px-8 py-4 bg-blue-800 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
          >
            Explore All Learning Paths
            <svg
              className="ml-2 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

const PathCard = ({ path, index }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: 0.1 * index }}
    className="bg-[#2a3441] rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-700 group"
  >
    {/* Card Header */}
    <div className="relative h-48 bg-gradient-to-br from-slate-700 to-gray-600 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
          <img
            src={path.image}
            alt={path.title}
            className="w-8 h-8 object-contain"
          />
        </div>
        <span className="bg-white/20 text-white px-3 py-1 rounded-full text-xs font-semibold">
          Path
        </span>
      </div>
      <h3 className="text-2xl font-bold text-white mb-2">
        {path.title}
      </h3>
      <p className="text-blue-100 text-sm">
        {path.description}
      </p>
    </div>

    {/* Card Content */}
    <div className="p-6">
      {/* Path Stats */}
      <div className="flex items-center justify-between text-sm text-gray-400 mb-6">
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 005.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
          </svg>
          <span>{path.courses}</span>
        </div>
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"></path>
          </svg>
          <span>{path.hours}</span>
        </div>
      </div>

      {/* CTA Button */}
      <Link
        to={path.path}
        className="block w-full bg-[#3d5c88] hover:bg-blue-900 text-white py-3 px-4 rounded-lg font-semibold text-center transition-colors duration-300"
      >
        Start Learning Path
      </Link>
    </div>
  </motion.div>
);

export default LearningPathsSection;
