import mongoose from "mongoose";
import { LAB_TYPES } from "../constants/labTypes.js";

const labSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Lab name is required"],
      unique: true,
      trim: true,
    },
    title:{
      type:String,
    },
    description: {
      type: String,
      trim: true,
    },
    labType: {
      type: String,
      enum: LAB_TYPES,
      required: true,
    },
    icon: {
      public_id: String,
      secure_url: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    slug: {
      type: String,
      unique: true,
      lowercase: true,
      trim: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },

    learningPoints: [
      {
        type: String,
        trim: true,
      },
    ],
    labels: [
      {
        type: String,
        enum: ["Hot", "Popular", "Career", "Advanced", "In Demand", "New"],
        trim: true,
      },
    ],
  },
  { timestamps: true }
);

labSchema.pre("save", function (next) {
  if (this.isModified("name")) {
    this.slug = this.name.toLowerCase().replace(/\s+/g, "-");
  }
  next();
});

const Lab = mongoose.model("Lab", labSchema);
export default Lab;
