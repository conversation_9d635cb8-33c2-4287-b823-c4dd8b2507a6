import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, <PERSON>O<PERSON>, Clock } from 'lucide-react';
import RichTextEditor from './RichTextEditor';

const ChapterManager = ({ theme }) => {
  const [chapters, setChapters] = useState([
    {
      id: 1,
      title: 'Introduction to React',
      description: 'Learn the fundamentals of React including components, props, and state management.',
      content: '<h2>Getting Started with React</h2><p>React is a JavaScript library for building user interfaces. In this chapter, you will learn:</p><ul><li>What is React and why use it</li><li>Setting up your development environment</li><li>Creating your first React component</li><li>Understanding JSX syntax</li></ul><h3>Prerequisites</h3><p>Before starting this chapter, you should have:</p><ul><li>Basic knowledge of HTML, CSS, and JavaScript</li><li>Familiarity with ES6+ features</li></ul>',
      courseId: 'react-fundamentals',
      order: 1,
      duration: 45,
      isPublished: true,
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      title: 'State and Props',
      description: 'Deep dive into React state management and component communication through props.',
      content: '<h2>Understanding State and Props</h2><p>State and props are fundamental concepts in React that enable dynamic and interactive user interfaces.</p><h3>What is State?</h3><p>State is a built-in React object that is used to contain data or information about the component.</p><h3>What are Props?</h3><p>Props (short for properties) are a way of passing data from parent to child components.</p>',
      courseId: 'react-fundamentals',
      order: 2,
      duration: 60,
      isPublished: true,
      createdAt: '2024-01-14'
    }
  ]);

  const [showEditor, setShowEditor] = useState(false);
  const [editingChapter, setEditingChapter] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    courseId: '',
    order: 1,
    duration: 30,
    isPublished: false
  });

  const courses = [
    { id: 'react-fundamentals', name: 'React Fundamentals' },
    { id: 'javascript-advanced', name: 'Advanced JavaScript' },
    { id: 'node-backend', name: 'Node.js Backend Development' },
    { id: 'database-design', name: 'Database Design' }
  ];



  const handleAdd = () => {
    setEditingChapter(null);
    setFormData({
      title: '',
      description: '',
      content: '',
      courseId: courses[0]?.id || '',
      order: chapters.length + 1,
      duration: 30,
      isPublished: false
    });
    setShowEditor(true);
  };

  const handleEdit = (chapter) => {
    setEditingChapter(chapter);
    setFormData({
      title: chapter.title,
      description: chapter.description,
      content: chapter.content,
      courseId: chapter.courseId,
      order: chapter.order,
      duration: chapter.duration,
      isPublished: chapter.isPublished
    });
    setShowEditor(true);
  };

  const handleSave = () => {
    if (editingChapter) {
      setChapters(prev => prev.map(chapter => 
        chapter.id === editingChapter.id 
          ? { ...chapter, ...formData }
          : chapter
      ));
    } else {
      const newChapter = {
        id: Date.now(),
        ...formData,
        createdAt: new Date().toISOString().split('T')[0]
      };
      setChapters(prev => [...prev, newChapter]);
    }
    setShowEditor(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this chapter?')) {
      setChapters(prev => prev.filter(chapter => chapter.id !== id));
    }
  };

  const togglePublished = (id) => {
    setChapters(prev => prev.map(chapter => 
      chapter.id === id 
        ? { ...chapter, isPublished: !chapter.isPublished }
        : chapter
    ));
  };

  const getCourseNameById = (courseId) => {
    return courses.find(course => course.id === courseId)?.name || 'Unknown Course';
  };

  const sortedChapters = [...chapters].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Chapter Manager
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Create and manage course chapters with rich content
          </p>
        </div>
        
        <button
          onClick={handleAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add Chapter</span>
        </button>
      </div>

      {/* Chapters Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {sortedChapters.map((chapter) => (
          <div
            key={chapter.id}
            className={`rounded-lg border p-6 ${
              theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <BookOpen size={16} className={theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} />
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    theme === 'dark' ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-800'
                  }`}>
                    Chapter {chapter.order}
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    chapter.isPublished
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {chapter.isPublished ? 'Published' : 'Draft'}
                  </span>
                </div>
                
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {chapter.title}
                </h3>
                
                <p className={`text-sm mb-3 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {chapter.description}
                </p>

                <div className="flex items-center space-x-4 text-sm">
                  <div className={`flex items-center space-x-1 ${
                    theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
                  }`}>
                    <Clock size={14} />
                    <span>{chapter.duration} min</span>
                  </div>
                  <div className={`${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                    {getCourseNameById(chapter.courseId)}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => togglePublished(chapter.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                  title={chapter.isPublished ? 'Unpublish' : 'Publish'}
                >
                  {chapter.isPublished ? '👁️' : '👁️‍🗨️'}
                </button>
                <button
                  onClick={() => handleEdit(chapter)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => handleDelete(chapter.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                      : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                  }`}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>

            {/* Content Preview */}
            <div className={`prose prose-sm max-w-none ${
              theme === 'dark' ? 'prose-invert' : ''
            }`}>
              <div 
                dangerouslySetInnerHTML={{ 
                  __html: chapter.content.substring(0, 200) + (chapter.content.length > 200 ? '...' : '') 
                }} 
              />
            </div>

            <div className={`mt-4 pt-4 border-t text-xs ${
              theme === 'dark' ? 'border-gray-700 text-gray-500' : 'border-gray-200 text-gray-400'
            }`}>
              Created: {chapter.createdAt}
            </div>
          </div>
        ))}
      </div>

      {/* Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className={`rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {editingChapter ? 'Edit Chapter' : 'Add New Chapter'}
              </h3>
              <button
                onClick={() => setShowEditor(false)}
                className={`p-2 rounded-lg transition-colors ${
                  theme === 'dark'
                    ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                }`}
              >
                ×
              </button>
            </div>

            <div className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Chapter Title
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    placeholder="Enter chapter title..."
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Course
                  </label>
                  <select
                    value={formData.courseId}
                    onChange={(e) => setFormData(prev => ({ ...prev, courseId: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  >
                    {courses.map(course => (
                      <option key={course.id} value={course.id}>{course.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Chapter Order
                  </label>
                  <input
                    type="number"
                    value={formData.order}
                    onChange={(e) => setFormData(prev => ({ ...prev, order: parseInt(e.target.value) }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    min="1"
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={formData.duration}
                    onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    min="1"
                  />
                </div>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  placeholder="Enter chapter description..."
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Chapter Content
                </label>
                <RichTextEditor
                  value={formData.content}
                  onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                  theme={theme}
                  placeholder="Enter chapter content..."
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPublished"
                  checked={formData.isPublished}
                  onChange={(e) => setFormData(prev => ({ ...prev, isPublished: e.target.checked }))}
                  className="mr-2"
                />
                <label htmlFor="isPublished" className={`text-sm ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Publish immediately
                </label>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowEditor(false)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Save size={16} />
                <span>{editingChapter ? 'Update' : 'Create'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChapterManager;
