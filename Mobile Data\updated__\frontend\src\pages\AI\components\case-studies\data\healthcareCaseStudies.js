const healthcareCaseStudies = [
  {
    title: "Medical Image Analysis",
    objective: "Learn medical image processing techniques",
    scenario: "Develop a system for analyzing X-ray images",
    keyConcepts: ["Image Preprocessing", "Medical Imaging", "Disease Detection", "CNN Architecture"],
    solution: `# Medical Image Analysis Example
import torch
import torch.nn as nn
import torchvision.transforms as transforms

class MedicalImageAnalyzer(nn.Module):
    def __init__(self, num_classes):
        super().__init__()
        # Use pre-trained model as base
        self.backbone = torchvision.models.resnet18(pretrained=True)
        
        # Replace last layer for medical classification
        num_features = self.backbone.fc.in_features
        self.backbone.fc = nn.Sequential(
            nn.Linear(num_features, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, num_classes)
        )
    
    def forward(self, x):
        return self.backbone(x)

# Image preprocessing pipeline
transform = transforms.Compose([
    transforms.Resize(256),
    transforms.CenterCrop(224),
    transforms.ToTensor(),
    transforms.Normalize([0.485, 0.456, 0.406], 
                       [0.229, 0.224, 0.225])
])
`
  }
];

export default healthcareCaseStudies;
