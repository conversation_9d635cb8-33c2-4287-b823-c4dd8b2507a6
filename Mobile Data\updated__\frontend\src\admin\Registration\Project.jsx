import { useEffect, useState } from "react";
import { DeleteModal } from "../../components/ui";
import axiosInstance from "../../utils/axiosInstance";

const Project = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isOpen, setIsOpen] = useState(false);
  const [projects, setProjects] = useState([]);
  const casesPerPage = 5;

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const { data } = await axiosInstance.get(
          "http://localhost:8000/api/v1/all-project-registration",
          { withCredentials: true }
        );
        setProjects(data.projects);
      } catch (err) {
        console.log(err.response?.data?.message || "Something went wrong!");
      }
    };

    fetchUsers();
  }, []);

  const indexOfLastCase = currentPage * casesPerPage;
  const indexOfFirstCase = indexOfLastCase - casesPerPage;
  const currentCases = projects.slice(indexOfFirstCase, indexOfLastCase);

  const nextPage = () => {
    if (currentPage < Math.ceil(projects.length / casesPerPage)) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const openDeleteModal = () => {
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setIsOpen(false);
  };

  const deleteConfirmation = () => {
    alert("Project Data Deleted!");
    setIsOpen(false);
  };

  return (
    <div className="bg-gray-100 min-h-screen py-10">
      {/* Filter Section */}
      <div className="w-[95%] max-w-[1100px] mx-auto p-6 bg-white border border-gray-300 rounded-lg shadow-md">
        <h2 className="text-2xl font-bold text-blue-500 mb-6">
          Filter Project
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label
              htmlFor="program-name"
              className="block font-semibold text-gray-700 mb-2"
            >
              Program Name
            </label>
            <input
              type="text"
              id="program-name"
              placeholder="Status"
              className="w-full h-10 px-3 border border-gray-300 rounded-md outline-none"
            />
          </div>

          <div>
            <label
              htmlFor="start-date"
              className="block font-semibold text-gray-700 mb-2"
            >
              Start Date
            </label>
            <input
              type="date"
              id="start-date"
              className="w-full h-10 px-3 border border-gray-300 rounded-md outline-none"
            />
          </div>
        </div>
      </div>

      {/* Table Section */}
      <div className="w-[95%] max-w-[1100px] mx-auto p-6 mt-8 bg-white rounded-lg shadow-lg overflow-hidden">
        <h2 className="text-xl font-bold text-blue-500 mb-4">
          Project Details
        </h2>

        <div className="max-h-[400px] overflow-x-auto overflow-y-auto">
          <table className="w-full border-collapse text-sm sm:text-base">
            <thead className="bg-blue-900 text-white sticky top-0 z-10">
              <tr className="uppercase text-left font-bold">
                <th className="p-3">#</th>
                <th className="p-3">First Name</th>
                <th className="p-3">Last Name</th>
                <th className="p-3">Email</th>
                <th className="p-3">Mobile</th>
                <th className="p-3">College Name</th>
                <th className="p-3">Degree</th>
                <th className="p-3">Semester</th>
                <th className="p-3">Project Name</th>
                <th className="p-3">Description</th>
                <th className="p-3">Given Date</th>
                <th className="p-3">Deadline</th>
                <th className="p-3">Queries</th>
                <th className="p-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {currentCases.map((data, index) => (
                <tr
                  key={index}
                  className="even:bg-gray-50 border-b border-gray-200 text-gray-800"
                >
                  <td className="p-3">{indexOfFirstCase + index + 1}</td>
                  <td className="p-3">{data.firstname}</td>
                  <td className="p-3">{data.lastname}</td>
                  <td className="p-3">{data.email}</td>
                  <td className="p-3">{data.mobileNumber}</td>
                  <td className="p-3">{data.collegeName}</td>
                  <td className="p-3">{data.degree}</td>
                  <td className="p-3">{data.currentSemester}</td>
                  <td className="p-3">{data.projectName}</td>
                  <td className="p-3">{data.projectDescription}</td>
                  <td className="p-3">{data.dateGiven}</td>
                  <td className="p-3">{data.deadline}</td>
                  <td className="p-3">{data.queries}</td>
                  <td className="p-3 text-center">
                    <button
                      className="w-[90px] h-[36px] bg-red-500 text-white rounded-md font-bold text-sm transition-transform transform hover:scale-105 hover:bg-red-600"
                      onClick={openDeleteModal}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="flex flex-wrap justify-center items-center text-center mt-6 gap-3 sm:gap-0 sm:flex-nowrap">
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md font-bold transition-transform transform hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={prevPage}
            disabled={currentPage === 1}
          >
            ⬅ Prev
          </button>
          <span className="text-lg font-bold text-blue-500 bg-blue-100 px-4 py-2 rounded-md">
            Page {currentPage}
          </span>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md font-bold transition-transform transform hover:scale-105 disabled:bg-gray-500 disabled:cursor-not-allowed"
            onClick={nextPage}
            disabled={currentPage >= Math.ceil(projects.length / casesPerPage)}
          >
            Next ➡
          </button>
        </div>
      </div>

      {/* Delete Modal */}
      <DeleteModal
        title={"Delete Project Table Data"}
        message={"Are You Sure Want to Delete?"}
        onClose={closeDeleteModal}
        isOpen={isOpen}
        onConfirm={deleteConfirmation}
      />
    </div>
  );
};

export default Project;
