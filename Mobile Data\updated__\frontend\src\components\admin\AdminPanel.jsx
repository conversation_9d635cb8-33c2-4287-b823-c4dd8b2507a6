import React from "react";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import { Menu, X, Sun, Moon, ArrowLeft } from "lucide-react";
import { useAdminPanel } from "../../hooks/useAdminPanel";

// Import all admin components
import UserLogsTable from "./components/UserLogsTable";
import FAQManager from "./components/FAQManager";
import IntroSectionManager from "./components/IntroSectionManager";
import ChapterManager from "./components/ChapterManager";
import QuestionForm from "./components/QuestionForm";
import ExplanationForm from "./components/ExplanationForm";
import PaymentDashboard from "./components/PaymentDashboard";
import RefundTracker from "./components/RefundTracker";
import RatingsDisplay from "./components/RatingsDisplay";
import ContentManager from "./components/ContentManager";
import Introduction from "./components/Introduction";
import Category from "./components/Category";
import LabSection from "./components/LabSection";
import CreateLab from "./components/CreateLab";

const AdminPanel = () => {
  const navigate = useNavigate();
  const {
    theme,
    activeSection,
    sidebarOpen,
    sections,
    toggleTheme,
    setActiveSection,
    toggleSidebar,
  } = useAdminPanel();

  const handleBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/");
    }
  };

  const renderActiveSection = () => {
    switch (activeSection) {
      case "user-logs":
        return <UserLogsTable theme={theme} />;
      case "faq-manager":
        return <FAQManager theme={theme} />;
      case "intro-manager":
        return <IntroSectionManager theme={theme} />;
      case "chapter-manager":
        return <ChapterManager theme={theme} />;
      case "question-form":
        return <QuestionForm theme={theme} />;
      case "explanation-form":
        return <ExplanationForm theme={theme} />;
      case "payment-dashboard":
        return <PaymentDashboard theme={theme} />;
      case "refund-tracker":
        return <RefundTracker theme={theme} />;
      case "ratings-display":
        return <RatingsDisplay theme={theme} />;
      case "content-manager":
        return <ContentManager theme={theme} />;
      case "create-lab":
        return <CreateLab theme={theme} />;
      case "lab-section":
        return <LabSection theme={theme} />;
      case "category":
        return <Category theme={theme} />;
      case "introduction":
        return <Introduction theme={theme} />;
      default:
        return <UserLogsTable theme={theme} />;
    }
  };

  return (
    <div
      className={`min-h-screen ${
        theme === "dark" ? "bg-gray-900" : "bg-gray-50"
      }`}
    >
      {/* Header */}
      <header
        className={`border-b ${
          theme === "dark"
            ? "bg-gray-800 border-gray-700"
            : "bg-white border-gray-200"
        } px-4 py-3 sticky top-0 z-50`}
      >
        <div className="flex items-center justify-between">
          {/* Left side */}
          <div className="flex items-center space-x-4">
            <button
              onClick={handleBack}
              className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                theme === "dark"
                  ? "text-gray-300 hover:bg-gray-700 hover:text-white"
                  : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
              }`}
              title="Go Back"
            >
              <ArrowLeft size={18} />
              <span className="hidden sm:inline">Back</span>
            </button>

            <button
              onClick={toggleSidebar}
              className={`p-2 rounded-lg transition-colors lg:hidden ${
                theme === "dark"
                  ? "text-gray-300 hover:bg-gray-700"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
            </button>

            <h1
              className={`text-xl font-bold ${
                theme === "dark" ? "text-white" : "text-gray-900"
              }`}
            >
              Admin Panel
            </h1>
          </div>

          {/* Right side */}
          <button
            onClick={toggleTheme}
            className={`p-2 rounded-lg transition-colors ${
              theme === "dark"
                ? "bg-gray-700 hover:bg-gray-600 text-yellow-400"
                : "bg-gray-100 hover:bg-gray-200 text-gray-600"
            }`}
            title="Toggle Theme"
          >
            {theme === "dark" ? <Sun size={20} /> : <Moon size={20} />}
          </button>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <motion.aside
          initial={false}
          animate={{
            width: sidebarOpen ? 280 : 0,
            opacity: sidebarOpen ? 1 : 0,
          }}
          transition={{ duration: 0.3 }}
          className={`${
            theme === "dark"
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          } border-r overflow-y-auto sticky top-16 h-[calc(100vh-4rem)]`}
        >
          <nav className="p-4 space-y-2">
            {sections.map((section) => (
              <button
                key={section.id}
                onClick={() => setActiveSection(section.id)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  activeSection === section.id
                    ? theme === "dark"
                      ? "bg-blue-600 text-white"
                      : "bg-blue-600 text-white"
                    : theme === "dark"
                    ? "text-gray-300 hover:bg-gray-700 hover:text-white"
                    : "text-gray-600 hover:bg-gray-100 hover:text-gray-900"
                }`}
              >
                <span className="text-lg">{section.icon}</span>
                <span className="font-medium">{section.label}</span>
              </button>
            ))}
          </nav>
        </motion.aside>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <motion.div
            key={activeSection}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {renderActiveSection()}
          </motion.div>
        </main>
      </div>
    </div>
  );
};

export default AdminPanel;
