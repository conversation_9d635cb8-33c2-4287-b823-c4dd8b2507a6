import React, { useState } from "react";
import { motion } from "framer-motion";

const SystemDesignLiveClasses = ({ onBackToCourse }) => {
  const [activeTab, setActiveTab] = useState("upcoming");

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const upcomingClasses = [
    {
      title: "Designing Scalable Web Applications",
      instructor: "Dr. <PERSON>",
      date: "July 18, 2025",
      time: "2:00 PM EST",
      duration: "2 hours",
      level: "Intermediate",
      topics: ["Load balancing", "Database sharding", "Caching strategies", "CDN implementation"]
    },
    {
      title: "Microservices Architecture Deep Dive",
      instructor: "Prof. <PERSON>",
      date: "July 20, 2025", 
      time: "3:00 PM EST",
      duration: "2.5 hours",
      level: "Advanced",
      topics: ["Service decomposition", "API gateway patterns", "Event-driven architecture", "Monitoring & observability"]
    },
    {
      title: "Database Design for Large Scale Systems",
      instructor: "Dr. <PERSON>",
      date: "July 22, 2025",
      time: "1:00 PM EST", 
      duration: "2 hours",
      level: "Intermediate",
      topics: ["NoSQL vs SQL", "Data partitioning", "Replication strategies", "ACID vs BASE"]
    },
    {
      title: "System Design Interview Masterclass",
      instructor: "Alex Thompson (Ex-Google)",
      date: "July 24, 2025",
      time: "4:00 PM EST",
      duration: "3 hours",
      level: "All Levels",
      topics: ["Problem-solving approach", "Communication techniques", "Common pitfalls", "Mock interviews"]
    }
  ];

  const pastRecordings = [
    {
      title: "Building Distributed Systems",
      instructor: "Dr. Robert Kim",
      date: "July 10, 2025",
      duration: "2.5 hours",
      views: "1,245",
      rating: "4.9",
      topics: ["CAP theorem", "Consistency patterns", "Consensus algorithms", "Fault tolerance"]
    },
    {
      title: "High-Performance Computing Architecture",
      instructor: "Prof. Lisa Wang",
      date: "July 8, 2025",
      duration: "2 hours",
      views: "987",
      rating: "4.8",
      topics: ["Performance optimization", "Caching layers", "Memory management", "CPU utilization"]
    },
    {
      title: "Cloud Infrastructure Design",
      instructor: "Mark Anderson (AWS Solutions Architect)",
      date: "July 5, 2025",
      duration: "2.5 hours",
      views: "1,567",
      rating: "4.9",
      topics: ["Auto-scaling", "Multi-region deployment", "Disaster recovery", "Cost optimization"]
    }
  ];

  const workshops = [
    {
      title: "Design Netflix's Recommendation System",
      type: "Interactive Workshop",
      date: "July 26, 2025",
      time: "2:00 PM EST",
      duration: "4 hours",
      participants: "Max 20",
      description: "Build a complete recommendation system from scratch"
    },
    {
      title: "Create a Chat Application Architecture",
      type: "Hands-on Session", 
      date: "July 28, 2025",
      time: "3:00 PM EST",
      duration: "3 hours",
      participants: "Max 15",
      description: "Design real-time messaging with WebSocket architecture"
    }
  ];

  return (
    <div className="py-8">
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🎓 Live System Design Classes
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Expert-Led{" "}
            <span className="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
              Learning
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Join live sessions with industry experts, participate in interactive workshops, and access recorded masterclasses
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-12">
          <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-2 flex gap-2">
            <button
              onClick={() => setActiveTab("upcoming")}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === "upcoming"
                  ? "bg-purple-500 text-white shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/10"
              }`}
            >
              Upcoming Classes
            </button>
            <button
              onClick={() => setActiveTab("recordings")}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === "recordings"
                  ? "bg-purple-500 text-white shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/10"
              }`}
            >
              Past Recordings
            </button>
            <button
              onClick={() => setActiveTab("workshops")}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${
                activeTab === "workshops"
                  ? "bg-purple-500 text-white shadow-lg"
                  : "text-white/70 hover:text-white hover:bg-white/10"
              }`}
            >
              Workshops
            </button>
          </div>
        </div>

        {/* Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          {activeTab === "upcoming" && upcomingClasses.map((classItem, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8 hover:bg-white/10 transition-all duration-300"
            >
              <div className="grid md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <div className="flex items-center gap-4 mb-4">
                    <h3 className="text-2xl font-bold text-white">{classItem.title}</h3>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      classItem.level === 'Beginner' ? 'bg-green-500/20 text-green-400' :
                      classItem.level === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-400' :
                      classItem.level === 'Advanced' ? 'bg-red-500/20 text-red-400' :
                      'bg-blue-500/20 text-blue-400'
                    }`}>
                      {classItem.level}
                    </span>
                  </div>
                  
                  <p className="text-white/80 mb-4">
                    <span className="font-semibold">Instructor:</span> {classItem.instructor}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {classItem.topics.map((topic, idx) => (
                      <span
                        key={idx}
                        className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="bg-white/5 rounded-lg p-6">
                  <div className="space-y-3 text-white/80">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-400">📅</span>
                      <span>{classItem.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-green-400">🕒</span>
                      <span>{classItem.time}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-purple-400">⏱️</span>
                      <span>{classItem.duration}</span>
                    </div>
                  </div>
                  
                  <button className="w-full mt-6 bg-gradient-to-r from-purple-500 to-blue-500 text-white py-3 rounded-lg font-medium hover:from-purple-600 hover:to-blue-600 transition-all duration-300">
                    Register Now
                  </button>
                </div>
              </div>
            </motion.div>
          ))}

          {activeTab === "recordings" && pastRecordings.map((recording, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8 hover:bg-white/10 transition-all duration-300"
            >
              <div className="grid md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <h3 className="text-2xl font-bold text-white mb-4">{recording.title}</h3>
                  
                  <p className="text-white/80 mb-4">
                    <span className="font-semibold">Instructor:</span> {recording.instructor}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {recording.topics.map((topic, idx) => (
                      <span
                        key={idx}
                        className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="bg-white/5 rounded-lg p-6">
                  <div className="space-y-3 text-white/80 mb-6">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-400">📅</span>
                      <span>{recording.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-purple-400">⏱️</span>
                      <span>{recording.duration}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-400">👁️</span>
                      <span>{recording.views} views</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-green-400">⭐</span>
                      <span>{recording.rating}/5</span>
                    </div>
                  </div>
                  
                  <button className="w-full bg-gradient-to-r from-green-500 to-blue-500 text-white py-3 rounded-lg font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300">
                    Watch Recording
                  </button>
                </div>
              </div>
            </motion.div>
          ))}

          {activeTab === "workshops" && workshops.map((workshop, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-8 hover:bg-white/10 transition-all duration-300"
            >
              <div className="grid md:grid-cols-3 gap-6">
                <div className="md:col-span-2">
                  <div className="flex items-center gap-4 mb-4">
                    <h3 className="text-2xl font-bold text-white">{workshop.title}</h3>
                    <span className="px-3 py-1 bg-orange-500/20 text-orange-400 rounded-full text-xs font-medium">
                      {workshop.type}
                    </span>
                  </div>
                  
                  <p className="text-white/80 mb-4">{workshop.description}</p>
                </div>
                
                <div className="bg-white/5 rounded-lg p-6">
                  <div className="space-y-3 text-white/80 mb-6">
                    <div className="flex items-center gap-2">
                      <span className="text-blue-400">📅</span>
                      <span>{workshop.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-green-400">🕒</span>
                      <span>{workshop.time}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-purple-400">⏱️</span>
                      <span>{workshop.duration}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-orange-400">👥</span>
                      <span>{workshop.participants}</span>
                    </div>
                  </div>
                  
                  <button className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 rounded-lg font-medium hover:from-orange-600 hover:to-red-600 transition-all duration-300">
                    Join Workshop
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default SystemDesignLiveClasses;
