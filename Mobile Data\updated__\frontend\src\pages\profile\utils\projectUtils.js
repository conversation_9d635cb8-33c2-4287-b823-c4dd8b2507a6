export const calculateDaysRemaining = (deadline) => {
  const today = new Date();
  const deadlineDate = new Date(deadline);
  const diffTime = deadlineDate - today;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export const getProjectStats = (projects) => {
  const totalProjects = projects.active.length + projects.completed.length + projects.paused.length;
  const completedProjects = projects.completed.length;
  const activeProjects = projects.active.length;
  const pausedProjects = projects.paused.length;
  const completionRate = totalProjects > 0 ? Math.round((completedProjects / totalProjects) * 100) : 0;
  
  const totalTimeSpent = [...projects.active, ...projects.completed, ...projects.paused]
    .reduce((total, project) => {
      const hours = parseInt(project.timeSpent.split(' ')[0]);
      return total + hours;
    }, 0);

  return {
    totalProjects,
    completedProjects,
    activeProjects,
    pausedProjects,
    completionRate,
    totalTimeSpent
  };
};
