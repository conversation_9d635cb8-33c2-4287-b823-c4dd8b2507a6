import React from 'react';
import { motion } from 'framer-motion';

const Stat = ({ number, label, delay }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.9 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.4, delay: delay }}
    className="flex flex-col items-center justify-center p-4"
  >
    <div className="relative">
      <div className="text-3xl sm:text-4xl font-bold text-white mb-1">
        {number}
      </div>
      <div className="absolute -bottom-1 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full"></div>
    </div>
    <div className="text-gray-400 text-sm font-medium mt-2 text-center">
      {label}
    </div>
  </motion.div>
);

const HeroStats = ({ itemVariants }) => (
  <motion.div
    variants={itemVariants}
    className="mt-16"
  >
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.3 }}
      className="text-center mb-10"
    >
      <h3 className="text-xl font-semibold text-white mb-2">
        Platform Statistics
      </h3>
      <div className="w-16 h-1 bg-gradient-to-r from-blue-600 to-blue-500 mx-auto rounded-full"></div>
    </motion.div>

    <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-6">
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        <Stat
          number="1000+"
          label="Problems"
          delay={0.4}
        />
        <Stat
          number="50+"
          label="Languages"
          delay={0.5}
        />
        <Stat
          number="500K+"
          label="Users"
          delay={0.6}
        />
        <Stat
          number="95%"
          label="Success"
          delay={0.7}
        />
      </div>
    </div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.8 }}
      className="mt-8 flex flex-wrap justify-center gap-6 text-sm text-gray-400"
    >
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        <span>Live Coding</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
        <span>Interview Prep</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-700 rounded-full"></div>
        <span>Real Projects</span>
      </div>
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-800 rounded-full"></div>
        <span>Certification</span>
      </div>
    </motion.div>
  </motion.div>
);

export default HeroStats;
