export const sanitizeInput = (code) => {
  if (typeof code !== "string") {
    throw new Error("Code must be a string.");
  }

  let sanitized = code;

  sanitized = sanitized.replace(
    /\b(child_process|exec|spawn|fork|curl|wget|nc|ncat|netcat|bash|sh|zsh|rm|del|fs\.|unlink|chmod|chown)\b/gi,
    ""
  );

  sanitized = sanitized.replace(
    /\b(require\(.+?\)|eval|Function|new Function|setTimeout|setInterval|module\.exports|__dirname|__filename)\b/gi,
    ""
  );

  sanitized = sanitized.replace(/__proto__|constructor|prototype/gi, "");

  sanitized = sanitized
    .replace(/\bwhile\s*\(\s*true\s*\)/gi, "// blocked while(true)")
    .replace(/\bfor\s*\(\s*;;\s*\)/gi, "// blocked for(;;)");

  sanitized = sanitized.replace(
    /\b(http\.request|axios|fetch|net\.connect|request\.get|socket\.connect|ftp|sftp|telnet)\b/gi,
    ""
  );

  sanitized = sanitized.replace(
    /\b(\/dev\/tcp\/|nc\s+-e\s+|bash\s+-i\s+|python\s+-c\s+|perl\s+-e\s+|ruby\s+-e\s+)\b/gi,
    ""
  );

  sanitized = sanitized.replace(/[\u0000\uFEFF]/g, "");

  return sanitized.trim();
};
