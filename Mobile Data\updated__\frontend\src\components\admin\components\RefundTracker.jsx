import React, { useState } from 'react';
import { Check, X, Clock, AlertCircle, DollarSign } from 'lucide-react';

const RefundTracker = ({ theme }) => {
  const [refunds, setRefunds] = useState([
    {
      id: 1,
      transactionId: 'TXN_001_2024',
      userId: 'user_001',
      userName: '<PERSON>',
      email: '<EMAIL>',
      amount: 299.99,
      reason: 'Course not as expected',
      requestDate: '2024-01-14',
      purchaseDate: '2024-01-10',
      status: 'pending',
      course: 'React Fundamentals',
      daysFromPurchase: 4,
      eligible: true
    },
    {
      id: 2,
      transactionId: 'TXN_002_2024',
      userId: 'user_002',
      userName: '<PERSON>',
      email: '<EMAIL>',
      amount: 199.99,
      reason: 'Technical issues with platform',
      requestDate: '2024-01-13',
      purchaseDate: '2024-01-08',
      status: 'approved',
      course: 'JavaScript Advanced',
      daysFromPurchase: 5,
      eligible: true
    },
    {
      id: 3,
      transactionId: 'TXN_003_2024',
      userId: 'user_003',
      userName: '<PERSON>',
      email: '<EMAIL>',
      amount: 399.99,
      reason: 'Changed mind',
      requestDate: '2024-01-12',
      purchaseDate: '2024-01-01',
      status: 'rejected',
      course: 'Full Stack Development',
      daysFromPurchase: 11,
      eligible: false
    }
  ]);

  const handleApprove = (id) => {
    setRefunds(prev => prev.map(refund => 
      refund.id === id 
        ? { ...refund, status: 'approved' }
        : refund
    ));
  };

  const handleReject = (id) => {
    if (window.confirm('Are you sure you want to reject this refund request?')) {
      setRefunds(prev => prev.map(refund => 
        refund.id === id 
          ? { ...refund, status: 'rejected' }
          : refund
      ));
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'processed': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getEligibilityStatus = (eligible, days) => {
    if (eligible && days <= 7) {
      return { color: 'text-green-600 bg-green-100', text: 'Eligible', icon: <Check size={14} /> };
    } else {
      return { color: 'text-red-600 bg-red-100', text: 'Not Eligible', icon: <X size={14} /> };
    }
  };

  const pendingRefunds = refunds.filter(r => r.status === 'pending');
  const approvedRefunds = refunds.filter(r => r.status === 'approved');
  const totalRefundAmount = approvedRefunds.reduce((sum, r) => sum + r.amount, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Refund Tracker
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage refund requests with 7-day eligibility policy
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`rounded-lg p-6 ${
          theme === 'dark' ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <div className="flex items-center">
            <div className={`p-3 rounded-full ${
              theme === 'dark' ? 'bg-yellow-900/20' : 'bg-yellow-100'
            }`}>
              <Clock className="text-yellow-600" size={24} />
            </div>
            <div className="ml-4">
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                Pending Requests
              </p>
              <p className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                {pendingRefunds.length}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-lg p-6 ${
          theme === 'dark' ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <div className="flex items-center">
            <div className={`p-3 rounded-full ${
              theme === 'dark' ? 'bg-green-900/20' : 'bg-green-100'
            }`}>
              <Check className="text-green-600" size={24} />
            </div>
            <div className="ml-4">
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                Approved Refunds
              </p>
              <p className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                {approvedRefunds.length}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-lg p-6 ${
          theme === 'dark' ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-200'
        }`}>
          <div className="flex items-center">
            <div className={`p-3 rounded-full ${
              theme === 'dark' ? 'bg-red-900/20' : 'bg-red-100'
            }`}>
              <DollarSign className="text-red-600" size={24} />
            </div>
            <div className="ml-4">
              <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                Total Refunded
              </p>
              <p className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                ${totalRefundAmount.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Refund Requests */}
      <div className="space-y-4">
        {refunds.map((refund) => {
          const eligibility = getEligibilityStatus(refund.eligible, refund.daysFromPurchase);
          
          return (
            <div
              key={refund.id}
              className={`rounded-lg border p-6 ${
                theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <h3 className={`text-lg font-semibold ${
                      theme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      Refund Request #{refund.id}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      getStatusColor(refund.status)
                    }`}>
                      {refund.status.charAt(0).toUpperCase() + refund.status.slice(1)}
                    </span>
                    <span className={`inline-flex items-center space-x-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      eligibility.color
                    }`}>
                      {eligibility.icon}
                      <span>{eligibility.text}</span>
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                      <p className={`text-sm font-medium ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        User Information
                      </p>
                      <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        {refund.userName}
                      </p>
                      <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        {refund.email}
                      </p>
                    </div>

                    <div>
                      <p className={`text-sm font-medium ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Transaction Details
                      </p>
                      <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        ID: {refund.transactionId}
                      </p>
                      <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        Course: {refund.course}
                      </p>
                      <p className={`text-sm font-semibold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                        Amount: ${refund.amount}
                      </p>
                    </div>

                    <div>
                      <p className={`text-sm font-medium ${
                        theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                      }`}>
                        Timeline
                      </p>
                      <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        Purchased: {refund.purchaseDate}
                      </p>
                      <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                        Requested: {refund.requestDate}
                      </p>
                      <p className={`text-sm ${
                        refund.daysFromPurchase <= 7 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {refund.daysFromPurchase} days from purchase
                      </p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className={`text-sm font-medium mb-2 ${
                      theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Reason for Refund
                    </p>
                    <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
                      {refund.reason}
                    </p>
                  </div>

                  {refund.daysFromPurchase > 7 && (
                    <div className={`flex items-center space-x-2 p-3 rounded-lg ${
                      theme === 'dark' ? 'bg-red-900/20' : 'bg-red-50'
                    }`}>
                      <AlertCircle className="text-red-600" size={16} />
                      <p className={`text-sm text-red-600`}>
                        This refund request is outside the 7-day eligibility window.
                      </p>
                    </div>
                  )}
                </div>

                {refund.status === 'pending' && (
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handleApprove(refund.id)}
                      disabled={!refund.eligible}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                        refund.eligible
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      <Check size={16} />
                      <span>Approve</span>
                    </button>
                    <button
                      onClick={() => handleReject(refund.id)}
                      className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                    >
                      <X size={16} />
                      <span>Reject</span>
                    </button>
                  </div>
                )}

                {refund.status === 'approved' && (
                  <div className={`px-4 py-2 rounded-lg ${
                    theme === 'dark' ? 'bg-green-900/20' : 'bg-green-50'
                  }`}>
                    <p className="text-green-600 text-sm font-medium">
                      ✓ Approved for refund
                    </p>
                  </div>
                )}

                {refund.status === 'rejected' && (
                  <div className={`px-4 py-2 rounded-lg ${
                    theme === 'dark' ? 'bg-red-900/20' : 'bg-red-50'
                  }`}>
                    <p className="text-red-600 text-sm font-medium">
                      ✗ Refund rejected
                    </p>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Policy Information */}
      <div className={`rounded-lg p-6 ${
        theme === 'dark' ? 'bg-blue-900/20 border border-blue-700' : 'bg-blue-50 border border-blue-200'
      }`}>
        <h3 className={`text-lg font-semibold mb-3 ${
          theme === 'dark' ? 'text-blue-300' : 'text-blue-800'
        }`}>
          Refund Policy
        </h3>
        <ul className={`space-y-2 text-sm ${
          theme === 'dark' ? 'text-blue-200' : 'text-blue-700'
        }`}>
          <li>• Refunds are only available within 7 days of purchase</li>
          <li>• All refund requests must include a valid reason</li>
          <li>• Approved refunds will be processed within 3-5 business days</li>
          <li>• Refunds will be issued to the original payment method</li>
          <li>• Course access will be revoked upon refund approval</li>
        </ul>
      </div>
    </div>
  );
};

export default RefundTracker;
