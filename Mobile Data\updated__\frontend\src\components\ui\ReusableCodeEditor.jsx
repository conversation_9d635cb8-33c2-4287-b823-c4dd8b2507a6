import React, { useState } from "react";
import { EmbeddedCodeEditor } from "./index";

const ReusableCodeEditor = ({ showPremiumOverlay, challenges: customChallenges }) => {
  const [selectedChallenge, setSelectedChallenge] = useState(null);
  const [code, setCode] = useState("");
  const [isRunning, setIsRunning] = useState(false);
  const [output, setOutput] = useState("");
  const [showOutput, setShowOutput] = useState(false);

  // Default challenges that can be customized by each course
  const defaultChallenges = [
    {
      id: "challenge-1",
      title: "Sum of Two Numbers",
      difficulty: "Beginner",
      description: "Write a function that returns the sum of two numbers.",
      requirements: [
        "Implement a function that takes two numbers as input",
        "Return their sum"
      ],
      template: `// Sum of Two Numbers\n// Implement the following function\n\nfunction solution(a, b) {\n  // TODO: Implement your solution here\n  \n  return a + b;\n}`,
      testCases: [
        { input: '1, 2', output: '3' },
        { input: '10, 5', output: '15' },
        { input: '-1, 1', output: '0' }
      ]
    },
    {
      id: "challenge-2",
      title: "Find Maximum in Array",
      difficulty: "Intermediate",
      description: "Write a function that returns the maximum number in an array.",
      requirements: [
        "Implement a function that takes an array of numbers",
        "Return the largest number in the array"
      ],
      template: `// Find Maximum in Array\n// Implement an efficient solution\n\nfunction solution(arr) {\n  // TODO: Implement your solution here\n  \n  return Math.max(...arr);\n}`,
      testCases: [
        { input: '[1, 2, 3, 4, 5]', output: '5' },
        { input: '[-10, 0, 10, 7]', output: '10' },
        { input: '[99, 23, 44]', output: '99' }
      ]
    }
  ];

  const [challenges, setChallenges] = useState(customChallenges || defaultChallenges);

  const handleCodeChange = (newCode) => {
    setCode(newCode);
  };

  const handleSelectChallenge = (challenge) => {
    setSelectedChallenge(challenge);
    setCode(challenge.template);
    setOutput("");
    setShowOutput(false);
  };
  
  const handleRunCode = () => {
    if (isRunning) return;
    
    setIsRunning(true);
    setShowOutput(true);
    
    // Simulate code execution with a delay
    setTimeout(() => {
      // Generate a simple output based on the code
      const outputText = `> Running ${selectedChallenge.id}...
> Executing code...
> Code executed successfully!

${selectedChallenge.id.includes('ai-') ? 'Python' : 'JavaScript'} output:
-------------------
Simulated output for ${selectedChallenge.title}
Your code has ${code.split('\n').length} lines
`;
      
      setOutput(outputText);
      setIsRunning(false);
    }, 1000);
  };
  
  const handleResetCode = () => {
    if (selectedChallenge) {
      setCode(selectedChallenge.template);
      setOutput("");
      setShowOutput(false);
    }
  };

  // CSS animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes slideInRight {
      from { transform: translateX(20px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5); }
      70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
      100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
    .slide-in {
      animation: slideInRight 0.5s forwards;
    }
    .pulse-animation {
      animation: pulse 2s infinite;
    }
    .code-container {
      transition: all 0.3s ease;
    }
    .editor-container {
      transition: all 0.5s ease-in-out;
    }
    .challenge-card {
      transition: all 0.2s ease;
    }
    .challenge-card:hover {
      transform: translateY(-2px);
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden mt-8">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1a3c50] to-[#010509] border-b border-white/10">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-2xl font-bold text-white mb-2">Interactive Code Editor</h2>
            <p className="text-gray-300">Practice coding with interactive challenges</p>
          </div>
          <div className="hidden md:flex items-center space-x-2">
            <div className="px-3 py-2 bg-[#303246]/60 backdrop-blur-lg rounded-lg border border-white/10 shadow-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full pulse-animation mr-2"></div>
                <span className="text-xs font-medium text-gray-300">Monaco Editor Ready</span>
              </div>
            </div>
            <div className="px-3 py-2 bg-[#303246]/60 backdrop-blur-lg rounded-lg border border-white/10 shadow-sm">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                <span className="text-xs font-medium text-gray-300">Auto-saving enabled</span>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 p-3 bg-[#303246]/30 backdrop-blur-lg border-l-4 border-blue-500/50 rounded">
          <p className="text-sm text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Select a challenge from below and start coding. Use the side panel to run your code and see results in the terminal.
          </p>
        </div>
      </div>

      <div className="flex flex-col h-[calc(100vh-250px)] min-h-[600px] sm:min-h-[700px]">
        {/* Challenges List - Now at the top */}
        <div className="w-full border-b border-white/10 bg-[#1e293b]/30 backdrop-blur-lg p-4">
          <h3 className="text-lg font-semibold mb-4 text-white">Challenges</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {challenges.map(challenge => (
              <div
                key={challenge.id}
                className={`p-3 rounded-lg cursor-pointer transition-all challenge-card ${
                  selectedChallenge && selectedChallenge.id === challenge.id
                    ? "bg-blue-900/50 border-l-4 border-blue-500/50 shadow-md"
                    : "bg-[#303246]/60 hover:bg-[#303246]/80 border border-white/10"
                }`}
                onClick={(e) => {
                  e.preventDefault();
                  handleSelectChallenge(challenge);
                }}
              >
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-white">{challenge.title}</h4>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    challenge.difficulty === "Beginner" 
                      ? "bg-green-900/50 text-green-300 border border-green-500/30" 
                      : challenge.difficulty === "Intermediate"
                        ? "bg-yellow-900/50 text-yellow-300 border border-yellow-500/30"
                        : "bg-red-900/50 text-red-300 border border-red-500/30"
                  }`}>
                    {challenge.difficulty}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Code Editor Area - Now below challenges */}
        <div className="flex-1 flex flex-col">
          {selectedChallenge ? (
            <div className="flex-1 flex flex-col h-full">
              <div className="flex flex-1 min-h-0">
                {/* Left: Challenge Details */}
                <div className="w-80 min-w-[260px] max-w-[320px] border-r border-white/10 bg-[#1e293b]/40 p-5 flex flex-col gap-4">
                  <h3 className="text-lg font-bold text-white mb-2">{selectedChallenge.title}</h3>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-semibold mb-2 ${
                    selectedChallenge.difficulty === "Beginner" 
                      ? "bg-green-900/50 text-green-300 border border-green-500/30" 
                      : selectedChallenge.difficulty === "Intermediate"
                        ? "bg-yellow-900/50 text-yellow-300 border border-yellow-500/30"
                        : "bg-red-900/50 text-red-300 border border-red-500/30"
                  }`}>
                    {selectedChallenge.difficulty}
                  </span>
                  <div className="text-gray-300 text-sm mb-2">
                    {selectedChallenge.description}
                  </div>
                  <div>
                    <h4 className="font-semibold text-xs text-blue-300 mb-1">Requirements</h4>
                    <ul className="list-disc list-inside text-xs text-gray-200 space-y-1">
                      {selectedChallenge.requirements && selectedChallenge.requirements.map((req, i) => (
                        <li key={i}>{req}</li>
                      ))}
                    </ul>
                  </div>
                  {/* Real test cases for the selected challenge */}
                  <div className="mt-4">
                    <h4 className="font-semibold text-xs text-blue-300 mb-1">Test Cases</h4>
                    <ul className="list-disc list-inside text-xs text-gray-200 space-y-1">
                      {selectedChallenge.testCases && selectedChallenge.testCases.length > 0 ? (
                        selectedChallenge.testCases.map((tc, idx) => (
                          <li key={idx}>
                            Input: <span className="font-mono">{tc.input}</span> → Output: <span className="font-mono">{tc.output}</span>
                          </li>
                        ))
                      ) : (
                        <li>No test cases available.</li>
                      )}
                    </ul>
                  </div>
                </div>
                {/* Center: Code Editor + Output stacked vertically */}
                <div className="flex-1 flex flex-col min-h-0">
                  {/* Code Editor */}
                  <div className="editor-container flex flex-col flex-1 min-h-0">
                    <div className="bg-[#1e293b]/50 text-white py-2 px-4 text-xs flex justify-between items-center">
                      <div className="flex items-center">
                        <span className="mr-2 font-medium">{selectedChallenge.title}</span>
                        <span className="px-2 py-1 bg-[#303246]/60 rounded text-xs">{selectedChallenge.id}.js</span>
                        <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                          selectedChallenge.difficulty === "Beginner" 
                            ? "bg-green-900/50 text-green-300 border border-green-500/30" 
                            : selectedChallenge.difficulty === "Intermediate"
                              ? "bg-yellow-900/50 text-yellow-300 border border-yellow-500/30"
                              : "bg-red-900/50 text-red-300 border border-red-500/30"
                        }`}>
                          {selectedChallenge.difficulty}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <div className="flex items-center">
                          <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                          <span className="text-xs text-gray-300">Auto-saved</span>
                        </div>
                      </div>
                    </div>
                    <div className="h-[calc(100%-32px)] flex-1 flex items-center justify-center min-h-0">
                      <EmbeddedCodeEditor
                        language={selectedChallenge.id.includes('ai-') ? "python" : "javascript"}
                        value={code}
                        onChange={handleCodeChange}
                        height="100%"
                        showSidebar={false}
                      />
                    </div>
                  </div>
                  {/* Output Section - Always visible below */}
                  <div className="bg-[#1e293b]/50 text-gray-100 border-t border-white/10 min-h-[120px] max-h-[220px] overflow-auto">
                    <div className="bg-[#1e293b]/70 py-2 px-4 text-xs flex justify-between items-center border-b border-white/10">
                      <div className="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <h4 className="text-sm font-semibold text-gray-300">Terminal Output</h4>
                      </div>
                      <div>
                        <span className="text-xs text-gray-400">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          Run code to see output
                        </span>
                      </div>
                    </div>
                    <div className="p-4">
                      {showOutput && output ? (
                        <div className="font-mono text-sm whitespace-pre-wrap text-green-400">
                          {output}
                          {isRunning && (
                            <span className="inline-block animate-pulse">▋</span>
                          )}
                        </div>
                      ) : (
                        <div className="h-full flex items-center justify-center text-gray-400">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          Run your code to see the results here
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                {/* Right: Side Control Panel */}
                <div className="w-48 border-l border-white/10 bg-[#1e293b]/30 backdrop-blur-lg flex flex-col">
                  <div className="p-3 border-b border-white/10">
                    <h4 className="text-sm font-semibold mb-3 text-gray-300">Actions</h4>
                    <div className="flex flex-col space-y-2">
                      <button 
                        className="w-full px-3 py-2 bg-[#303246]/60 backdrop-blur-lg text-white rounded-lg hover:bg-[#303246]/80 transition-colors border border-white/10 text-xs font-medium flex items-center justify-center"
                        onClick={handleRunCode}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Run Code
                      </button>
                      
                      <button 
                        className="w-full px-3 py-2 bg-[#303246]/40 text-gray-300 rounded-lg hover:bg-[#303246]/60 transition-colors text-xs flex items-center justify-center border border-white/10"
                        onClick={handleResetCode}
                        title="Reset code to original template"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Reset Code
                      </button>
                      
                      <button 
                        className="w-full px-3 py-2 bg-[#303246]/40 text-gray-300 rounded-lg hover:bg-[#303246]/60 transition-colors text-xs flex items-center justify-center border border-white/10"
                        onClick={showPremiumOverlay}
                        title="View documentation for this challenge"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        Docs
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-3">
                    <h4 className="text-sm font-semibold mb-2 text-gray-300">Challenge Info</h4>
                    <div className="text-xs text-gray-400">
                      <p><span className="font-medium">Difficulty:</span> {selectedChallenge.difficulty}</p>
                      <p className="mt-2"><span className="font-medium">Description:</span> {selectedChallenge.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-[#1e293b]/30 backdrop-blur-lg p-8 text-center">
              <div>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
                <h3 className="text-xl font-medium text-gray-300">Select a coding challenge above to begin</h3>
                <p className="mt-2 text-gray-400 max-w-md">Choose from the available challenges to practice your coding skills.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReusableCodeEditor;