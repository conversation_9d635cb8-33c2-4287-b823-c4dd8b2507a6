import { motion } from "framer-motion";
import { AnimatedBackground } from "../ui";
import ReusableNavbar from "./ReusableNavbar";
import { MainContentWrapper } from "./index";

const HomepageLayout = ({ toggleSidebar }) => {
  const dummyToggleSidebar = () => {};

  return (
    <div className="bg-gray-900">
      {/* <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden"
    >
         </motion.div> */}
      {/* <AnimatedBackground /> */}

      <ReusableNavbar
        toggleSidebar={dummyToggleSidebar}
        title="Upcoding"
        showSidebarToggle={false}
        isSidebarOpen={false}
      />

      <MainContentWrapper isSidebarOpen={false} toggleSidebar={toggleSidebar} />

      {/* <h1>sfsfs</h1> */}
    </div>
  );
};

export default HomepageLayout;
