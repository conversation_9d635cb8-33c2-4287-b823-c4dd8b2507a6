import React, { useState } from 'react';

const ApiTester = ({ isDarkMode }) => {
  const [apiUrl, setApiUrl] = useState('https://jsonplaceholder.typicode.com/posts/1');
  const [apiMethod, setApiMethod] = useState('GET');
  const [apiHeaders, setApiHeaders] = useState('{"Content-Type": "application/json"}');
  const [apiBody, setApiBody] = useState('');
  const [apiResponse, setApiResponse] = useState('');
  const [isApiLoading, setIsApiLoading] = useState(false);

  const testApi = async () => {
    setIsApiLoading(true);
    try {
      const headers = JSON.parse(apiHeaders);
      const options = {
        method: apiMethod,
        headers
      };
      
      if (apiMethod !== 'GET' && apiBody) {
        options.body = apiBody;
      }
      
      const response = await fetch(apiUrl, options);
      const data = await response.text();
      
      setApiResponse(`Status: ${response.status} ${response.statusText}\n\nResponse:\n${data}`);
    } catch (error) {
      setApiResponse(`Error: ${error.message}`);
    }
    setIsApiLoading(false);
  };

  return (
    <div className={`w-80 border-l ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'} flex flex-col`}>
      <div className={`p-3 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <h3 className="text-sm font-semibold">API Tester</h3>
      </div>
      
      <div className="flex-1 overflow-auto p-3 space-y-3">
        {/* URL Input */}
        <div>
          <label className="block text-xs font-medium mb-1">URL</label>
          <input
            type="text"
            value={apiUrl}
            onChange={(e) => setApiUrl(e.target.value)}
            className={`w-full px-2 py-1 text-xs rounded border ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:ring-1 focus:ring-blue-500`}
            placeholder="https://api.example.com/endpoint"
          />
        </div>

        {/* Method Selector */}
        <div>
          <label className="block text-xs font-medium mb-1">Method</label>
          <select
            value={apiMethod}
            onChange={(e) => setApiMethod(e.target.value)}
            className={`w-full px-2 py-1 text-xs rounded border ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:ring-1 focus:ring-blue-500`}
          >
            <option value="GET">GET</option>
            <option value="POST">POST</option>
            <option value="PUT">PUT</option>
            <option value="DELETE">DELETE</option>
            <option value="PATCH">PATCH</option>
          </select>
        </div>

        {/* Headers */}
        <div>
          <label className="block text-xs font-medium mb-1">Headers (JSON)</label>
          <textarea
            value={apiHeaders}
            onChange={(e) => setApiHeaders(e.target.value)}
            className={`w-full px-2 py-1 text-xs rounded border h-16 resize-none ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            } focus:ring-1 focus:ring-blue-500`}
            placeholder='{"Content-Type": "application/json"}'
          />
        </div>

        {/* Body */}
        {apiMethod !== 'GET' && (
          <div>
            <label className="block text-xs font-medium mb-1">Body</label>
            <textarea
              value={apiBody}
              onChange={(e) => setApiBody(e.target.value)}
              className={`w-full px-2 py-1 text-xs rounded border h-20 resize-none ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:ring-1 focus:ring-blue-500`}
              placeholder='{"key": "value"}'
            />
          </div>
        )}

        {/* Test Button */}
        <button
          onClick={testApi}
          disabled={isApiLoading}
          className={`w-full py-2 px-3 text-xs rounded font-medium transition-colors ${
            isApiLoading
              ? 'bg-gray-500 text-white cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isApiLoading ? 'Testing...' : 'Test API'}
        </button>

        {/* Response */}
        {apiResponse && (
          <div>
            <label className="block text-xs font-medium mb-1">Response</label>
            <pre className={`w-full px-2 py-1 text-xs rounded border h-32 overflow-auto ${
              isDarkMode
                ? 'bg-gray-900 border-gray-600 text-green-400'
                : 'bg-gray-50 border-gray-300 text-gray-800'
            }`}>
              {apiResponse}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiTester;
