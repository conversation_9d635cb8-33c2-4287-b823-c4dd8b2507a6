import React, { useState } from "react";
import { EmbeddedCodeEditor } from "../../../components/ui"; // Using the simpler embedded version

const LabEnvironmentNew = ({ showPremiumOverlay }) => {
  const [selectedChallenge, setSelectedChallenge] = useState(null);
  const [code, setCode] = useState("");
  const [isRunning, setIsRunning] = useState(false);
  const [output, setOutput] = useState("");
  const [showOutput, setShowOutput] = useState(false);

  const challenges = [
    {
      id: "url-shortener",
      title: "URL Shortener API",
      difficulty: "Beginner",
      description: "Design and implement a basic API for a URL shortening service like TinyURL or bit.ly.",
      requirements: [
        "Create endpoint to shorten long URLs",
        "Create endpoint to redirect from short URLs",
        "Use efficient hashing algorithm",
        "Handle collisions appropriately"
      ],
      template: `// URL Shortener API Implementation
// Implement the following functions

function generateShortUrl(longUrl) {
  // TODO: Implement hash generation
  
}

function storeMappingInDatabase(shortUrl, longUrl) {
  // TODO: Store in database
  
}

function getLongUrlFromShort(shortUrl) {
  // TODO: Retrieve from database
  
}

// API Endpoints
app.post('/shorten', (req, res) => {
  // TODO: Implement shortening endpoint
  
});

app.get('/:shortUrl', (req, res) => {
  // TODO: Implement redirect endpoint
  
});`
    },
    {
      id: "rate-limiter",
      title: "Rate Limiter",
      difficulty: "Intermediate",
      description: "Design and implement a rate limiting system to prevent abuse of your API endpoints.",
      requirements: [
        "Implement token bucket algorithm",
        "Support different limits for different users",
        "Track requests per user/IP",
        "Return appropriate HTTP status codes"
      ],
      template: `// Rate Limiter Implementation
// Implement the following functions

class RateLimiter {
  constructor(maxRequests, timeWindow) {
    // TODO: Initialize rate limiter
    
  }
  
  allowRequest(userId) {
    // TODO: Check if request is allowed
    
  }
  
  resetQuota(userId) {
    // TODO: Reset quota for user
    
  }
}

// Middleware Implementation
function rateLimitMiddleware(req, res, next) {
  // TODO: Implement middleware
  
}`
    },
    {
      id: "cache",
      title: "Caching System",
      difficulty: "Intermediate",
      description: "Design an LRU (Least Recently Used) cache for a web application.",
      requirements: [
        "Implement get and put operations in O(1) time",
        "Automatically remove least recently used items when cache is full",
        "Support cache size configuration",
        "Handle cache invalidation"
      ],
      template: `// LRU Cache Implementation
class LRUCache {
  constructor(capacity) {
    // TODO: Initialize cache structure
    
  }
  
  get(key) {
    // TODO: Get item from cache and mark as recently used
    
  }
  
  put(key, value) {
    // TODO: Add or update item and remove LRU if needed
    
  }
  
  invalidate(key) {
    // TODO: Remove item from cache
    
  }
}`
    },
    {
      id: "load-balancer",
      title: "Load Balancer",
      difficulty: "Advanced",
      description: "Design a simple load balancing algorithm to distribute requests across multiple servers.",
      requirements: [
        "Implement round-robin algorithm",
        "Add weighted distribution based on server capacity",
        "Add health checking for servers",
        "Handle server addition/removal dynamically"
      ],
      template: `// Load Balancer Implementation
class LoadBalancer {
  constructor(servers) {
    // TODO: Initialize with array of server objects
    
  }
  
  nextServer() {
    // TODO: Return next server based on algorithm
    
  }
  
  markServerDown(serverId) {
    // TODO: Mark server as unavailable
    
  }
  
  markServerUp(serverId) {
    // TODO: Mark server as available
    
  }
  
  addServer(server) {
    // TODO: Add new server to pool
    
  }
  
  removeServer(serverId) {
    // TODO: Remove server from pool
    
  }
}`
    }
  ];

  const handleCodeChange = (newCode) => {
    setCode(newCode);
  };

  const handleSelectChallenge = (challenge) => {
    setSelectedChallenge(challenge);
    setCode(challenge.template);
    setOutput("");
    setShowOutput(false);
  };
  
  const handleRunCode = () => {
    if (isRunning) return;
    
    // Show premium modal instead of running code
    showPremiumOverlay();
  };
  
  const handleResetCode = () => {
    if (selectedChallenge) {
      setCode(selectedChallenge.template);
      setOutput("");
      setShowOutput(false);
    }
  };

  // CSS animation styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes slideInRight {
      from { transform: translateX(20px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5); }
      70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
      100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
    }
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
    .slide-in {
      animation: slideInRight 0.5s forwards;
    }
    .pulse-animation {
      animation: pulse 2s infinite;
    }
    .code-container {
      transition: all 0.3s ease;
    }
    .editor-container {
      transition: all 0.5s ease-in-out;
    }
    .challenge-card {
      transition: all 0.2s ease;
    }
    .challenge-card:hover {
      transform: translateY(-2px);
    }
  `;

  return (
    <div className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="p-6 bg-gradient-to-r from-[#1a3c50] to-[#010509] border-b border-white/10">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-3xl font-bold text-white mb-2">Lab Environment</h2>
            <p className="text-gray-300">Practice system design concepts with interactive coding challenges</p>
          </div>
          <div className="hidden md:flex items-center space-x-2">
            <div className="px-3 py-2 bg-[#303246]/60 backdrop-blur-lg rounded-lg border border-white/10 shadow-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-full pulse-animation mr-2"></div>
                <span className="text-xs font-medium text-gray-300">Monaco Editor Ready</span>
              </div>
            </div>
            <div className="px-3 py-2 bg-[#303246]/60 backdrop-blur-lg rounded-lg border border-white/10 shadow-sm">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                <span className="text-xs font-medium text-gray-300">Auto-saving enabled</span>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 p-3 bg-[#303246]/30 backdrop-blur-lg border-l-4 border-blue-500/50 rounded">
          <p className="text-sm text-gray-300">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Select a challenge from below and start coding. Use the side panel to run your code and see results in the terminal.
          </p>
        </div>
      </div>

      <div className="flex flex-col h-[calc(100vh-250px)] min-h-[600px] sm:min-h-[700px]">
        {/* Challenges List - Now at the top */}
        <div className="w-full border-b border-white/10 bg-[#1e293b]/30 backdrop-blur-lg p-4">
          <h3 className="text-lg font-semibold mb-4 text-white">Challenges</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {challenges.map(challenge => (
              <div
                key={challenge.id}
                className={`p-3 rounded-lg cursor-pointer transition-all challenge-card ${
                  selectedChallenge && selectedChallenge.id === challenge.id
                    ? "bg-blue-900/50 border-l-4 border-blue-500/50 shadow-md"
                    : "bg-[#303246]/60 hover:bg-[#303246]/80 border border-white/10"
                }`}
                onClick={(e) => {
                  e.preventDefault();
                  handleSelectChallenge(challenge);
                }}
              >
                <div className="flex justify-between items-center">
                  <h4 className="font-medium text-white">{challenge.title}</h4>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    challenge.difficulty === "Beginner" 
                      ? "bg-green-900/50 text-green-300 border border-green-500/30" 
                      : challenge.difficulty === "Intermediate"
                        ? "bg-yellow-900/50 text-yellow-300 border border-yellow-500/30"
                        : "bg-red-900/50 text-red-300 border border-red-500/30"
                  }`}>
                    {challenge.difficulty}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Code Editor Area - Now below challenges */}
        <div className="flex-1 flex flex-col">
          {selectedChallenge ? (
            <div className="flex-1 flex flex-col h-full">
              <div className="flex h-[calc(100%-160px)]">
                {/* Main Editor Section */}
                <div className="flex-1 editor-container flex flex-col">
                  <div className="bg-[#1e293b]/50 text-white py-2 px-4 text-xs flex justify-between items-center">
                    <div className="flex items-center">
                      <span className="mr-2 font-medium">{selectedChallenge.title}</span>
                      <span className="px-2 py-1 bg-[#303246]/60 rounded text-xs">{selectedChallenge.id}.js</span>
                      <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                        selectedChallenge.difficulty === "Beginner" 
                          ? "bg-green-900/50 text-green-300 border border-green-500/30" 
                          : selectedChallenge.difficulty === "Intermediate"
                            ? "bg-yellow-900/50 text-yellow-300 border border-yellow-500/30"
                            : "bg-red-900/50 text-red-300 border border-red-500/30"
                      }`}>
                        {selectedChallenge.difficulty}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                        <span className="text-xs text-gray-300">Auto-saved</span>
                      </div>
                    </div>
                  </div>
                  <div className="h-[calc(100%-32px)] flex-1 flex items-center justify-center">
                    <EmbeddedCodeEditor
                      language="javascript"
                      value={code}
                      onChange={handleCodeChange}
                      height="100%"
                      showSidebar={false}
                    />
                  </div>
                </div>
                
                {/* Side Control Panel */}
                <div className="w-48 border-l border-white/10 bg-[#1e293b]/30 backdrop-blur-lg flex flex-col">
                  <div className="p-3 border-b border-white/10">
                    <h4 className="text-sm font-semibold mb-3 text-gray-300">Actions</h4>
                    <div className="flex flex-col space-y-2">
                      <button 
                        className="w-full px-3 py-2 bg-[#303246]/60 backdrop-blur-lg text-white rounded-lg hover:bg-[#303246]/80 transition-colors border border-white/10 text-xs font-medium flex items-center justify-center"
                        onClick={handleRunCode}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Run Code
                      </button>
                      
                      <button 
                        className="w-full px-3 py-2 bg-[#303246]/40 text-gray-300 rounded-lg hover:bg-[#303246]/60 transition-colors text-xs flex items-center justify-center border border-white/10"
                        onClick={handleResetCode}
                        title="Reset code to original template"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Reset Code
                      </button>
                      
                      <button 
                        className="w-full px-3 py-2 bg-[#303246]/40 text-gray-300 rounded-lg hover:bg-[#303246]/60 transition-colors text-xs flex items-center justify-center border border-white/10"
                        onClick={showPremiumOverlay}
                        title="View documentation for this challenge"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                        Docs
                      </button>
                    </div>
                  </div>
                  
                  <div className="p-3">
                    <h4 className="text-sm font-semibold mb-2 text-gray-300">Challenge Info</h4>
                    <div className="text-xs text-gray-400">
                      <p><span className="font-medium">Difficulty:</span> {selectedChallenge.difficulty}</p>
                      <p className="mt-2"><span className="font-medium">Focus:</span> {
                        selectedChallenge.id === "url-shortener" 
                          ? "URL hashing & API design"
                          : selectedChallenge.id === "rate-limiter" 
                            ? "Request throttling"
                            : selectedChallenge.id === "cache" 
                              ? "Memory management"
                              : "Load distribution"
                      }</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Output Section - Always visible below */}
              <div className="h-40 border-t border-white/10 overflow-auto bg-[#1e293b]/50 text-gray-100">
                <div className="bg-[#1e293b]/70 py-2 px-4 text-xs flex justify-between items-center border-b border-white/10">
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <h4 className="text-sm font-semibold text-gray-300">Terminal Output</h4>
                  </div>
                  <div>
                    <span className="text-xs text-gray-400">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Run code to see output
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <div className="h-full flex items-center justify-center text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Run your code to see the results here
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-[#1e293b]/30 backdrop-blur-lg p-8 text-center">
              <div>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
                <h3 className="text-xl font-medium text-gray-300">Select a coding challenge above to begin</h3>
                <p className="mt-2 text-gray-400 max-w-md">Choose from URL shorteners, rate limiters, caching systems, and more to practice your system design skills.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LabEnvironmentNew;