export const chapterData = [
  {
    _id: "ai-ch1",
    title: "Fundamentals of Artificial Intelligence",
    description: "Explore the core concepts and history of artificial intelligence. This chapter introduces AI foundations, philosophical questions, and the evolution of AI technologies from early rule-based systems to modern approaches.",
    difficulty: 1,
    tags: ["basics", "history", "concepts"]
  },
  {
    _id: "ai-ch2",
    title: "Machine Learning Algorithms",
    description: "Understand different types of machine learning including supervised, unsupervised, and reinforcement learning. Learn about decision trees, linear regression, logistic regression, and k-means clustering algorithms.",
    difficulty: 3,
    tags: ["algorithms", "supervised learning", "unsupervised learning"]
  },
  {
    _id: "ai-ch3",
    title: "Deep Learning and Neural Networks",
    description: "Dive into the architecture and operation of neural networks. Learn about activation functions, backpropagation, convolutional neural networks (CNNs), recurrent neural networks (RNNs), and transformer models.",
    difficulty: 4,
    tags: ["neural networks", "CNN", "RNN"]
  },
  {
    _id: "ai-ch4",
    title: "Natural Language Processing",
    description: "Explore how computers understand, interpret, and generate human language. Study text preprocessing, tokenization, part-of-speech tagging, named entity recognition, and sentiment analysis techniques.",
    difficulty: 4,
    tags: ["NLP", "language", "text processing"]
  },
  {
    _id: "ai-ch5",
    title: "Computer Vision",
    description: "Learn how machines interpret and understand visual information. This chapter covers image processing, feature extraction, object detection, image segmentation, and facial recognition systems.",
    difficulty: 4,
    tags: ["vision", "image processing", "object detection"]
  },
  {
    _id: "ai-ch6",
    title: "AI Ethics and Responsible AI",
    description: "Understand the ethical implications and responsible development of AI systems. Learn about bias in AI, fairness, transparency, privacy concerns, and governance frameworks for ethical AI deployment.",
    difficulty: 3,
    tags: ["ethics", "responsible AI", "bias"]
  },
  {
    _id: "ai-ch7",
    title: "AI in Robotics and Automation",
    description: "Explore how AI powers modern robotics and automation systems. This chapter covers robot perception, path planning, manipulation, human-robot interaction, and industrial applications of AI-driven robotics.",
    difficulty: 4,
    tags: ["robotics", "automation", "control systems"]
  },
  {
    _id: "ai-ch8",
    title: "Generative AI and Large Language Models",
    description: "Understand the cutting-edge technology behind generative AI systems like GPT and DALL-E. Learn about transformer architectures, attention mechanisms, prompt engineering, and fine-tuning techniques.",
    difficulty: 5,
    tags: ["generative AI", "LLMs", "transformers"]
  }
];
