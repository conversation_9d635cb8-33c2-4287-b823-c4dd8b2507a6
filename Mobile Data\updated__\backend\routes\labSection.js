import express from "express";
import { isAuthenticated } from "../middleware/auth.js";
import { createLabSection, getAllLabSections, updateLabSection } from "../controllers/labSection.js";
import { upload } from "../utils/multerConfig.js";

const labSectionRouter = express.Router();

labSectionRouter.post(
  "/create",
  isAuthenticated,
  upload.single("icon"),
  createLabSection
);

labSectionRouter.put(
  "/update/:id",
  isAuthenticated,
  upload.single("icon"),
  updateLabSection
);

labSectionRouter.get(
  "/all/:labId",
  isAuthenticated,
  getAllLabSections
);


export default labSectionRouter;
