import { motion } from "framer-motion";
import { Footer } from "../layout";
import { Widgets } from "../ui";
import HeroSection from "../sections/HeroSection";
import CourseSection from "../sections/CourseSection";
import LearningPathsSection from "../sections/LearningPathsSection";
import { BackToTopButton } from "../sections/common/BackgroundEffects";

const Content = ({ toggleSidebar }) => {
  // const location = useLocation();

  const handleScrollToTop = (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  // const containerVariants = {
  //   hidden: { opacity: 0 },
  //   visible: {
  //     opacity: 1,
  //     transition: {
  //       staggerChildren: 0.2,
  //       delayChildren: 0.3,
  //     },
  //   },
  // };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0,
        ease: "easeOut",
      },
    },
  };

  return (
    <div>
      {/* Hero Section */}
      <HeroSection itemVariants={itemVariants} />

      {/* Main Content Sections */}
      {/* Background  bg-gradient-to-br from-[#1a3c50] to-[#010509] */}
      <div className=" ">
        {/* Courses Section */}
        <CourseSection  itemVariants={itemVariants} />

        {/* Learning Paths Section */}
        <LearningPathsSection itemVariants={itemVariants} />

        {/* Widgets */}
        <div variants={itemVariants} className="py-8 relative overflow-hidden">
          <div className="relative z-10 text-white">
            {/* isme v dikkat hai */}
            <Widgets />
          </div>
        </div>
      </div>

      {/* Footer */}
      <div
        // variants={itemVariants}
        className="bg-[#010509] border-t border-white/10"
      >
        <Footer />
      </div>

      {/* Back to Top Button */}
      <BackToTopButton handleScrollToTop={handleScrollToTop} />
      {/* </motion.div> */}
    </div>
  );
};

export default Content;
