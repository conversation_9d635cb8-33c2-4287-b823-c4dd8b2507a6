import { useState } from "react";
import { 
  FaUsers, 
  FaInfoCircle, 
  FaFileContract, 
  FaShieldAlt,
  FaHandshake,
  FaGlobe,
  FaHeart,
  FaRocket
} from "react-icons/fa";
import { motion } from "framer-motion";
import AboutUs from "./components/AboutUs";
import TermsAndConditions from "./components/TermsAndConditions";
import "./community.css";

const Community = () => {
  const [activeTab, setActiveTab] = useState("about");

  const tabs = [
    {
      id: "about",
      label: "About Us",
      icon: <FaInfoCircle />,
      component: AboutUs,
      color: "from-blue-600 to-cyan-600",
      description: "Learn about our mission and values"
    },
    {
      id: "terms",
      label: "Terms & Conditions",
      icon: <FaFileContract />,
      component: TermsAndConditions,
      color: "from-purple-600 to-pink-600",
      description: "Our terms of service and policies"
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 text-white">
      {/* Header Section */}
      <div className="relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute top-0 right-0 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative z-10 pt-20 pb-16 px-4 md:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto text-center">
            {/* Main Title */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-8"
            >
              <div className="flex items-center justify-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center">
                  <FaUsers className="text-3xl text-white" />
                </div>
                <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-cyan-200 bg-clip-text text-transparent">
                  Community
                </h1>
              </div>
              <p className="text-xl md:text-2xl text-slate-300 max-w-3xl mx-auto leading-relaxed">
                Welcome to our community hub - your gateway to understanding our mission, 
                values, and the terms that govern our platform
              </p>
            </motion.div>

            {/* Stats Cards */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12"
            >
              <div className="bg-gradient-to-br from-blue-900/60 via-blue-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-blue-500/30 hover:border-blue-400/50 transition-all duration-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-200 text-sm font-semibold">Community</p>
                    <p className="text-2xl font-bold text-white">10K+</p>
                  </div>
                  <FaUsers className="text-blue-400 text-2xl" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-900/60 via-green-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-green-500/30 hover:border-green-400/50 transition-all duration-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-200 text-sm font-semibold">Trust</p>
                    <p className="text-2xl font-bold text-white">100%</p>
                  </div>
                  <FaShieldAlt className="text-green-400 text-2xl" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-900/60 via-purple-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-purple-500/30 hover:border-purple-400/50 transition-all duration-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-200 text-sm font-semibold">Global</p>
                    <p className="text-2xl font-bold text-white">50+</p>
                  </div>
                  <FaGlobe className="text-purple-400 text-2xl" />
                </div>
              </div>

              <div className="bg-gradient-to-br from-pink-900/60 via-pink-800/70 to-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-pink-500/30 hover:border-pink-400/50 transition-all duration-500">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-pink-200 text-sm font-semibold">Support</p>
                    <p className="text-2xl font-bold text-white">24/7</p>
                  </div>
                  <FaHeart className="text-pink-400 text-2xl" />
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="sticky top-0 z-40 bg-slate-900/80 backdrop-blur-xl border-b border-slate-700/50">
        <div className="max-w-6xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="flex justify-center">
            <div className="flex bg-slate-800/50 rounded-2xl p-2 m-4">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`relative flex items-center gap-3 px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                    activeTab === tab.id
                      ? `bg-gradient-to-r ${tab.color} text-white shadow-lg transform scale-105`
                      : "text-slate-300 hover:text-white hover:bg-slate-700/50"
                  }`}
                >
                  <span className="text-lg">{tab.icon}</span>
                  <div className="text-left">
                    <div className="font-bold">{tab.label}</div>
                    <div className="text-xs opacity-75 hidden md:block">{tab.description}</div>
                  </div>
                  
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="activeTab"
                      className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 rounded-xl"
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="relative z-10">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="min-h-screen"
        >
          {ActiveComponent && <ActiveComponent />}
        </motion.div>
      </div>

      {/* Footer CTA */}
      <div className="relative z-10 py-16 px-4 md:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="bg-gradient-to-br from-slate-800/90 via-blue-900/80 to-purple-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500">
            <div className="flex items-center justify-center gap-3 mb-6">
              <FaRocket className="text-4xl text-blue-400" />
              <h2 className="text-3xl font-bold text-white">Ready to Get Started?</h2>
            </div>
            <p className="text-slate-300 text-lg mb-8 max-w-2xl mx-auto">
              Join our community of learners and developers. Start your journey with us today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold">
                Join Community
              </button>
              <button className="px-8 py-4 bg-slate-700/50 hover:bg-slate-600/50 text-white rounded-xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300 font-semibold">
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Community;
