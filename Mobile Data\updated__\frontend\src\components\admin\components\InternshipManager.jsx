import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, X, Search, Calendar, MapPin, Building, Users, Clock, DollarSign, Eye, EyeOff } from 'lucide-react';

const InternshipManager = ({ theme }) => {
  // Enhanced internship data based on internshipModal.js
  const [internships, setInternships] = useState([
    {
      _id: '507f1f77bcf86cd799439011',
      title: 'Full Stack Developer Intern',
      company: 'TechCorp Solutions',
      location: 'San Francisco, CA',
      type: 'remote',
      duration: '3 months',
      stipend: 2500,
      description: 'Join our dynamic team as a Full Stack Developer Intern. You will work on cutting-edge web applications using React, Node.js, and MongoDB. This is an excellent opportunity to gain hands-on experience in a fast-paced startup environment.',
      requirements: [
        'Proficiency in JavaScript, HTML, CSS',
        'Experience with React.js and Node.js',
        'Understanding of database concepts',
        'Strong problem-solving skills',
        'Excellent communication skills'
      ],
      responsibilities: [
        'Develop and maintain web applications',
        'Collaborate with senior developers',
        'Participate in code reviews',
        'Write clean, maintainable code',
        'Assist in testing and debugging'
      ],
      skills: ['JavaScript', 'React', 'Node.js', 'MongoDB', 'Git'],
      applicationDeadline: '2024-02-15T23:59:59Z',
      startDate: '2024-03-01T09:00:00Z',
      endDate: '2024-06-01T17:00:00Z',
      isActive: true,
      featured: true,
      applicationsCount: 45,
      maxApplicants: 100,
      contactEmail: '<EMAIL>',
      contactPhone: '******-0123',
      benefits: ['Mentorship program', 'Flexible hours', 'Learning stipend', 'Certificate'],
      workingHours: '9 AM - 5 PM PST',
      createdAt: '2024-01-10T10:00:00Z',
      updatedAt: '2024-01-15T14:30:00Z'
    },
    {
      _id: '507f1f77bcf86cd799439012',
      title: 'Data Science Intern',
      company: 'DataInsights Inc',
      location: 'New York, NY',
      type: 'hybrid',
      duration: '6 months',
      stipend: 3000,
      description: 'Work with our data science team to analyze large datasets and build predictive models. You will gain experience with Python, machine learning algorithms, and data visualization tools.',
      requirements: [
        'Strong background in statistics and mathematics',
        'Proficiency in Python and SQL',
        'Experience with pandas, numpy, scikit-learn',
        'Knowledge of data visualization tools',
        'Currently pursuing degree in related field'
      ],
      responsibilities: [
        'Analyze complex datasets',
        'Build and validate machine learning models',
        'Create data visualizations and reports',
        'Collaborate with cross-functional teams',
        'Present findings to stakeholders'
      ],
      skills: ['Python', 'SQL', 'Machine Learning', 'Pandas', 'Matplotlib', 'Tableau'],
      applicationDeadline: '2024-02-20T23:59:59Z',
      startDate: '2024-03-15T09:00:00Z',
      endDate: '2024-09-15T17:00:00Z',
      isActive: true,
      featured: false,
      applicationsCount: 32,
      maxApplicants: 50,
      contactEmail: '<EMAIL>',
      contactPhone: '******-0456',
      benefits: ['Health insurance', 'Gym membership', 'Free lunch', 'Conference attendance'],
      workingHours: '10 AM - 6 PM EST',
      createdAt: '2024-01-12T11:30:00Z',
      updatedAt: '2024-01-14T16:45:00Z'
    },
    {
      _id: '507f1f77bcf86cd799439013',
      title: 'Mobile App Developer Intern',
      company: 'MobileFirst Studios',
      location: 'Austin, TX',
      type: 'onsite',
      duration: '4 months',
      stipend: 2200,
      description: 'Develop mobile applications for iOS and Android platforms. Work with experienced developers to create user-friendly and performant mobile apps using React Native and Flutter.',
      requirements: [
        'Experience with React Native or Flutter',
        'Understanding of mobile app development lifecycle',
        'Knowledge of JavaScript/Dart',
        'Familiarity with mobile UI/UX principles',
        'Portfolio of mobile projects'
      ],
      responsibilities: [
        'Develop cross-platform mobile applications',
        'Implement responsive UI designs',
        'Integrate APIs and third-party services',
        'Test applications on various devices',
        'Optimize app performance'
      ],
      skills: ['React Native', 'Flutter', 'JavaScript', 'Dart', 'Mobile UI/UX'],
      applicationDeadline: '2024-02-10T23:59:59Z',
      startDate: '2024-02-26T09:00:00Z',
      endDate: '2024-06-26T17:00:00Z',
      isActive: false,
      featured: false,
      applicationsCount: 28,
      maxApplicants: 30,
      contactEmail: '<EMAIL>',
      contactPhone: '******-0789',
      benefits: ['Flexible schedule', 'Device allowance', 'Team events', 'Mentorship'],
      workingHours: '9 AM - 5 PM CST',
      createdAt: '2024-01-08T09:15:00Z',
      updatedAt: '2024-01-13T12:20:00Z'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingInternship, setEditingInternship] = useState(null);
  const [selectedInternship, setSelectedInternship] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    company: '',
    location: '',
    type: 'remote',
    duration: '',
    stipend: 0,
    description: '',
    requirements: [],
    responsibilities: [],
    skills: [],
    applicationDeadline: '',
    startDate: '',
    endDate: '',
    isActive: true,
    featured: false,
    maxApplicants: 50,
    contactEmail: '',
    contactPhone: '',
    benefits: [],
    workingHours: ''
  });

  // Filter internships
  const filteredInternships = internships.filter(internship => {
    const matchesSearch = internship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         internship.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         internship.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         internship.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = filterType === '' || internship.type === filterType;
    const matchesStatus = filterStatus === '' || 
      (filterStatus === 'active' && internship.isActive) ||
      (filterStatus === 'inactive' && !internship.isActive);
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const handleAdd = () => {
    setEditingInternship(null);
    setFormData({
      title: '',
      company: '',
      location: '',
      type: 'remote',
      duration: '',
      stipend: 0,
      description: '',
      requirements: [],
      responsibilities: [],
      skills: [],
      applicationDeadline: '',
      startDate: '',
      endDate: '',
      isActive: true,
      featured: false,
      maxApplicants: 50,
      contactEmail: '',
      contactPhone: '',
      benefits: [],
      workingHours: ''
    });
    setShowModal(true);
  };

  const handleEdit = (internship) => {
    setEditingInternship(internship);
    setFormData({
      title: internship.title,
      company: internship.company,
      location: internship.location,
      type: internship.type,
      duration: internship.duration,
      stipend: internship.stipend,
      description: internship.description,
      requirements: internship.requirements,
      responsibilities: internship.responsibilities,
      skills: internship.skills,
      applicationDeadline: internship.applicationDeadline.split('T')[0],
      startDate: internship.startDate.split('T')[0],
      endDate: internship.endDate.split('T')[0],
      isActive: internship.isActive,
      featured: internship.featured,
      maxApplicants: internship.maxApplicants,
      contactEmail: internship.contactEmail,
      contactPhone: internship.contactPhone,
      benefits: internship.benefits,
      workingHours: internship.workingHours
    });
    setShowModal(true);
  };

  const handleSave = () => {
    if (editingInternship) {
      setInternships(prev => prev.map(internship => 
        internship._id === editingInternship._id 
          ? { ...internship, ...formData, updatedAt: new Date().toISOString() }
          : internship
      ));
    } else {
      const newInternship = {
        _id: Date.now().toString(),
        ...formData,
        applicationsCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setInternships(prev => [...prev, newInternship]);
    }
    setShowModal(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this internship?')) {
      setInternships(prev => prev.filter(internship => internship._id !== id));
    }
  };

  const toggleStatus = (id) => {
    setInternships(prev => prev.map(internship => 
      internship._id === id 
        ? { ...internship, isActive: !internship.isActive, updatedAt: new Date().toISOString() }
        : internship
    ));
  };

  const handleViewDetails = (internship) => {
    setSelectedInternship(internship);
    setShowDetails(true);
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'remote': return 'bg-green-100 text-green-800';
      case 'onsite': return 'bg-blue-100 text-blue-800';
      case 'hybrid': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Internship Manager
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Comprehensive internship management based on internshipModal.js - Total: {internships.length} internships
          </p>
        </div>
        
        <button
          onClick={handleAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add Internship</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Search internships by title, company, location, or skills..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
              theme === 'dark'
                ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          />
        </div>
        
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className={`px-4 py-2 rounded-lg border ${
            theme === 'dark'
              ? 'bg-gray-800 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
        >
          <option value="">All Types</option>
          <option value="remote">Remote</option>
          <option value="onsite">Onsite</option>
          <option value="hybrid">Hybrid</option>
        </select>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className={`px-4 py-2 rounded-lg border ${
            theme === 'dark'
              ? 'bg-gray-800 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* Internship List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredInternships.length === 0 ? (
          <div className={`col-span-full text-center py-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
            No internships found matching your criteria.
          </div>
        ) : (
          filteredInternships.map((internship) => (
            <div
              key={internship._id}
              className={`rounded-lg border p-6 ${
                theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
              } ${internship.featured ? 'ring-2 ring-yellow-400' : ''}`}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className={`text-lg font-semibold ${
                      theme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      {internship.title}
                    </h3>
                    {internship.featured && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                        Featured
                      </span>
                    )}
                  </div>

                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center gap-1">
                      <Building size={14} />
                      <span>{internship.company}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin size={14} />
                      <span>{internship.location}</span>
                    </div>
                    <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getTypeColor(internship.type)}`}>
                      {internship.type}
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleViewDetails(internship)}
                    className={`p-2 rounded-lg transition-colors ${
                      theme === 'dark'
                        ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                    }`}
                    title="View Details"
                  >
                    <Eye size={16} />
                  </button>
                  <button
                    onClick={() => handleEdit(internship)}
                    className={`p-2 rounded-lg transition-colors ${
                      theme === 'dark'
                        ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                        : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                    }`}
                    title="Edit"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDelete(internship._id)}
                    className={`p-2 rounded-lg transition-colors ${
                      theme === 'dark'
                        ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                        : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                    }`}
                    title="Delete"
                  >
                    <Trash2 size={16} />
                  </button>
                </div>
              </div>

              {/* Content */}
              <p className={`text-sm mb-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                {internship.description.substring(0, 150)}...
              </p>

              {/* Skills */}
              <div className="flex flex-wrap gap-1 mb-4">
                {internship.skills.slice(0, 4).map((skill, index) => (
                  <span key={index} className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                    theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'
                  }`}>
                    {skill}
                  </span>
                ))}
                {internship.skills.length > 4 && (
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                    theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'
                  }`}>
                    +{internship.skills.length - 4} more
                  </span>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Clock size={14} className="text-gray-400" />
                  <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    {internship.duration}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign size={14} className="text-gray-400" />
                  <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    ${internship.stipend}/month
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Users size={14} className="text-gray-400" />
                  <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    {internship.applicationsCount}/{internship.maxApplicants} applied
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar size={14} className="text-gray-400" />
                  <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    Deadline: {new Date(internship.applicationDeadline).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* Status */}
              <div className="flex items-center justify-between">
                <button
                  onClick={() => toggleStatus(internship._id)}
                  className={`px-3 py-1 text-xs rounded-full ${
                    internship.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}
                >
                  {internship.isActive ? 'Active' : 'Inactive'}
                </button>

                <span className={`text-xs ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                  Updated: {new Date(internship.updatedAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default InternshipManager;
