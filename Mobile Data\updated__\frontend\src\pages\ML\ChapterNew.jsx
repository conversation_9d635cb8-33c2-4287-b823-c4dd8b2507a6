import { useState } from "react";

const ChapterNew = () => {
  const [activeChapter, setActiveChapter] = useState(null);

  const toggleChapter = (index) => {
    setActiveChapter(activeChapter === index ? null : index);
  };

  const machineLearningChapters = [
    {
      title: "Introduction to Machine Learning",
      content: [
        "What is machine learning and how does it differ from traditional programming?",
        "Discuss the different types of machine learning: supervised, unsupervised, and reinforcement learning.",
      ],
    },
    {
      title: "Data Preprocessing",
      content: [
        "What are the key steps in preparing data for machine learning?",
        "How do you handle missing values and categorical data in datasets?",
      ],
    },
    {
      title: "Supervised Learning Algorithms",
      content: [
        "What are the core concepts of regression and classification?",
        "Explain common algorithms such as linear regression, decision trees, and support vector machines.",
      ],
    },
    {
      title: "Unsupervised Learning Techniques",
      content: [
        "What is clustering and how is it applied in machine learning?",
        "Discuss dimensionality reduction techniques like PCA and t-SNE.",
      ],
    },
    {
      title: "Model Evaluation and Selection",
      content: [
        "What metrics are used to evaluate model performance in machine learning?",
        "How do you perform cross-validation and why is it important?",
      ],
    },
    {
      title: "Feature Engineering",
      content: [
        "What is feature engineering and how does it impact model performance?",
        "Discuss techniques for selecting and creating effective features.",
      ],
    },
    {
      title: "Deep Learning Fundamentals",
      content: [
        "What are neural networks and how do they differ from traditional algorithms?",
        "Explain the architecture of deep learning models.",
      ],
    },
    {
      title: "Deployment and Productionization of Models",
      content: [
        "What are the best practices for deploying machine learning models into production?",
        "Discuss tools and platforms commonly used for model deployment.",
      ],
    },
  ];
  
  return (
    <div id="chapters" className="main-content bg-transparent backdrop-blur-lg border border-white/10 rounded-xl p-6">
      <h2 className="text-2xl font-bold text-white mb-6">
        Machine Learning Course
      </h2>
      
      <ul className="space-y-3">
        {machineLearningChapters.map((chapter, index) => (
          <li 
            key={index} 
            className="bg-[#1e293b]/30 backdrop-blur-lg rounded-xl border border-white/10 overflow-hidden"
          >
            <div
              className="flex justify-between items-center p-4 cursor-pointer hover:bg-white/5 transition-colors"
              onClick={() => toggleChapter(index)}
            >
              <span className="font-medium text-gray-200">{chapter.title}</span>
              <span className="text-blue-400">
                {activeChapter === index ? "▲" : "▼"}
              </span>
            </div>
            
            {activeChapter === index && (
              <div className="p-4 border-t border-white/10 bg-[#1e293b]/50">
                <ul className="space-y-2 text-gray-300">
                  {chapter.content.map((item, idx) => (
                    <li key={idx} className="flex items-start">
                      <span className="mr-2 text-blue-400">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ChapterNew;