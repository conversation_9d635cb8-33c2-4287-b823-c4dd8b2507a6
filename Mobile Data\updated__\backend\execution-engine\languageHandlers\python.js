import path from "path";
import fs from "fs";
import os from "os";
import crypto from "crypto";
import containerPool from "../execution/containerPool.js";

// Generate temporary .py file
const writeTempFile = (content, extension = ".py", fixedName = null) => {
  const filename = fixedName || `code-${crypto.randomUUID()}${extension}`;
  const fullPath = path.join(os.tmpdir(), filename);
  fs.writeFileSync(fullPath, content, "utf-8");
  return fullPath;
};

// Stream file to container
async function streamFile(container, hostPath, destName) {
  const data = fs.readFileSync(hostPath);

  const exec = await container.exec({
    Cmd: ["sh", "-c", `tee /app/${destName} > /dev/null`],
    AttachStdin: true,
    AttachStdout: true,
    AttachStderr: true,
  });

  const stream = await exec.start({ hijack: true, stdin: true });

  await new Promise((resolve, reject) => {
    stream.write(data);
    stream.end();
    stream.on("close", resolve);
    stream.on("error", reject);
  });

  await new Promise((res) => setTimeout(res, 50));

  const inspect = await exec.inspect();

  if (inspect.ExitCode !== 0) {
    throw new Error(`File streaming failed at /app/${destName}, exit code: ${inspect.ExitCode}`);
  }
}

// Python executor
export default {
  execute: async (codeContent, input = "", { timeout = 5000 } = {}) => {
    const codePath = writeTempFile(codeContent, ".py");
    const inputPath = input ? writeTempFile(input, ".txt", "input.txt") : null;
    const codeFile = path.basename(codePath);

    let containerObj;

    try {
      containerObj = await containerPool.getContainer("python");
      const c = containerObj.container;

      await streamFile(c, codePath, codeFile);
      if (inputPath) {
        await streamFile(c, inputPath, "input.txt");
      }

      const cmd = inputPath
        ? `python3 /app/${codeFile} < /app/input.txt`
        : `python3 /app/${codeFile}`;

      const exec = await c.exec({
        Cmd: ["sh", "-c", cmd],
        AttachStdout: true,
        AttachStderr: true,
      });

      const stream = await exec.start({ hijack: true, stdin: false });

      let out = "", err = "";

      await new Promise((res, rej) => {
        c.modem.demuxStream(
          stream,
          { write: (b) => { out += b.toString(); }},
          { write: (b) => { err += b.toString(); }}
        );
        stream.on("end", res);
        stream.on("error", rej);
      });

      return {
        output: out.trim(),
        error: err.trim() || null,
      };
    } catch (e) {
      return { output: "", error: e.message };
    } finally {
      try {
        if (fs.existsSync(codePath)) fs.unlinkSync(codePath);
        if (inputPath && fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
      } catch {}
      if (containerObj) containerPool.releaseContainer(containerObj);
    }
  },
};
