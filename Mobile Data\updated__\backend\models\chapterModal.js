import mongoose from "mongoose";

const resourceSchema = new mongoose.Schema({
  title: { type: String, required: true },
  url: { type: String, required: true },
  type: { type: String, enum: ["pdf", "video", "link"], required: true },
});

const chapterSchema = new mongoose.Schema(
  {
    title: { type: String, required: true, trim: true },
    description: { type: String },
    slug: { type: String, index: true },
    chapterNumber: { type: Number, index: true },

    labId: { type: mongoose.Schema.Types.ObjectId, ref: "Lab", required: true },
    labsectionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LabSection",
      required: true,
      index: true,
    },

    level: {
      type: String,
      enum: ["beginner", "intermediate", "advanced"],
      default: "beginner",
    },

    isFree: { type: Boolean, default: true },
    isActive: { type: Boolean, default: true }, 
    isDeleted: { type: Boolean, default: false }, 
    prerequisites: [{ type: mongoose.Schema.Types.ObjectId, ref: "Chapter" }],
    estimatedTime: { type: Number },

    thumbnail: {
      url: { type: String },
      public_id: { type: String },
    },

    resources: [resourceSchema],

    metaTitle: { type: String },
    metaDescription: { type: String },
    keywords: [{ type: String }],

    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    bookmarks: { type: Number, default: 0 },

    visibility: {
      type: String,
      enum: ["public", "private", "restricted"],
      default: "public",
    },
    rolesAllowed: [
      { type: String, enum: ["student", "teacher", "admin"], default: [] },
    ],

    status: {
      type: String,
      enum: ["draft", "published", "archived"],
      default: "draft",
    },

    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  },
  { timestamps: true }
);

chapterSchema.index({ labId: 1, slug: 1 }, { unique: true });
chapterSchema.index({ title: "text", description: "text", keywords: "text" });

const Chapter = mongoose.model("Chapter", chapterSchema);
export default Chapter;
