import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import axios from "axios";
import { useEffect, useState } from "react";

const CodingProblem = () => {
  const [questions, setQuestions] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [difficultyFilter, setDifficultyFilter] = useState("");
  const [topicFilter, setTopicFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 8;

  useEffect(() => {
    const fetchQuestions = async () => {
      try {
        // Use relative path for API calls instead of hardcoded localhost
        const res = await axios.get("/api/questions");
        setQuestions(res.data);
      } catch (err) {
        console.error("Error fetching questions:", err);
        // Enhanced fallback mock data with real problems
        setQuestions([
          {
            id: 1,
            title: "Two Sum",
            difficulty: "Easy",
            tags: ["Array", "Hash Table"],
            solved: true,
            acceptance: "49.2%",
            companies: ["Google", "Amazon", "Microsoft"],
            description:
              "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.",
            likes: 12543,
            dislikes: 423,
          },
          {
            id: 2,
            title: "Add Two Numbers",
            difficulty: "Medium",
            tags: ["Linked List", "Math"],
            solved: false,
            acceptance: "34.8%",
            companies: ["Microsoft", "Apple", "Facebook"],
            description:
              "You are given two non-empty linked lists representing two non-negative integers. Add the two numbers and return the sum as a linked list.",
            likes: 8934,
            dislikes: 1823,
          },
          {
            id: 3,
            title: "Longest Substring Without Repeating Characters",
            difficulty: "Medium",
            tags: ["Hash Table", "Sliding Window", "String"],
            solved: true,
            acceptance: "32.1%",
            companies: ["Amazon", "Bloomberg", "Adobe"],
            description:
              "Given a string s, find the length of the longest substring without repeating characters.",
            likes: 19234,
            dislikes: 923,
          },
          {
            id: 4,
            title: "Median of Two Sorted Arrays",
            difficulty: "Hard",
            tags: ["Array", "Binary Search", "Divide and Conquer"],
            solved: false,
            acceptance: "35.2%",
            companies: ["Google", "ByteDance", "Apple"],
            description:
              "Given two sorted arrays nums1 and nums2 of size m and n respectively, return the median of the two sorted arrays.",
            likes: 15234,
            dislikes: 1723,
          },
          {
            id: 5,
            title: "Valid Parentheses",
            difficulty: "Easy",
            tags: ["String", "Stack"],
            solved: true,
            acceptance: "40.5%",
            companies: ["Facebook", "Amazon", "Microsoft"],
            description:
              "Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', determine if the input string is valid.",
            likes: 11234,
            dislikes: 534,
          },
          {
            id: 6,
            title: "Merge k Sorted Lists",
            difficulty: "Hard",
            tags: ["Linked List", "Divide and Conquer", "Heap"],
            solved: false,
            acceptance: "47.6%",
            companies: ["Google", "Amazon", "Uber"],
            description:
              "You are given an array of k linked-lists lists, each linked-list is sorted in ascending order. Merge all the linked-lists into one sorted linked-list and return it.",
            likes: 9234,
            dislikes: 423,
          },
          {
            id: 7,
            title: "Best Time to Buy and Sell Stock",
            difficulty: "Easy",
            tags: ["Array", "Dynamic Programming"],
            solved: true,
            acceptance: "54.1%",
            companies: ["Amazon", "Goldman Sachs", "Bloomberg"],
            description:
              "You are given an array prices where prices[i] is the price of a given stock on the ith day. You want to maximize your profit by choosing a single day to buy one stock and choosing a different day in the future to sell that stock.",
            likes: 13456,
            dislikes: 456,
          },
          {
            id: 8,
            title: "Maximum Subarray",
            difficulty: "Easy",
            tags: ["Array", "Divide and Conquer", "Dynamic Programming"],
            solved: false,
            acceptance: "49.5%",
            companies: ["LinkedIn", "Apple", "Google"],
            description:
              "Given an integer array nums, find the contiguous subarray (containing at least one number) which has the largest sum and return its sum.",
            likes: 16234,
            dislikes: 789,
          },
          {
            id: 9,
            title: "Binary Tree Inorder Traversal",
            difficulty: "Easy",
            tags: ["Stack", "Tree", "Depth-First Search"],
            solved: true,
            acceptance: "74.2%",
            companies: ["Microsoft", "Amazon", "Facebook"],
            description:
              "Given the root of a binary tree, return the inorder traversal of its nodes' values.",
            likes: 8234,
            dislikes: 234,
          },
          {
            id: 10,
            title: "Unique Paths",
            difficulty: "Medium",
            tags: ["Math", "Dynamic Programming", "Combinatorics"],
            solved: false,
            acceptance: "62.7%",
            companies: ["Google", "Uber", "ByteDance"],
            description:
              "There is a robot on an m x n grid. The robot is initially located at the top-left corner (i.e., grid[0][0]). The robot tries to move to the bottom-right corner (i.e., grid[m - 1][n - 1]). The robot can only move either down or right at any point in time.",
            likes: 9876,
            dislikes: 234,
          },
          {
            id: 11,
            title: "Climbing Stairs",
            difficulty: "Easy",
            tags: ["Math", "Dynamic Programming", "Memoization"],
            solved: true,
            acceptance: "51.4%",
            companies: ["Adobe", "Apple", "Amazon"],
            description:
              "You are climbing a staircase. It takes n steps to reach the top. Each time you can either climb 1 or 2 steps. In how many distinct ways can you climb to the top?",
            likes: 12345,
            dislikes: 345,
          },
          {
            id: 12,
            title: "Word Ladder",
            difficulty: "Hard",
            tags: ["Hash Table", "String", "Breadth-First Search"],
            solved: false,
            acceptance: "35.8%",
            companies: ["Facebook", "Amazon", "Microsoft"],
            description:
              "A transformation sequence from word beginWord to word endWord using a dictionary wordList is a sequence of words beginWord -> s1 -> s2 -> ... -> sk such that: Every adjacent pair of words differs by a single letter.",
            likes: 7234,
            dislikes: 1234,
          },
        ]);
      }
    };

    fetchQuestions();
  }, []);

  const dataStructureTopics = [
    "Array",
    "Hash Table",
    "Linked List",
    "Stack",
    "Tree",
    "String",
    "Dynamic Programming",
    "Math",
    "Binary Search",
    "Sliding Window",
    "Breadth-First Search",
    "Depth-First Search",
    "Divide and Conquer",
    "Heap",
    "Combinatorics",
    "Memoization",
  ];

  const filteredQuestions = questions.filter((question) => {
    return (
      question.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (difficultyFilter === "" || question.difficulty === difficultyFilter) &&
      (topicFilter === "" ||
        question.tags.some(
          (tag) => tag.toLowerCase() === topicFilter.toLowerCase()
        ))
    );
  });

  const totalPages = Math.ceil(filteredQuestions.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredQuestions.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case "Easy":
        return "text-green-600 bg-green-100";
      case "Medium":
        return "text-yellow-600 bg-yellow-100";
      case "Hard":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.6, ease: "easeOut" },
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-6 py-16">
        {/* Enhanced Header Section */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="relative inline-block mb-6">
            <motion.h1
              className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-[#118c6e] via-[#0e7c62] to-[#0a6e58] bg-clip-text  mb-4"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "linear",
              }}
            >
              Master Coding Excellence
            </motion.h1>
            <motion.div
              className="absolute -inset-2 rounded-lg opacity-20 blur-xl"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>

          <motion.p
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
            className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed italic"
          >
            Solve top coding and interview questions from leading tech
            companies. Build your problem-solving skills with our curated
            collection of challenges.
          </motion.p>

          {/* Stats Row */}
          <motion.div
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="flex flex-wrap justify-center gap-8 mt-12"
          >
            {[
              { number: "2500+", label: "Coding Problems", icon: "⚡" },
              { number: "50+", label: "Company Collections", icon: "🏢" },
              { number: "15+", label: "Topic Categories", icon: "📚" },
              { number: "95%", label: "Interview Success", icon: "🎯" },
            ].map((stat, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white backdrop-blur-sm border border-white/20 rounded-xl p-6 text-center shadow-lg"
              >
                <div className="text-3xl mb-2">{stat.icon}</div>
                <div className="text-2xl font-bold text-gray-800">
                  {stat.number}
                </div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Advanced Filters Section */}
        <motion.div
          initial={{ y: 30, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-8 mb-12 shadow-2xl bg-[#303246]"
        >
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 ">
            {/* Search */}
            {/* Search */}
            <div className="lg:col-span-2 relative">
              <label
                htmlFor="search-input"
                className="block text-sm font-semibold text-white mb-2"
              >
                Search Problems
              </label>
              <input
                id="search-input"
                type="text"
                placeholder="Search by problem title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                aria-label="Search Problems"
                className="w-full pl-12 pr-10 py-3 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:border-transparent transition-all"
              />
              {/* Search Icon */}
              <div className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none">
                🔍
              </div>

              {/* Clear Button */}
              {searchTerm && (
                <button
                  aria-label="Clear search"
                  onClick={() => setSearchTerm("")}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                  &#10005;
                </button>
              )}
            </div>

            {/* Difficulty Filter */}
            <div>
              <label
                htmlFor="difficulty-select"
                className="block text-sm font-semibold text-white mb-2"
              >
                Difficulty
              </label>
              <select
                id="difficulty-select"
                value={difficultyFilter}
                onChange={(e) => setDifficultyFilter(e.target.value)}
                className="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:border-transparent transition-all"
              >
                <option value="">All Levels</option>
                <option value="Easy">Easy</option>
                <option value="Medium">Medium</option>
                <option value="Hard">Hard</option>
              </select>
            </div>

            {/* Topic Filter */}
            <div>
              <label
                htmlFor="topic-select"
                className="block text-sm font-semibold text-white mb-2"
              >
                Topic
              </label>
              <select
                id="topic-select"
                value={topicFilter}
                onChange={(e) => setTopicFilter(e.target.value)}
                className="w-full px-4 py-3 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:border-transparent transition-all"
              >
                <option value="">All Topics</option>
                {dataStructureTopics.map((topic) => (
                  <option key={topic} value={topic}>
                    {topic}
                  </option>
                ))}
              </select>
            </div>3
          </div>

          {/* Topic Pills */}
          <div className="mt-6">
            <p className="text-sm font-semibold text-white mb-3">
              Popular Topics:
            </p>
            <div className="flex flex-wrap gap-2">
              {[
                "Array",
                "Dynamic Programming",
                "Tree",
                "Hash Table",
                "String",
                "Binary Search",
              ].map((topic) => (
                <motion.button
                  key={topic}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setTopicFilter(topic)}
                  className={`px-5 py-2 rounded-full text-sm font-medium transition-all focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 ${
                    topicFilter === topic
                      ? "bg-blue-600 text-white shadow-lg"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                  aria-pressed={topicFilter === topic}
                >
                  {topic}
                </motion.button>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Problems Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-12"
        >
          {currentItems.map((question, index) => (
            <motion.div
              key={question.id}
              variants={cardVariants}
              whileHover={{
                scale: 1.02,
                y: -8,
                boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
              }}
              className="bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-white/90 transition-all duration-300 group shadow-xl"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-bold text-gray-800 group-hover:text-blue-600 transition-colors">
                      {question.title}
                    </h3>
                    {question.solved && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"
                      >
                        <span className="text-white text-xs">✓</span>
                      </motion.div>
                    )}
                  </div>

                  <div className="flex items-center gap-3 mb-3">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-semibold ${getDifficultyColor(
                        question.difficulty
                      )}`}
                    >
                      {question.difficulty}
                    </span>
                    {question.acceptance && (
                      <span className="text-sm text-gray-600">
                        {question.acceptance} acceptance
                      </span>
                    )}
                  </div>

                  {/* Problem Description */}
                  {question.description && (
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">
                      {question.description}
                    </p>
                  )}

                  <div className="flex flex-wrap gap-2 mb-4">
                    {question.tags.slice(0, 3).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-lg font-medium"
                      >
                        {tag}
                      </span>
                    ))}
                    {question.tags.length > 3 && (
                      <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-lg">
                        +{question.tags.length - 3} more
                      </span>
                    )}
                  </div>

                  {/* Stats Row */}
                  <div className="flex items-center justify-between mb-4">
                    {question.likes && question.dislikes && (
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1 text-green-600">
                          <span>👍</span>
                          <span>{question.likes.toLocaleString()}</span>
                        </div>
                        <div className="flex items-center gap-1 text-red-600">
                          <span>👎</span>
                          <span>{question.dislikes.toLocaleString()}</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {question.companies && (
                    <div className="mb-4">
                      <p className="text-xs text-gray-500 mb-1">Asked by:</p>
                      <div className="flex flex-wrap gap-1">
                        {question.companies
                          .slice(0, 3)
                          .map((company, compIndex) => (
                            <span
                              key={compIndex}
                              className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded font-medium"
                            >
                              {company}
                            </span>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex gap-4 justify-center flex-wrap">
                <Link
                  to={`/code-editor/${question.id}/solve`}
                  className="w-48 min-w-[12rem] whitespace-nowrap bg-[#118c6e] hover:bg-[#0f7a5f] text-white py-3 px-3 rounded-xl font-semibold text-center transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  Solve Challenge
                </Link>

                <Link
                  to={`/code-editor/${question.id}/solution`}
                  className="w-48 min-w-[12rem] whitespace-nowrap bg-[#2d3e3f] hover:bg-[#3c5051] text-white py-3 px-3 rounded-xl font-semibold text-center transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  See Solution
                </Link>

                {/* <button className="w-12 h-12 flex items-center justify-center border-2 border-gray-200 rounded-xl hover:border-blue-300 transition-colors shadow-md">
                  💾
                </button> */}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.8 }}
            className="flex justify-center mt-12"
          >
            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
              >
                ← Previous
              </button>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      currentPage === page
                        ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg"
                        : "bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
                    }`}
                  >
                    {page}
                  </button>
                )
              )}

              <button
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className="px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed bg-white text-gray-600 hover:bg-gray-50 border border-gray-200"
              >
                Next →
              </button>
            </div>
          </motion.div>
        )}

        {/* Enhanced Call to Action */}
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 1 }}
          className="relative mt-20 bg-[#149b77] rounded-3xl p-12 text-white text-center overflow-hidden"
        >
          {/* Background Pattern */}
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10 ">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 to-transparent"></div>
            <motion.div
              animate={{
                backgroundPosition: ["0% 0%", "100% 100%"],
                opacity: [0.1, 0.2, 0.1],
              }}
              transition={{ duration: 8, repeat: Infinity }}
              className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.1)_50%,transparent_75%)] bg-[length:20px_20px]"
            />
          </div>

          <div className="relative z-10">
            <h3 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Ace Your Next Interview?
            </h3>
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
              className="text-xl mb-10 opacity-90 max-w-3xl mx-auto italic"
            >
              Join over 10,000 developers who've successfully landed their dream
              jobs after mastering our coding challenges and interview
              preparation materials.
            </motion.p>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            >
              <Link
                to="/register"
                className="bg-[#303246] text-white px-8 py-4 rounded-xl font-bold  transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105"
              >
                🚀 Start Practicing Free
              </Link>
              <Link
                to="/premium"
                className="border-2 border-white text-white px-8 py-4 rounded-xl font-bold  hover:text-blue-600 transition-all duration-300 transform hover:scale-105"
              >
                💎 View Premium Plans
              </Link>
            </motion.div>

            {/* Success Stats */}
            <motion.div
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6 }}
              className="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6"
            >
              {[
                { stat: "95%", label: "Interview Success Rate" },
                { stat: "$120K", label: "Average Salary Increase" },
                { stat: "30 Days", label: "Average Job Search Time" },
                { stat: "500+", label: "Partner Companies" },
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl font-bold">{item.stat}</div>
                  <div className="text-sm text-gray-200 opacity-80">
                    {item.label}
                  </div>
                </div>
              ))}
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default CodingProblem;
