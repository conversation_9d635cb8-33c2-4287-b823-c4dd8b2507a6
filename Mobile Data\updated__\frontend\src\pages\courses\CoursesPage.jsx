import React, { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { FaPython, FaReact, FaDatabase, FaBrain, FaRobot, FaCode, FaSearch, FaJs, FaFilter } from "react-icons/fa";
import ReusableNavbar from "../../components/layout/ReusableNavbar";
import { PaymentModal } from "../../components/ui";

const CoursesPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLevel, setSelectedLevel] = useState("All");
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);
  
  const coursesData = [
    {
      id: "python",
      title: "Python Programming",
      description: "Master Python from basics to advanced concepts with hands-on projects",
      path: "/pythoncourse",
      icon: <FaPython className="text-4xl" />,
      level: "Beginner to Advanced",
      duration: "12 weeks",
      students: "15,000+",
      rating: 4.8,
      features: ["Interactive Labs", "Real Projects", "Live Sessions", "Certificate"],
      technologies: ["Python", "Django", "Flask", "Data Analysis"]
    },
    {
      id: "fullstack",
      title: "Full Stack Development",
      description: "Build complete web applications with modern technologies",
      path: "/fullstack-course",
      icon: <FaReact className="text-4xl" />,
      level: "Intermediate",
      duration: "16 weeks",
      students: "12,000+",
      rating: 4.9,
      features: ["MERN Stack", "Deployment", "API Development", "Portfolio"],
      technologies: ["React", "Node.js", "MongoDB", "Express"]
    },
    {
      id: "datascience",
      title: "Data Science",
      description: "Analyze data and build predictive models with Python",
      path: "/datascience-course",
      icon: <FaDatabase className="text-4xl" />,
      level: "Intermediate",
      duration: "20 weeks",
      students: "8,500+",
      rating: 4.7,
      features: ["Data Analysis", "Visualization", "ML Models", "Real Datasets"],
      technologies: ["Python", "Pandas", "NumPy", "Matplotlib"]
    },
    {
      id: "ml",
      title: "Machine Learning",
      description: "Build intelligent systems with machine learning algorithms",
      path: "/ml-course",
      icon: <FaBrain className="text-4xl" />,
      level: "Advanced",
      duration: "18 weeks",
      students: "6,200+",
      rating: 4.8,
      features: ["ML Algorithms", "Deep Learning", "Model Deployment", "Projects"],
      technologies: ["Python", "TensorFlow", "Scikit-learn", "Keras"]
    },
    {
      id: "ai",
      title: "Artificial Intelligence",
      description: "Explore AI concepts and build intelligent applications",
      path: "/ai-course",
      icon: <FaRobot className="text-4xl" />,
      level: "Advanced",
      duration: "22 weeks",
      students: "4,800+",
      rating: 4.9,
      features: ["Neural Networks", "NLP", "Computer Vision", "AI Ethics"],
      technologies: ["Python", "TensorFlow", "PyTorch", "OpenAI"]
    },
    {
      id: "dsa",
      title: "Data Structures & Algorithms",
      description: "Master DSA for technical interviews and competitive programming",
      path: "/data_strut",
      icon: <FaCode className="text-4xl" />,
      level: "Intermediate",
      duration: "14 weeks",
      students: "18,000+",
      rating: 4.8,
      features: ["Problem Solving", "Interview Prep", "Coding Practice", "Algorithms"],
      technologies: ["Python", "Java", "C++", "JavaScript"]
    },
    {
      id: "sql",
      title: "SQL Database",
      description: "Master database management and SQL queries",
      path: "/sql_50",
      icon: <FaSearch className="text-4xl" />,
      level: "Beginner to Intermediate",
      duration: "8 weeks",
      students: "10,500+",
      rating: 4.6,
      features: ["Query Optimization", "Database Design", "Joins", "Indexing"],
      technologies: ["MySQL", "PostgreSQL", "SQLite", "MongoDB"]
    },
    {
      id: "javascript",
      title: "JavaScript Mastery",
      description: "30 days of JavaScript challenges and projects",
      path: "/30_days_js",
      icon: <FaJs className="text-4xl" />,
      level: "Beginner to Advanced",
      duration: "30 days",
      students: "14,200+",
      rating: 4.7,
      features: ["Daily Challenges", "ES6+", "DOM Manipulation", "Async Programming"],
      technologies: ["JavaScript", "ES6+", "Node.js", "React"]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  // Filter courses based on search term and level
  const filteredCourses = coursesData.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.technologies.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesLevel = selectedLevel === "All" || course.level.includes(selectedLevel);

    return matchesSearch && matchesLevel;
  });

  const handleStartLearning = (course) => {
    const courseData = {
      name: course.title,
      labType: course.id,
      _id: course.id
    };
    setSelectedCourse(courseData);
    setIsPaymentModalOpen(true);
  };

  const closePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedCourse(null);
  };

  const levels = ["All", "Beginner", "Intermediate", "Advanced"];

  return (
    <div className="min-h-screen bg-gray-800 relative overflow-hidden">
      <ReusableNavbar
        toggleSidebar={() => {}}
        title="All Courses"
        showSidebarToggle={false}
        isSidebarOpen={false}
      />

      <div className="relative z-10 pt-20">
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center py-16 px-6"
        >
          <h1 className="text-5xl md:text-6xl font-bold text-white mb-6">
            Explore Our Courses
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Choose from our comprehensive collection of courses designed to accelerate your career.
            Learn from industry experts, build real projects, and get job-ready skills.
          </p>
        </motion.div>

        {/* Search and Filter Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-4xl mx-auto px-6 mb-12"
        >
          <div className="bg-transparent border border-gray-600 rounded-2xl p-6">
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search Input */}
              <div className="flex-1 relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search courses, technologies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-transparent border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 outline-none transition-all duration-200 text-white placeholder-gray-400"
                />
              </div>

              {/* Level Filter */}
              <div className="relative">
                <FaFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value)}
                  className="pl-10 pr-8 py-3 bg-transparent border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-400 focus:border-blue-400 outline-none transition-all duration-200 text-white min-w-[150px]"
                >
                  {levels.map(level => (
                    <option key={level} value={level} className="bg-gray-800 text-white">{level} Level</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Results Count */}
            <div className="mt-4 text-sm text-gray-300">
              Showing {filteredCourses.length} of {coursesData.length} courses
            </div>
          </div>
        </motion.div>

        {/* Courses Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-7xl mx-auto px-6 pb-20"
        >
          {filteredCourses.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
              {filteredCourses.map((course) => (
                <motion.div
                  key={course.id}
                  variants={cardVariants}
                  whileHover={{ y: -10, scale: 1.02 }}
                  className="group"
                >
                  <Link to={course.path} className="block">
                    <div className="relative overflow-hidden rounded-2xl bg-transparent border border-gray-600 hover:border-gray-500 transition-all duration-300 h-full">
                      {/* Card Header */}
                      <div className="h-32 bg-transparent border-b border-gray-600 relative overflow-hidden">
                        <div className="absolute top-4 right-4 text-gray-300 group-hover:text-white group-hover:scale-110 transition-all duration-300">
                          {course.icon}
                        </div>
                        <div className="absolute bottom-4 left-4">
                          <span className="inline-block px-3 py-1 bg-transparent border border-gray-600 rounded-full text-white text-sm font-medium group-hover:border-gray-500 transition-all duration-300">
                            {course.level}
                          </span>
                        </div>
                      </div>

                      {/* Card Content */}
                      <div className="p-6 flex flex-col h-full">
                        <div className="flex-grow">
                          <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
                            {course.title}
                          </h3>
                          <p className="text-gray-300 text-sm mb-4 line-clamp-2 leading-relaxed">
                            {course.description}
                          </p>

                          {/* Course Stats */}
                          <div className="flex items-center justify-between mb-4 text-sm text-gray-400">
                            <div className="flex items-center space-x-3">
                              <span className="flex items-center text-gray-300">
                                <span className="text-yellow-400 mr-1">⭐</span>
                                {course.rating}
                              </span>
                              <span className="flex items-center text-gray-300">
                                <span className="text-blue-400 mr-1">👥</span>
                                {course.students}
                              </span>
                            </div>
                            <span className="font-medium text-gray-300 bg-white/10 px-2 py-1 rounded-md text-xs">
                              {course.duration}
                            </span>
                          </div>

                          {/* Features */}
                          <div className="mb-4">
                            <div className="flex flex-wrap gap-1.5">
                              {course.features.slice(0, 3).map((feature, index) => (
                                <span
                                  key={index}
                                  className="inline-block px-2.5 py-1 bg-transparent hover:bg-gray-800 text-gray-300 text-xs rounded-md transition-colors duration-200 border border-gray-600"
                                >
                                  {feature}
                                </span>
                              ))}
                              {course.features.length > 3 && (
                                <span className="inline-block px-2.5 py-1 bg-transparent text-gray-300 text-xs rounded-md border border-gray-600">
                                  +{course.features.length - 3} more
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Technologies */}
                          <div className="mb-6">
                            <div className="flex flex-wrap gap-1.5">
                              {course.technologies.slice(0, 3).map((tech, index) => (
                                <span
                                  key={index}
                                  className="inline-block px-2.5 py-1 bg-transparent text-white text-xs rounded-md font-medium border border-gray-500"
                                >
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* CTA Button */}
                        <div className="mt-auto">
                          <button
                            onClick={() => handleStartLearning(course)}
                            className="w-full py-3 px-4 bg-transparent border border-gray-500 hover:border-gray-400 text-white text-center rounded-lg font-medium group-hover:bg-gray-800 transition-all duration-300 cursor-pointer"
                          >
                            Start Learning →
                          </button>
                        </div>
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}
            </div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-20"
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-bold text-white mb-2">No courses found</h3>
              <p className="text-gray-300 mb-6">
                Try adjusting your search terms or filters to find what you're looking for.
              </p>
              <button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedLevel("All");
                }}
                className="px-6 py-3 bg-transparent border border-gray-500 hover:border-gray-400 text-white rounded-lg font-medium hover:bg-gray-800 transition-all duration-300"
              >
                Clear Filters
              </button>
            </motion.div>
          )}
        </motion.div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={closePaymentModal}
        courseData={selectedCourse}
      />
    </div>
  );
};

export default CoursesPage;
