import React from 'react';
import { Editor } from '@monaco-editor/react';

const MonacoEditor = ({
  code,
  activeFile,
  files,
  onChange,
  isDarkMode
}) => {
  const getLanguage = () => {
    const fileType = files[activeFile]?.type;
    return fileType === 'javascript' ? 'javascript' : fileType;
  };

  return (
    <div className="flex-1">
      <div className={`p-2 border-b text-sm ${
        isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-300' : 'border-gray-200 bg-gray-50 text-gray-700'
      }`}>
        <span>{activeFile}</span>
      </div>
      <Editor
        height="calc(100% - 40px)"
        language={getLanguage()}
        value={code}
        onChange={onChange}
        theme={isDarkMode ? 'vs-dark' : 'light'}
        options={{
          minimap: { enabled: false },
          fontSize: 14,
          lineNumbers: 'on',
          roundedSelection: false,
          scrollBeyondLastLine: false,
          automaticLayout: true,
          tabSize: 2,
          insertSpaces: true,
          wordWrap: 'on',
          contextmenu: true,
          selectOnLineNumbers: true,
          glyphMargin: false,
          folding: true,
          lineDecorationsWidth: 10,
          lineNumbersMinChars: 3,
          renderLineHighlight: 'line',
          scrollbar: {
            vertical: 'visible',
            horizontal: 'visible',
            useShadows: false,
            verticalHasArrows: false,
            horizontalHasArrows: false,
          },
        }}
      />
    </div>
  );
};

export default MonacoEditor;
