import React, { useState } from "react";

const Introduction = ({ theme }) => {
  const sampleIntroductions = [
    {
      id: 1,
      lab: "64e861b2f674091a4c1e7e9a",
      labName: "Chemistry Lab", // Just for display
      section: "64f0c2f6b6b2b84738cfb62a",
      sectionName: "Organic Chemistry",
      courseOverview: "This course covers fundamentals of chemistry.",
      whyLearning: "To build a strong foundation.",
      whatYouWillLearn: "Atomic structure, Bonding.",
      keyTopics: ["Atoms", "Molecules", "Reactions"],
      toolsAndTechnologies: [
        { name: "Tool A", icon: { public_id: "", secure_url: "" } },
        { name: "Tool B", icon: { public_id: "", secure_url: "" } },
      ],
      conceptAndSkills: {
        description: "You will learn key chemistry concepts.",
        points: ["Atomic theory", "Periodic table", "Chemical bonding"],
      },
      careerImpact: "Enhance job prospects in chemical engineering.",
      createdBy: "User1",
      lastEditedBy: "User2",
    },
  ];

  const [introductions, setIntroductions] = useState(sampleIntroductions);
  const [editingIntro, setEditingIntro] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const inputClass = `w-full mt-1 border rounded px-3 py-2 ${
    theme === "dark"
      ? "bg-gray-700 text-white border-gray-600 placeholder-gray-300"
      : "bg-white text-gray-900 border-gray-300 placeholder-gray-500"
  }`;

  // Form state initialized to editing item or empty
  const [form, setForm] = useState({
    lab: "",
    labName: "",
    section: "",
    sectionName: "",
    courseOverview: "",
    whyLearning: "",
    whatYouWillLearn: "",
    keyTopics: [""],
    toolsAndTechnologies: [
      { name: "", icon: { public_id: "", secure_url: "" } },
    ],
    conceptAndSkills: { description: "", points: [""] },
    careerImpact: "",
  });

  // Reset form when opening modal for new or editing
  const openEditModal = (intro) => {
    if (intro) {
      setEditingIntro(intro);
      // Deep copy to form
      setForm({ ...intro });
    } else {
      setEditingIntro(null);
      setForm({
        lab: "",
        labName: "",
        section: "",
        sectionName: "",
        courseOverview: "",
        whyLearning: "",
        whatYouWillLearn: "",
        keyTopics: [""],
        toolsAndTechnologies: [
          { name: "", icon: { public_id: "", secure_url: "" } },
        ],
        conceptAndSkills: { description: "", points: [""] },
        careerImpact: "",
      });
    }
    setShowModal(true);
  };

  const handleDelete = (id) => {
    if (window.confirm("Are you sure you want to delete this introduction?")) {
      setIntroductions((prev) => prev.filter((intro) => intro.id !== id));
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleKeyTopicChange = (index, value) => {
    const newTopics = [...form.keyTopics];
    newTopics[index] = value;
    setForm((prev) => ({ ...prev, keyTopics: newTopics }));
  };

  const addKeyTopic = () =>
    setForm((prev) => ({ ...prev, keyTopics: [...prev.keyTopics, ""] }));
  const removeKeyTopic = (index) => {
    if (form.keyTopics.length === 1) return; // Keep at least one
    const newTopics = form.keyTopics.filter((_, i) => i !== index);
    setForm((prev) => ({ ...prev, keyTopics: newTopics }));
  };

  const handleToolChange = (index, field, value) => {
    const newTools = [...form.toolsAndTechnologies];
    if (field === "name") {
      newTools[index].name = value;
    } else if (field === "public_id" || field === "secure_url") {
      newTools[index].icon[field] = value;
    }
    setForm((prev) => ({ ...prev, toolsAndTechnologies: newTools }));
  };

  const addTool = () => {
    setForm((prev) => ({
      ...prev,
      toolsAndTechnologies: [
        ...prev.toolsAndTechnologies,
        { name: "", icon: { public_id: "", secure_url: "" } },
      ],
    }));
  };

  const removeTool = (index) => {
    if (form.toolsAndTechnologies.length === 1) return;
    const newTools = form.toolsAndTechnologies.filter((_, i) => i !== index);
    setForm((prev) => ({ ...prev, toolsAndTechnologies: newTools }));
  };

  // Handling conceptAndSkills inputs
  const handleConceptDescriptionChange = (e) => {
    const value = e.target.value;
    setForm((prev) => ({
      ...prev,
      conceptAndSkills: { ...prev.conceptAndSkills, description: value },
    }));
  };

  const handleConceptPointChange = (index, value) => {
    const newPoints = [...form.conceptAndSkills.points];
    newPoints[index] = value;
    setForm((prev) => ({
      ...prev,
      conceptAndSkills: { ...prev.conceptAndSkills, points: newPoints },
    }));
  };

  const addConceptPoint = () => {
    setForm((prev) => ({
      ...prev,
      conceptAndSkills: {
        ...prev.conceptAndSkills,
        points: [...prev.conceptAndSkills.points, ""],
      },
    }));
  };

  const removeConceptPoint = (index) => {
    if (form.conceptAndSkills.points.length === 1) return;
    const newPoints = form.conceptAndSkills.points.filter(
      (_, i) => i !== index
    );
    setForm((prev) => ({
      ...prev,
      conceptAndSkills: { ...prev.conceptAndSkills, points: newPoints },
    }));
  };

  // Save form (add or update)
  const handleSave = () => {
    // Basic validation for required fields
    if (
      !form.lab ||
      !form.section ||
      !form.courseOverview.trim() ||
      !form.whyLearning.trim() ||
      !form.whatYouWillLearn.trim() ||
      form.keyTopics.some((kt) => !kt.trim()) ||
      !form.conceptAndSkills.description.trim() ||
      form.conceptAndSkills.points.some((pt) => !pt.trim()) ||
      !form.careerImpact.trim()
    ) {
      alert(
        "Please fill out all required fields and remove empty array entries."
      );
      return;
    }

    if (editingIntro) {
      // Update existing
      setIntroductions((prev) =>
        prev.map((intro) =>
          intro.id === editingIntro.id
            ? { ...form, id: editingIntro.id }
            : intro
        )
      );
    } else {
      // Add new entry
      setIntroductions((prev) => [
        ...prev,
        {
          ...form,
          id: Date.now(),
        },
      ]);
    }
    setShowModal(false);
  };

  return (
    <div
      className={`max-w-7xl mx-auto p-8 space-y-6 ${
        theme === "dark" ? "bg-gray-900 text-white" : "bg-white text-gray-900"
      }`}
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Introductions</h1>
        <button
          onClick={() => openEditModal(null)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded"
        >
          Add Introduction
        </button>
      </div>

      {/* Table */}
      <div className="overflow-x-auto border rounded">
        <table className="min-w-full table-auto border-collapse">
          <thead>
            <tr>
              <th className="border px-3 py-2 text-left">Lab</th>
              <th className="border px-3 py-2 text-left">Section</th>
              <th className="border px-3 py-2 text-left">Course Overview</th>
              <th className="border px-3 py-2 text-left">Why Learning</th>
              <th className="border px-3 py-2 text-left">
                What You Will Learn
              </th>
              <th className="border px-3 py-2 text-left">Key Topics</th>
              <th className="border px-3 py-2 text-left">Tools & Tech</th>
              <th className="border px-3 py-2 text-left">Concept & Skills</th>
              <th className="border px-3 py-2 text-left">Career Impact</th>
              <th className="border px-3 py-2 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {introductions.length === 0 && (
              <tr>
                <td colSpan={10} className="text-center p-4 text-gray-600">
                  No introductions found.
                </td>
              </tr>
            )}
            {introductions.map((intro) => (
              <tr key={intro.id}>
                <td className="border px-3 py-2">
                  {intro.labName || intro.lab}
                </td>
                <td className="border px-3 py-2">
                  {intro.sectionName || intro.section}
                </td>
                <td
                  className="border px-3 py-2 max-w-xs truncate"
                  title={intro.courseOverview}
                >
                  {intro.courseOverview}
                </td>
                <td
                  className="border px-3 py-2 max-w-xs truncate"
                  title={intro.whyLearning}
                >
                  {intro.whyLearning}
                </td>
                <td
                  className="border px-3 py-2 max-w-xs truncate"
                  title={intro.whatYouWillLearn}
                >
                  {intro.whatYouWillLearn}
                </td>
                <td
                  className="border px-3 py-2 max-w-xs truncate"
                  title={intro.keyTopics.join(", ")}
                >
                  {intro.keyTopics.join(", ")}
                </td>
                <td
                  className="border px-3 py-2 max-w-xs truncate"
                  title={intro.toolsAndTechnologies
                    .map((t) => t.name)
                    .join(", ")}
                >
                  {intro.toolsAndTechnologies.map((t) => t.name).join(", ")}
                </td>
                <td className="border px-3 py-2 max-w-xs truncate">
                  <strong>Description:</strong>{" "}
                  {intro.conceptAndSkills.description}
                  <br />
                  <strong>Points:</strong>{" "}
                  {intro.conceptAndSkills.points.join(", ")}
                </td>
                <td
                  className="border px-3 py-2 max-w-xs truncate"
                  title={intro.careerImpact}
                >
                  {intro.careerImpact}
                </td>
                <td className="border px-3 py-2">
                  <button
                    onClick={() => openEditModal(intro)}
                    className="mr-2 text-blue-600 hover:underline"
                    aria-label="Edit Introduction"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(intro.id)}
                    className="text-red-600 hover:underline"
                    aria-label="Delete Introduction"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Edit Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex justify-center items-start overflow-auto pt-10 p-4">
          <div
            className={`rounded-lg shadow-lg max-w-5xl w-full max-h-[90vh] overflow-auto p-6 ${
              theme === "dark"
                ? "bg-gray-800 text-white"
                : "bg-white text-gray-900"
            }`}
          >
            <h2 className="text-xl font-semibold mb-4">
              {editingIntro ? "Edit Introduction" : "Add Introduction"}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Lab */}
              <div>
                <label className="font-medium">Lab (ID or name)</label>
                <input
                  type="text"
                  name="lab"
                  value={form.lab}
                  onChange={handleChange}
                  className={inputClass}
                  placeholder="Lab ObjectId or name"
                />
              </div>
              <div>
                <label className="font-medium">Lab Name (for display)</label>
                <input
                  type="text"
                  name="labName"
                  value={form.labName}
                  onChange={handleChange}
                  className={inputClass}
                  placeholder="Lab display name"
                />
              </div>
              {/* Section */}
              <div>
                <label className="font-medium">Section (ID or name)</label>
                <input
                  type="text"
                  name="section"
                  value={form.section}
                  onChange={handleChange}
                  className={inputClass}
                  placeholder="LabSection ObjectId or name"
                />
              </div>
              <div>
                <label className="font-medium">
                  Section Name (for display)
                </label>
                <input
                  type="text"
                  name="sectionName"
                  value={form.sectionName}
                  onChange={handleChange}
                  className={inputClass}
                  placeholder="Section display name"
                />
              </div>
              {/* courseOverview */}
              <div className="md:col-span-2">
                <label className="font-medium">Course Overview *</label>
                <textarea
                  name="courseOverview"
                  value={form.courseOverview}
                  onChange={handleChange}
                  className={inputClass}
                  rows={3}
                  required
                />
              </div>
              {/* whyLearning */}
              <div className="md:col-span-2">
                <label className="font-medium">Why Learning? *</label>
                <textarea
                  name="whyLearning"
                  value={form.whyLearning}
                  onChange={handleChange}
                  className={inputClass}
                  rows={3}
                  required
                />
              </div>
              {/* whatYouWillLearn */}
              <div className="md:col-span-2">
                <label className="font-medium">What You Will Learn *</label>
                <textarea
                  name="whatYouWillLearn"
                  value={form.whatYouWillLearn}
                  onChange={handleChange}
                  className={inputClass}
                  rows={3}
                  required
                />
              </div>

              {/* keyTopics (array) */}
              <div className="md:col-span-2">
                <label className="font-medium mb-1">Key Topics *</label>
                {form.keyTopics.map((topic, i) => (
                  <div key={i} className="flex items-center mb-2 gap-2">
                    <input
                      type="text"
                      value={topic}
                      onChange={(e) => handleKeyTopicChange(i, e.target.value)}
                      className="border rounded flex-grow px-3 py-2"
                      placeholder="Topic"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => removeKeyTopic(i)}
                      className="bg-red-500 text-white rounded px-2 py-1"
                      disabled={form.keyTopics.length === 1}
                      title="Remove this topic"
                    >
                      &times;
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addKeyTopic}
                  className="text-blue-600 hover:underline"
                >
                  + Add Key Topic
                </button>
              </div>

              {/* toolsAndTechnologies */}
              <div className="md:col-span-2">
                <label className="font-medium mb-1">
                  Tools and Technologies *
                </label>
                {form.toolsAndTechnologies.map((tool, i) => (
                  <div key={i} className="mb-3 space-y-1 border p-3 rounded">
                    <div className="flex items-center justify-between mb-1">
                      <strong>Tool #{i + 1}</strong>
                      <button
                        type="button"
                        onClick={() => removeTool(i)}
                        className="bg-red-500 text-white rounded px-2 py-1"
                        disabled={form.toolsAndTechnologies.length === 1}
                        title="Remove this tool"
                      >
                        &times;
                      </button>
                    </div>
                    <input
                      type="text"
                      placeholder="Tool Name"
                      value={tool.name}
                      onChange={(e) =>
                        handleToolChange(i, "name", e.target.value)
                      }
                      className="w-full border rounded px-3 py-2 mb-1"
                      required
                    />
                    <input
                      type="text"
                      placeholder="Icon public_id"
                      value={tool.icon.public_id}
                      onChange={(e) =>
                        handleToolChange(i, "public_id", e.target.value)
                      }
                      className="w-full border rounded px-3 py-2 mb-1"
                    />
                    <input
                      type="text"
                      placeholder="Icon secure_url"
                      value={tool.icon.secure_url}
                      onChange={(e) =>
                        handleToolChange(i, "secure_url", e.target.value)
                      }
                      className="w-full border rounded px-3 py-2"
                    />
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addTool}
                  className="text-blue-600 hover:underline"
                >
                  + Add Tool
                </button>
              </div>

              {/* conceptAndSkills */}
              <div className="md:col-span-2">
                <label className="font-medium">
                  Concept and Skills: Description *
                </label>
                <textarea
                  name="conceptDesc"
                  value={form.conceptAndSkills.description}
                  onChange={handleConceptDescriptionChange}
                  rows={3}
                  className="w-full border rounded px-3 py-2 mb-4"
                  required
                />

                <label className="font-medium">Points *</label>
                {form.conceptAndSkills.points.map((pt, i) => (
                  <div key={i} className="flex items-center mb-2 gap-2">
                    <input
                      type="text"
                      value={pt}
                      onChange={(e) =>
                        handleConceptPointChange(i, e.target.value)
                      }
                      className="border rounded flex-grow px-3 py-2"
                      placeholder="Point"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => removeConceptPoint(i)}
                      className="bg-red-500 text-white rounded px-2 py-1"
                      disabled={form.conceptAndSkills.points.length === 1}
                      title="Remove this point"
                    >
                      &times;
                    </button>
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addConceptPoint}
                  className="text-blue-600 hover:underline"
                >
                  + Add Point
                </button>
              </div>

              {/* careerImpact */}
              <div className="md:col-span-2">
                <label className="font-medium">Career Impact *</label>
                <textarea
                  name="careerImpact"
                  value={form.careerImpact}
                  onChange={handleChange}
                  rows={3}
                  className="w-full border rounded px-3 py-2"
                  required
                />
              </div>
            </div>

            {/* Modal Buttons */}
            <div className="mt-6 flex justify-end space-x-4">
              <button
                onClick={() => setShowModal(false)}
                className="px-5 py-2 border rounded hover:bg-gray-100"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-5 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                {editingIntro ? "Update" : "Add"} Introduction
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Introduction;
