export const applyUpdatesToContent = (existingContent, updates) => {
  const fieldsToUpdate = [
    "title", "description", "slug", "body", "chapterId",
    "labId", "labsectionId", "author", "tags", "category",
    "keywords", "status", "visibility", "rolesAllowed",
    "checklist", "isPremium"
  ];

  fieldsToUpdate.forEach((field) => {
    if (updates[field] !== undefined) {
      existingContent[field] = updates[field];
    }
  });
};

export const updateThumbnail = (existingContent, thumbnail) => {
  if (thumbnail?.public_id && thumbnail?.secure_url) {
    existingContent.thumbnail = {
      public_id: thumbnail.public_id,
      secure_url: thumbnail.secure_url,
    };
  }
};

export const updateAttachments = (existingContent, attachments) => {
  if (Array.isArray(attachments)) {
    existingContent.attachments = attachments.map(att => ({
      public_id: att.public_id,
      secure_url: att.secure_url,
    }));
  }
};
