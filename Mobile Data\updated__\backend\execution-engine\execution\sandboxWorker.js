import { parentPort } from "worker_threads";
import { deleteTempFile, writeTempFile } from "./codeUtils.js";
import { getExecutionTimeout } from "../../utils/constants.js";
import languageHandlers from "../languageHandlers/index.js";

parentPort.on("message", async (job) => {
  const { code, language, input } = job;

  try {
    const handler = languageHandlers[language];

    if (!handler) {
      throw new Error(`Unsupported language: ${language}`);
    }

    const tempFilePath = await writeTempFile(code, language);

    const result = await handler.execute(tempFilePath, input, {
      timeout: getExecutionTimeout(language),
    });

    await deleteTempFile(tempFilePath);

    parentPort.postMessage({
      success: true,
      output: result.output,
      error: result.error || null,
      time: result.time,
      memory: result.memory,
    });
  } catch (err) {
    parentPort.postMessage({
      success: false,
      error: err.message || "Code execution failed",
    });
  }
});
