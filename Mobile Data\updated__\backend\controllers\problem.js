import mongoose from "mongoose";
import Problem from "../models/problemModal.js";
import <PERSON>rrorHandler from "../utils/ErrorHandler.js";
import { redis } from "../utils/redisClient.js";
import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import { sanitizeObject } from "../utils/sanitizeInput.js";
import { redisDelPattern } from "../utils/redisDel.js";
import { OUTPUT_CHARACTER_LIMIT } from "../utils/constants.js";
import { executeCode } from "../execution-engine/execution/executor.js";
import TestCase from "../models/testCaseModal.js";
import { sanitizeInput } from "../utils/sanitizeExecutionEngine.js";
import Submission from "../models/submissionModal.js";
import logger from "../utils/logger.js";
import { v4 as uuidv4 } from "uuid";
// import jobQueue from "../execution-engine/execution/jobQueue.js";
import pMap from "p-map";
import Lab from "../models/labModal.js";
import { extractHeadersAndCode } from "../utils/extractHeaderAndCode.js";
// import { getJobData, publishToQueue } from "../utils/rabbitMQ.js";

// export const createProblem = CatchAsyncError(async (req, res, next) => {
//   const body = sanitizeObject(req.body, {
//     lab: "string",
//     labSection: "string",
//     category: "string",
//     title: "string",
//     slug: "string?",
//     description: "string",
//     inputDescription: "string?",
//     outputDescription: "string?",
//     constraints: "string?",
//     difficulty: "string",
//     tags: "object?",
//     allowedLanguages: "object?",
//     starterCode: "object?",
//     solutionCode: "object?",
//     testCases: "object?",
//     timeLimit: "number?",
//     memoryLimit: "number?",
//     isActive: "boolean?",
//     isPremium: "boolean?",
//   });

//   const {
//     lab,
//     labSection,
//     category,
//     title,
//     slug,
//     description,
//     inputDescription,
//     outputDescription,
//     constraints,
//     difficulty,
//     allowedLanguages = [],
//     starterCode = {},
//     solutionCode = {},
//     testCases = [],
//     tags = [],
//     timeLimit = 1000,
//     memoryLimit = 256,
//     isActive = true,
//     isPremium = false,
//   } = body;

//   const requiredIds = [lab, labSection, category];
//   if (requiredIds.some((id) => !mongoose.Types.ObjectId.isValid(id))) {
//     return next(new ErrorHandler("Invalid Lab/Section/Category ID", 400));
//   }

//   const difficultyLevels = ["easy", "medium", "hard"];
//   if (!difficultyLevels.includes(difficulty)) {
//     return next(new ErrorHandler("Invalid difficulty level", 400));
//   }

//   const languageSet = new Set(["cpp", "java", "javascript", "python"]);
//   for (const lang of allowedLanguages) {
//     if (!languageSet.has(lang)) {
//       return next(new ErrorHandler(`Invalid language: ${lang}`, 400));
//     }
//   }

//   const generatedSlug =
//     slug?.toLowerCase().replace(/\s+/g, "-") ??
//     title.toLowerCase().trim().replace(/\s+/g, "-");

//   const existing = await Problem.findOne({ slug: generatedSlug });
//   if (existing) {
//     return next(new ErrorHandler("Problem with same slug already exists", 409));
//   }

//   const newProblem = await Problem.create({
//     lab,
//     labSection,
//     category,
//     title: title.trim(),
//     slug: generatedSlug,
//     description: description.trim(),
//     inputDescription,
//     outputDescription,
//     constraints,
//     difficulty,
//     allowedLanguages,
//     starterCode,
//     solutionCode,
//     testCases,
//     tags: tags.map((tag) => tag.trim()),
//     timeLimit,
//     memoryLimit,
//     isActive,
//     isPremium,
//     author: req.user._id,
//   });

//   await redis.del(`problems:lab:${lab}`);
//   await redis.del(`problems:section:${labSection}`);

//   res.status(201).json({
//     success: true,
//     message: "Problem created successfully",
//     problem: newProblem,
//   });
// });

export const createProblem = CatchAsyncError(async (req, res, next) => {
  const body = sanitizeObject(req.body, {
    lab: "string",
    labSection: "string",
    category: "string",
    title: "string",
    slug: "string?",
    description: "string",
    inputDescription: "string?",
    outputDescription: "string?",
    constraints: "string?",
    difficulty: "string",
    tags: "string[]?",
    starterCode: "object?",
    solutionCode: "object?",
    mainCodeTemplate: "object?",
    testCases: "object?",
    timeLimit: "number?",
    memoryLimit: "number?",
    isActive: "boolean?",
    isPremium: "boolean?",
  });

  let {
    lab,
    labSection,
    category,
    title,
    slug,
    description,
    inputDescription,
    outputDescription,
    constraints,
    difficulty,
    starterCode = {},
    solutionCode = {},
    mainCodeTemplate = {},
    testCases = [],
    tags = [],
    timeLimit = 1000,
    memoryLimit = 256,
    isActive = true,
    isPremium = false,
  } = body;

  const requiredIds = [lab, labSection, category];
  if (requiredIds.some((id) => !mongoose.Types.ObjectId.isValid(id))) {
    return next(new ErrorHandler("Invalid Lab/Section/Category ID", 400));
  }

  const difficultyLevels = ["easy", "medium", "hard"];
  if (!difficultyLevels.includes(difficulty)) {
    return next(new ErrorHandler("Invalid difficulty level", 400));
  }

  const generatedSlug =
    slug?.toLowerCase().replace(/\s+/g, "-") ??
    title.toLowerCase().trim().replace(/\s+/g, "-");

  const existing = await Problem.findOne({ slug: generatedSlug });
  if (existing) {
    return next(new ErrorHandler("Problem with same slug already exists", 409));
  }

  const newProblem = await Problem.create({
    lab,
    labSection,
    category,
    title: title.trim(),
    slug: generatedSlug,
    description: description.trim(),
    inputDescription,
    outputDescription,
    constraints,
    difficulty,
    starterCode,
    solutionCode,
    mainCodeTemplate,
    testCases,
    tags: tags.map((tag) => tag.trim()),
    timeLimit,
    memoryLimit,
    isActive,
    isPremium,
    author: req.user._id,
  });

  await redis.del(`problems:lab:${lab}`);
  await redis.del(`problems:section:${labSection}`);

  res.status(201).json({
    success: true,
    message: "Problem created successfully",
    problem: newProblem,
  });
});

export const updateProblem = CatchAsyncError(async (req, res, next) => {
  const problemId = req.params.id;

  if (!mongoose.Types.ObjectId.isValid(problemId)) {
    return next(new ErrorHandler("Invalid problem ID", 400));
  }

  const problem = await Problem.findById(problemId);
  if (!problem) {
    return next(new ErrorHandler("Problem not found", 404));
  }

  const body = sanitizeObject(req.body, {
    lab: "string?",
    labSection: "string?",
    category: "string?",
    title: "string?",
    slug: "string?",
    description: "string?",
    inputDescription: "string?",
    outputDescription: "string?",
    constraints: "string?",
    difficulty: "string?",
    allowedOptions: "object?",
    starterCode: "object?",
    solutionCode: "object?",
    testCases: "object?",
    tags: "string[]?",
    timeLimit: "number?",
    memoryLimit: "number?",
    isActive: "boolean?",
    isPremium: "boolean?",
    mainCodeTemplate: "object?",
  });

  const objectIdFields = ["lab", "labSection", "category"];
  for (const field of objectIdFields) {
    if (body[field] && !mongoose.Types.ObjectId.isValid(body[field])) {
      return next(new ErrorHandler(`Invalid ${field} ID`, 400));
    }
  }

  if (body.difficulty) {
    const levels = ["easy", "medium", "hard"];
    if (!levels.includes(body.difficulty)) {
      return next(new ErrorHandler("Invalid difficulty level", 400));
    }
  }

  // 🧠 Dynamically fetch valid languages from Lab
  if (body.allowedOptions) {
    let labId = body.lab || problem.lab;

    const lab = await Lab.findById(labId);
    if (!lab) {
      return next(new ErrorHandler("Associated lab not found", 404));
    }

    const validLangs = new Set(lab.labType);

    for (const lang of Object.keys(body.allowedOptions)) {
      if (!validLangs.has(lang)) {
        return next(
          new ErrorHandler(`Invalid language in allowedOptions: ${lang}`, 400)
        );
      }
    }
  }

  if (body.title && !body.slug) {
    body.slug = body.title.toLowerCase().trim().replace(/\s+/g, "-");
  }

  if (body.slug) {
    const existing = await Problem.findOne({
      slug: body.slug,
      _id: { $ne: problemId },
    });
    if (existing) {
      return next(new ErrorHandler("Slug already in use", 409));
    }
  }

  Object.assign(problem, {
    ...body,
    tags: body.tags?.map((tag) => tag.trim()) ?? problem.tags,
  });

  problem.updatedAt = new Date();
  await problem.save();

  await redis.del(`problems:lab:${problem.lab}`);
  await redis.del(`problems:section:${problem.labSection}`);

  res.status(200).json({
    success: true,
    message: "Problem updated successfully",
    problem,
  });
});

export const getProblemById = CatchAsyncError(async (req, res, next) => {
  const { problemId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(problemId)) {
    return next(new ErrorHandler("Invalid problem ID", 400));
  }

  const cacheKey = `problem:${problemId}`;
  const cached = await redis.get(cacheKey);

  if (cached) {
    return res.status(200).json({
      success: true,
      message: "Problem fetched from cache",
      problem: cached,
    });
  }

  const problem = await Problem.findById(problemId)
    .populate("lab", "name slug labType")
    .populate("labSection", "title slug")
    .populate("category", "name slug")
    .populate("testCases")
    .populate("author", "name email")
    .lean();

  if (!problem) {
    return next(new ErrorHandler("Problem not found", 404));
  }

  await redis.set(cacheKey, problem, { ex: 60 * 60 });

  res.status(200).json({
    success: true,
    message: "Problem fetched successfully",
    problem,
  });
});

export const getAllProblems = CatchAsyncError(async (req, res) => {
  const {
    page = 1,
    limit = 6,
    search = "",
    lab,
    labSection,
    category,
    difficulty,
    isActive,
    isPremium,
    sort = "createdAt",
  } = req.query;

  const pageNumber = Math.max(Number(page), 1);
  const pageSize = Math.min(Number(limit), 100);
  const skip = (pageNumber - 1) * pageSize;

  const cacheKey = `problems:${pageNumber}:${pageSize}:${search}:${lab}:${labSection}:${category}:${difficulty}:${isActive}:${isPremium}:${sort}`;
  const cached = await redis.get(cacheKey);

  if (cached) {
    return res.status(200).json({
      success: true,
      message: "Problems fetched from cache",
      ...cached,
    });
  }

  const filters = {};

  if (search) {
    filters.title = { $regex: search.trim(), $options: "i" };
  }

  if (lab && mongoose.Types.ObjectId.isValid(lab)) {
    filters.lab = lab;
  }

  if (labSection && mongoose.Types.ObjectId.isValid(labSection)) {
    filters.labSection = labSection;
  }

  if (category && mongoose.Types.ObjectId.isValid(category)) {
    filters.category = category;
  }

  if (difficulty && ["easy", "medium", "hard"].includes(difficulty)) {
    filters.difficulty = difficulty;
  }

  if (isActive !== undefined) {
    filters.isActive = isActive === "true";
  }

  if (isPremium !== undefined) {
    filters.isPremium = isPremium === "true";
  }

  const total = await Problem.countDocuments(filters);

  const problems = await Problem.find(filters)
    .sort(sort.startsWith("-") ? { [sort.slice(1)]: -1 } : { [sort]: 1 })
    .skip(skip)
    .limit(pageSize)
    .select(
      "title slug difficulty description constraints inputDescription outputDescription starterCode solutionCode allowedOptions mainCodeTemplate category isActive isPremium createdAt updatedAt"
    )
    .lean();

  const responseData = {
    total,
    page: pageNumber,
    limit: pageSize,
    problems,
  };

  await redis.set(cacheKey, responseData, { ex: 60 * 15 });

  res.status(200).json({
    success: true,
    message: "Problems fetched successfully",
    ...responseData,
  });
});

export const deleteProblem = CatchAsyncError(async (req, res, next) => {
  const { problemId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(problemId)) {
    return next(new ErrorHandler("Invalid Problem ID", 400));
  }

  const problem = await Problem.findById(problemId);
  if (!problem) {
    return next(new ErrorHandler("Problem not found", 404));
  }

  await problem.deleteOne();

  await redis.del(`problem:${problemId}`);
  await redisDelPattern("problems:*");

  res.status(200).json({
    success: true,
    message: "Problem deleted successfully",
  });
});

export const getProblemsByCategory = CatchAsyncError(async (req, res, next) => {
  const { categoryId } = req.params;

  if (!mongoose.Types.ObjectId.isValid(categoryId)) {
    return next(new ErrorHandler("Invalid Category ID", 400));
  }

  const cacheKey = `problems:category:${categoryId}`;
  const cached = await redis.get(cacheKey);

  if (cached) {
    return res.status(200).json({
      success: true,
      message: "Problems fetched from cache",
      problems: cached,
    });
  }

  const problems = await Problem.find({
    category: categoryId,
    isActive: true,
  })
    .select("title slug difficulty tags lab labSection createdAt updatedAt")
    .sort({ createdAt: -1 })
    .populate("lab", "name slug")
    .populate("labSection", "title slug")
    .lean();

  await redis.set(cacheKey, problems, { ex: 60 * 60 });

  return res.status(200).json({
    success: true,
    message: "Problems fetched successfully",
    problems,
  });
});

export const toggleProblemStatus = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Problem ID", 400));
  }

  const problem = await Problem.findById(id);
  if (!problem) {
    return next(new ErrorHandler("Problem not found", 404));
  }

  problem.isActive = !problem.isActive;
  await problem.save();

  await redis.del(`problem:${id}`);
  await redis.del(`problems:category:${problem.category}`);
  await redis.del(`problems:labSection:${problem.labSection}`);

  return res.status(200).json({
    success: true,
    message: `Problem ${
      problem.isActive ? "enabled" : "disabled"
    } successfully`,
    status: problem.isActive,
  });
});

// const OUTPUT_CHARACTER_LIMIT = 1000;

export const runCodeHandler = CatchAsyncError(async (req, res, next) => {
  const { code: rawUserCode, language, problemId } = req.body;

  console.log("📥 Incoming Request:", { language, problemId });
  console.log("🧠 Raw User Code:\n", rawUserCode);

  if (!rawUserCode || !language || !problemId) {
    console.error("❌ Missing required fields.");
    return next(
      new ErrorHandler(
        "Missing required fields: code, language, or problemId",
        400
      )
    );
  }

  console.log("🔍 Fetching problem details...");
  const problem = await Problem.findById(problemId)
    .populate("testCases")
    .populate("lab");

  if (!problem || !problem.isActive) {
    console.error("❌ Problem not found or inactive");
    return next(new ErrorHandler("Problem not found or inactive", 404));
  }

  const labType = problem.lab?.labType;
  console.log("🧪 Lab Type:", labType);

  if (!labType) {
    console.error("❌ Lab type missing");
    return next(new ErrorHandler("Associated lab type not found.", 400));
  }

  const LANGUAGE_OPTIONS = {
    DSA: ["cpp", "java", "javascript", "python"],
    Frontend: ["html", "css", "js", "reactjs", "vuejs", "angularjs"],
    Backend: ["nodejs", "django", "springboot"],
    Database: ["mysql", "mongodb", "postgresql"],
  };

  const allowedLanguages = LANGUAGE_OPTIONS[labType] || [];
  console.log("✅ Allowed Languages:", allowedLanguages);

  if (!allowedLanguages.includes(language)) {
    console.error(
      `❌ Language '${language}' not allowed for lab type '${labType}'`
    );
    return next(
      new ErrorHandler(
        `Language '${language}' is not allowed for lab type '${labType}'. Allowed: ${allowedLanguages.join(
          ", "
        )}`,
        400
      )
    );
  }

  console.log("🧩 Checking if problem allows this language:", language);
  if (!problem.allowedOptions.includes(language)) {
    console.error("❌ Language not allowed for this problem");
    return next(
      new ErrorHandler(
        `Language '${language}' is not allowed for this specific problem.`,
        400
      )
    );
  }

  const mainCodeTemplate = problem.mainCodeTemplate?.get(language);
  console.log("📄 Main Code Template:\n", mainCodeTemplate);

  if (!mainCodeTemplate) {
    console.error("❌ No mainCodeTemplate found");
    return next(
      new ErrorHandler("No main code template found for this language", 400)
    );
  }

  console.log("🔍 Fetching visible & active test cases...");
  const visibleTestCases = await TestCase.find({
    _id: { $in: problem.testCases },
    isVisible: true,
    status: "active",
  });

  console.log("✅ Visible Test Cases:", visibleTestCases.length);
  if (!visibleTestCases.length) {
    console.error("❌ No visible test cases found");
    return next(
      new ErrorHandler("No visible test cases found for this problem.", 400)
    );
  }

  // 🧪 Extract user headers & body
  const { userHeaders, userCode } = extractHeadersAndCode(rawUserCode);
  console.log("📦 Extracted Headers:\n", userHeaders);
  console.log("🧾 Extracted Code:\n", userCode);

  // 🔧 Merge headers + user code
  const fullUserCode = `${userHeaders}\n${userCode}`;
  let runtimeCodeTemplate = mainCodeTemplate;
  if (language === "cpp") {
    runtimeCodeTemplate = mainCodeTemplate.replace(
      "getline(cin, inputLine);",
      "getline(cin >> ws, inputLine);"
    );
  }
  const mergedCode = runtimeCodeTemplate.replace("{{user_code}}", fullUserCode);
  console.log("🧵 Merged Code:\n", mergedCode);

  const cleanCode = sanitizeInput(mergedCode);

  const jobResults = await pMap(
    visibleTestCases,
    async (testCase, index) => {
      const input = (testCase.input || "").trim();
      const expectedOutput = (testCase.expectedOutput || "").trim();

      console.log(`🚀 Running Test Case ${index + 1} | Input:\n${input}`);

      let result = null;
      try {
        result = await executeCode({
          code: cleanCode,
          language,
          input,
          labType,
          timeout: 3000,
        });
        console.log(`✅ Output:\n${result.output}`);
      } catch (err) {
        console.error("❌ Execution Error:", err.message);
        return {
          input,
          expectedOutput,
          actualOutput: "",
          passed: false,
          error: err.message,
          time: null,
          memory: null,
        };
      }

      let actualOutput = (result.output || "").replace(/\r\n/g, "\n").trim();

      console.log(
        `🧾 Cleaned Output (${index + 1}):`,
        JSON.stringify(actualOutput)
      );

      if (actualOutput.length > OUTPUT_CHARACTER_LIMIT) {
        actualOutput =
          actualOutput.slice(0, OUTPUT_CHARACTER_LIMIT) + "...(truncated)";
      }

      const passed = !result.error && actualOutput === expectedOutput;

      console.log(`🧪 Test Case Result ${index + 1}:`, {
        passed,
        expected: expectedOutput,
        actual: actualOutput,
      });

      return {
        input,
        expectedOutput,
        actualOutput,
        passed,
        error: result.error || null,
        time: result.time || null,
        memory: result.memory || null,
      };
    },
    { concurrency: 1 }
  );

  console.log("🎯 Final Job Results:", jobResults);

  res.status(200).json({
    success: true,
    total: jobResults.length,
    passed: jobResults.filter((t) => t.passed).length,
    testResults: jobResults,
  });
});
 
// export const runCodeHandler = CatchAsyncError(async (req, res, next) => {
//   const { code, language, problemId } = req.body;

//   console.log("➡️ Incoming request body:", req.body);

//   if (!code || !language || !problemId) {
//     return next(new ErrorHandler("Missing required fields: code, language, or problemId", 400));
//   }

//   const cleanCode = sanitizeInput(code);
//   console.log("🧼 Cleaned Code:", cleanCode);

//   const problem = await Problem.findById(problemId).populate("testCases");
//   if (!problem || !problem.isActive) {
//     return next(new ErrorHandler("Problem not found or inactive", 404));
//   }

//   const visibleTestCases = await TestCase.find({
//     _id: { $in: problem.testCases },
//     isVisible: true,
//     status: "active",
//   });

//   if (!visibleTestCases.length) {
//     return next(new ErrorHandler("No visible test cases found for this problem.", 400));
//   }

//   console.log("🧪 Visible Test Cases Found:", visibleTestCases.length);

//   const jobResults = await pMap(
//     visibleTestCases,
//     async (testCase, index) => {
//       const { input = "", expectedOutput = "" } = testCase;

//       // Normalize input
//       const raw = `${input.trim()}\n${expectedOutput.trim()}`;
//       let lines = raw.split("\n");
//       if (lines.length > 2) lines = lines.slice(0, 2);
//       if (lines.length === 1) {
//         const parts = lines[0].trim().split(/\s+/);
//         const target = parts.pop() || "";
//         lines = [parts.join(" "), target];
//       }
//       const combinedInput = lines.join("\n");

//       console.log(`📦 Enqueueing test case ${index + 1}`);

//       let jobId, jobResult;
//       try {
//         jobId = await jobQueue.enqueue({
//           code: cleanCode,
//           language,
//           input: combinedInput,
//           expectedOutput: expectedOutput.trim(),
//           timeout: 3000, // ✅ timeout passed here
//         });

//         console.log(`🆔 Job ${jobId} enqueued`);

//         // Optionally: wait/poll for result if needed
//         // For now, return dummy response
//         jobResult = {
//           input: combinedInput,
//           expectedOutput: expectedOutput.trim(),
//           actualOutput: "[waiting]",
//           passed: false,
//           error: null,
//           time: null,
//           memory: null,
//           jobId,
//         };

//       } catch (err) {
//         console.error("❌ Error enqueueing job:", err);
//         jobResult = {
//           input: combinedInput,
//           expectedOutput: expectedOutput.trim(),
//           actualOutput: "",
//           passed: false,
//           error: err.message,
//           time: null,
//           memory: null,
//         };
//       }

//       return jobResult;
//     },
//     { concurrency: 1 }
//   );

//   console.log("📤 Final job results:", jobResults);

//   res.status(200).json({
//     success: true,
//     total: jobResults.length,
//     passed: jobResults.filter((t) => t.passed).length,
//     testResults: jobResults,
//   });
// });

export const submitCode = CatchAsyncError(async (req, res, next) => {
  console.log("🔰 submitCode handler called");
  const { problemId, language, code, submissionMode = "practice" } = req.body;
  const userId = req.user?._id;
  const ipAddress = req.ip;
  const userAgent = req.headers["user-agent"];

  console.log("📥 Request body:", req.body);
  console.log("👤 User ID:", userId);

  if (!problemId || !language || !code || !userId) {
    console.log("❌ Missing required fields");
    return next(new ErrorHandler("Missing required fields", 400));
  }

  const problem = await Problem.findById(problemId).populate("testCases");
  if (!problem) {
    console.log("❌ Problem not found");
    return next(new ErrorHandler("Problem not found", 404));
  }

  if (!problem.allowedLanguages.includes(language)) {
    console.log("❌ Language not supported:", language);
    return next(
      new ErrorHandler("Language not supported for this problem", 400)
    );
  }

  const hiddenTestCases = await TestCase.find({
    _id: { $in: problem.testCases },
    isVisible: false,
    status: "active",
  });

  if (!hiddenTestCases.length) {
    console.log("❌ No hidden test cases found");
    return next(
      new ErrorHandler("No hidden test cases found for submission", 400)
    );
  }

  const sanitizedCode = sanitizeInput(code);
  let passedCount = 0;
  let totalTime = 0;
  let totalMemory = 0;

  console.log(`🧪 Running ${hiddenTestCases.length} hidden test cases`);

  const results = await Promise.all(
    hiddenTestCases.map(async (testCase, idx) => {
      console.log(`➡️ Test case #${idx + 1} (ID: ${testCase._id}) running...`);

      let result = await executeCode({
        code: sanitizedCode,
        language,
        input: testCase.input || "",
        timeout: 3000,
      });

      if (!result) {
        console.log(`⏰ Test case #${idx + 1} execution failed`);
        return {
          testCaseId: testCase._id,
          status: "Execution Failed",
          output: "",
          expectedOutput: testCase.expectedOutput,
          executionTime: 0,
          memoryUsed: 0,
          error: "No result from executor",
        };
      }

      const actualOutput = (result.output || "").trim();
      const expectedOutput = (testCase.expectedOutput || "").trim();
      const isPassed = !result.error && actualOutput === expectedOutput;

      console.log(`🔍 Test case #${idx + 1} result:`, {
        actualOutput,
        expectedOutput,
        isPassed,
        error: result.error || null,
      });

      if (isPassed) passedCount++;

      totalTime += result.time || 0;
      totalMemory += result.memory || 0;

      return {
        testCaseId: testCase._id,
        status: isPassed ? "Passed" : "Failed",
        output: actualOutput,
        expectedOutput,
        executionTime: result.time || 0,
        memoryUsed: result.memory || 0,
        error: result.error || null,
      };
    })
  );

  console.log("result", results);

  const resultStatus =
    passedCount === hiddenTestCases.length ? "Passed" : "Failed";

  console.log(
    `✅ Submission result: ${resultStatus} | Passed: ${passedCount}/${hiddenTestCases.length}`
  );

  const submission = await Submission.create({
    user: userId,
    problem: problemId,
    language,
    submittedCode: sanitizedCode,
    executionResult: resultStatus,
    executionTime: totalTime,
    memoryUsed: totalMemory,
    testCasesPassedCount: passedCount,
    totalTestCases: hiddenTestCases.length,
    detailedResults: results,
    isFinal: true,
    submissionMode,
    ipAddress,
    userAgent,
  });

  return res.status(200).json({
    success: true,
    message: "Submission completed",
    submissionId: submission._id,
    result: resultStatus,
    passedCount,
    totalTestCases: hiddenTestCases.length,
    testResults: results,
  });
});

// export const submitCode = CatchAsyncError(async (req, res, next) => {
//   console.log("🔰 submitCode handler called");
//   const { problemId, language, code, submissionMode = "practice" } = req.body;
//   const userId = req.user?._id;
//   const ipAddress = req.ip;
//   const userAgent = req.headers["user-agent"];

//   console.log("📥 Request body:", req.body);
//   console.log("👤 User ID:", userId);

//   if (!problemId || !language || !code || !userId) {
//     console.log("❌ Missing required fields");
//     return next(new ErrorHandler("Missing required fields", 400));
//   }

//   const problem = await Problem.findById(problemId).populate("testCases");
//   if (!problem) {
//     console.log("❌ Problem not found");
//     return next(new ErrorHandler("Problem not found", 404));
//   }

//   if (!problem.allowedLanguages.includes(language)) {
//     console.log("❌ Language not supported:", language);
//     return next(
//       new ErrorHandler("Language not supported for this problem", 400)
//     );
//   }

//   const hiddenTestCases = await TestCase.find({
//     _id: { $in: problem.testCases },
//     isVisible: false,
//     status: "active",
//   });

//   if (!hiddenTestCases.length) {
//     console.log("❌ No hidden test cases found");
//     return next(
//       new ErrorHandler("No hidden test cases found for submission", 400)
//     );
//   }

//   const sanitizedCode = sanitizeInput(code);
//   let passedCount = 0;
//   let totalTime = 0;
//   let totalMemory = 0;

//   console.log(`🧪 Running ${hiddenTestCases.length} hidden test cases`);

//   const results = await Promise.all(
//     hiddenTestCases.map(async (testCase, idx) => {
//       console.log(`➡️ Test case #${idx + 1} (ID: ${testCase._id}) running...`);

//       // ✅ Enqueue job
//       const jobId = await jobQueue.enqueue(
//         {
//           code: sanitizedCode,
//           language,
//           input: testCase.input || "",
//         },
//         0,
//         3000 // set timeout here
//       );

//       let result = null;
//       let attempts = 0;

//       while (attempts < 20) {
//         const data = await redis.get(`job:result:${jobId}`);
//         if (data) {
//           try {
//             result = JSON.parse(data);
//           } catch (err) {
//             console.log("⚠️ Redis result parse failed:", err);
//             result = null;
//           }
//           if (result) break;
//         }

//         await new Promise((res) => setTimeout(res, 500));
//         attempts++;
//       }

//       if (!result) {
//         console.log(`⏰ Test case #${idx + 1} timed out`);
//         return {
//           testCaseId: testCase._id,
//           status: "Time Limit Exceeded",
//           output: "",
//           expectedOutput: testCase.expectedOutput,
//           executionTime: 0,
//           memoryUsed: 0,
//           error: "Execution timed out",
//         };
//       }

//       const actualOutput = (result.output || "").trim();
//       const expectedOutput = (testCase.expectedOutput || "").trim();
//       const isPassed = !result.error && actualOutput === expectedOutput;

//       console.log(`🔍 Test case #${idx + 1} result:`, {
//         actualOutput,
//         expectedOutput,
//         isPassed,
//         error: result.error || null,
//       });

//       if (isPassed) passedCount++;

//       totalTime += result.time || 0;
//       totalMemory += result.memory || 0;

//       return {
//         testCaseId: testCase._id,
//         status: isPassed ? "Passed" : "Failed",
//         output: actualOutput,
//         expectedOutput,
//         executionTime: result.time || 0,
//         memoryUsed: result.memory || 0,
//         error: result.error || null,
//       };
//     })
//   );

//   console.log("result", results);

//   const resultStatus =
//     passedCount === hiddenTestCases.length ? "Passed" : "Failed";

//   console.log(
//     `✅ Submission result: ${resultStatus} | Passed: ${passedCount}/${hiddenTestCases.length}`
//   );

//   const submission = await Submission.create({
//     user: userId,
//     problem: problemId,
//     language,
//     submittedCode: sanitizedCode,
//     executionResult: resultStatus,
//     executionTime: totalTime,
//     memoryUsed: totalMemory,
//     testCasesPassedCount: passedCount,
//     totalTestCases: hiddenTestCases.length,
//     detailedResults: results,
//     isFinal: true,
//     submissionMode,
//     ipAddress,
//     userAgent,
//   });

//   return res.status(200).json({
//     success: true,
//     message: "Submission completed",
//     submissionId: submission._id,
//     result: resultStatus,
//     passedCount,
//     totalTestCases: hiddenTestCases.length,
//     testResults: results,
//   });
// });
