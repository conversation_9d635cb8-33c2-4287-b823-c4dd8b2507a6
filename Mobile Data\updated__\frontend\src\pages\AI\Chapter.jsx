import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import axios from "axios";
import { chapterData } from "./data/chapterData.js";

const Chapter = () => {
  const [questions, setQuestions] = useState([]);
  const [loading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeChapter, setActiveChapter] = useState(null);

  const toggleChapter = (index) => {
    setActiveChapter(activeChapter === index ? null : index);
  };

  useEffect(() => {
    // Always set mock data first to avoid undefined/null errors
    setQuestions(chapterData || []);
    setIsLoading(false);
    
    // Then try to fetch from API (the mock data will already be rendered)
    axios
      .get("/api/ai-questions")
      .then((res) => {
        if (res.data && Array.isArray(res.data) && res.data.length > 0) {
          setQuestions(res.data);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        console.error("API Error:", err);
        // Already using mock data, just log the error
        setIsLoading(false);
      });
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#1e293b]/10 to-transparent py-16 flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-12 h-12 border-4 border-cyan-400 border-t-transparent rounded-full"
        />
        <span className="ml-4 text-lg text-white/80">Loading chapters...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-[#1e293b]/10 to-transparent py-16 flex items-center justify-center">
        <div className="bg-red-900/20 backdrop-blur-sm border border-red-500/20 text-red-400 px-6 py-4 rounded-lg">
          <p className="font-medium">❌ {error}</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-b from-[#1e293b]/10 to-transparent py-16"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-5xl mx-auto px-4 md:px-6">
        {/* Header Section */}
        <motion.div variants={itemVariants} className="text-center mb-16">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-block px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg"
          >
            📚 Course Chapters
          </motion.div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
            Artificial Intelligence{" "}
            <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
              Chapters
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Explore comprehensive chapters covering all aspects of artificial intelligence and machine learning
          </p>
        </motion.div>
        
        {/* Chapter list would go here */}
        <div className="space-y-8 mt-12">
          {questions.map((chapter, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm rounded-xl overflow-hidden border border-white/10 hover:border-cyan-400/50 transition-all duration-300"
            >
              <button
                onClick={() => toggleChapter(index)}
                className="w-full px-6 py-5 flex items-center justify-between hover:bg-white/5 transition-colors duration-200"
              >
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center text-white font-bold shadow-md">
                    {index + 1}
                  </div>
                  <h3 className="text-xl font-bold text-white ml-4">{chapter.title}</h3>
                </div>
                <div className="text-purple-600">
                  <svg
                    className={`w-6 h-6 transform transition-transform duration-200 ${
                      activeChapter === index ? 'rotate-180' : ''
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>
              
              <AnimatePresence>
                {activeChapter === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 py-5 bg-[#1e293b]/30 backdrop-blur-sm border-t border-white/10">
                      <p className="text-white/80 mb-4">{chapter.description}</p>
                      <div className="flex items-center gap-2 mb-4">
                        <span className="px-2 py-1 bg-cyan-400/10 border border-cyan-400/20 rounded text-cyan-400 text-sm">
                          Difficulty: {chapter.difficulty}/5
                        </span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {chapter.tags.map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className="px-3 py-1 bg-white/5 border border-white/10 rounded-full text-white/70 text-sm hover:border-cyan-400/50 transition-colors duration-200"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default Chapter;
