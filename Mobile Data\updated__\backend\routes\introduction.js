import express from "express";
import { isAuthenticated } from "../middleware/auth.js";
import { createIntroduction, getIntroduction } from "../controllers/introduction.js";
import { upload } from "../utils/multerConfig.js";

const introductionRouter = express.Router();

introductionRouter.post(
  "/create",
  isAuthenticated,
  upload.array("icon"),
  createIntroduction
);

introductionRouter.get(
  "/:labId/:sectionId",
  isAuthenticated,
  getIntroduction
);

export default introductionRouter;
