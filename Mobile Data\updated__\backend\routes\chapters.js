import express from "express";
import { isAuthenticated } from "../middleware/auth.js";
import {
  createChapter,
  deleteChapter,
  getAllChapters,
  getChapterById,
  incrementEngagement,
  manageResources,
  restoreChapter,
  searchChapters,
  toggleChapterStatus,
  updateChapter,
  updateVisibility,
} from "../controllers/chapters.js";
import { upload } from "../utils/multerConfig.js";

const chapterRouter = express.Router();

chapterRouter.post(
  "/create",
  isAuthenticated,
  upload.single("thumbnail"),
  createChapter
);

chapterRouter.put(
  "/update/:id",
  isAuthenticated,
  upload.single("thumbnail"),
  updateChapter
);

chapterRouter.get("/all", isAuthenticated, getAllChapters);

chapterRouter.get("/search-chapter", isAuthenticated, searchChapters);

chapterRouter.get("/:id", isAuthenticated, getChapterById);

chapterRouter.patch("/restore/:id", isAuthenticated, restoreChapter);

chapterRouter.patch("/delete/:id", isAuthenticated, deleteChapter);

chapterRouter.patch("/toggle/:id", isAuthenticated, toggleChapterStatus);

chapterRouter.patch("/visibility/:id", isAuthenticated, updateVisibility);

chapterRouter.patch("/increment/:id", isAuthenticated, incrementEngagement);

chapterRouter.patch("/manager-resource/:id", isAuthenticated, manageResources);

export default chapterRouter;
