/* Community Page Styles */

/* Smooth scrolling for the entire page */
.community-page {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
.community-content::-webkit-scrollbar {
  width: 8px;
}

.community-content::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 4px;
}

.community-content::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 4px;
}

.community-content::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Animation for floating elements */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animated-gradient-text {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  animation: gradient-shift 4s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card hover effects */
.community-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.community-card:hover {
  transform: translateY(-5px) scale(1.02);
}

/* Tab transition effects */
.tab-content-enter {
  opacity: 0;
  transform: translateY(20px);
}

.tab-content-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.tab-content-exit {
  opacity: 1;
  transform: translateY(0);
}

.tab-content-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Responsive text sizing */
@media (max-width: 768px) {
  .community-title {
    font-size: 2.5rem;
  }
  
  .community-subtitle {
    font-size: 1.125rem;
  }
}

@media (max-width: 640px) {
  .community-title {
    font-size: 2rem;
  }
  
  .community-subtitle {
    font-size: 1rem;
  }
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom focus styles */
.community-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Backdrop blur fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-fallback {
    background-color: rgba(15, 23, 42, 0.8);
  }
}

/* Print styles */
@media print {
  .community-page {
    background: white !important;
    color: black !important;
  }
  
  .community-card {
    border: 1px solid #ccc !important;
    background: white !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .community-card {
    border: 2px solid currentColor;
  }
  
  .community-button {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .floating-element,
  .animated-gradient-text {
    animation: none;
  }
  
  .community-card {
    transition: none;
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  .community-content {
    color-scheme: dark;
  }
}
