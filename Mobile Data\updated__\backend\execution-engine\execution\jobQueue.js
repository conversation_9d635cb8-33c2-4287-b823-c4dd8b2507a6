// // jobQueue.js
// import { v4 as uuidv4 } from "uuid";
// import {
//   publishToQueue,
//   getJobData,
//   deleteJobData,
//   updateJobData,
// } from "../../utils/rabbitMQ.js";

// class JobQueue {
//   constructor() {
//     console.log(`JobQueue initialize with RabbitMQ`);
//   }

//   async enqueue(data, priority = 0,timeout=2000) {
//     const job = {
//       id: uuidv4(),
//       data,
//       priority,
//       createdAt: Date.now(),
//       timeout,
//       attempts: 0,
//     };

   

//     const success = await publishToQueue(job);
//     if (!success) {
//       console.error(`enqueue job failed ${job.id}`);
//       throw new Error("enqueue job failed");
//     }

//     return job.id;
//   }

//   async cancelJob(jobId) {
   
//     await deleteJobData(jobId);
//   }

//   async retryJob(job) {
//     if (job.attempts < 3) {
//       job.attempts++;
      
//       await updateJobData(job);
//       await publishToQueue(job);
//     } else {
//       console.warn(`Job ${job.id} failed`);
//       await deleteJobData(job.id);
//     }
//   }

//   async getJob(jobId) {
//     return await getJobData(jobId);
//   }
// }

// export default new JobQueue();
