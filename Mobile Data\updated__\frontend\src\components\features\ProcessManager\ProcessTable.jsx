import React from 'react';
import { motion } from 'framer-motion';

const ProcessTable = ({
  processes,
  theme,
  onStart,
  onStop,
  onRestart,
  onKill,
  onSelectProcess,
  formatUptime,
  formatMemory,
  getStatusColor
}) => {
  const getActionButton = (process, action, label, color, onClick) => (
    <button
      onClick={() => onClick(process.pid)}
      disabled={
        (action === 'start' && process.status === 'running') ||
        (action === 'stop' && process.status !== 'running') ||
        (action === 'restart' && process.status !== 'running')
      }
      className={`px-2 py-1 text-xs rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${color}`}
    >
      {label}
    </button>
  );

  return (
    <div className={`rounded-lg border overflow-hidden ${
      theme === 'dark' 
        ? 'bg-gray-800 border-gray-700' 
        : 'bg-white border-gray-200'
    }`}>
      {/* Table Header */}
      <div className={`px-6 py-4 border-b ${
        theme === 'dark' ? 'border-gray-700 bg-gray-750' : 'border-gray-200 bg-gray-50'
      }`}>
        <h2 className={`text-lg font-semibold ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          Running Processes ({processes.length})
        </h2>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className={`${
            theme === 'dark' ? 'bg-gray-750' : 'bg-gray-50'
          }`}>
            <tr>
              <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
              }`}>
                PID
              </th>
              <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
              }`}>
                Process Name
              </th>
              <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
              }`}>
                Status
              </th>
              <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
              }`}>
                CPU %
              </th>
              <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
              }`}>
                Memory
              </th>
              <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
              }`}>
                Uptime
              </th>
              <th className={`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${
                theme === 'dark' ? 'text-gray-300' : 'text-gray-500'
              }`}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody className={`divide-y ${
            theme === 'dark' ? 'divide-gray-700' : 'divide-gray-200'
          }`}>
            {processes.map((process, index) => (
              <motion.tr
                key={process.pid}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => onSelectProcess(process)}
                className={`cursor-pointer transition-colors ${
                  theme === 'dark' 
                    ? 'hover:bg-gray-700' 
                    : 'hover:bg-gray-50'
                }`}
              >
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-mono ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-900'
                }`}>
                  {process.pid}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  <div className="flex items-center">
                    <div className="text-sm font-medium">{process.name}</div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    process.status === 'running' 
                      ? 'bg-green-100 text-green-800' 
                      : process.status === 'stopped'
                      ? 'bg-yellow-100 text-yellow-800'
                      : process.status === 'sleeping'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    <div className={`w-2 h-2 rounded-full mr-1 ${getStatusColor(process.status).replace('text-', 'bg-')}`}></div>
                    {process.status}
                  </span>
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-mono ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-900'
                }`}>
                  <div className="flex items-center">
                    <div className={`w-16 bg-gray-200 rounded-full h-2 mr-2 ${
                      theme === 'dark' ? 'bg-gray-600' : 'bg-gray-200'
                    }`}>
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${Math.min(process.cpu, 100)}%` }}
                      ></div>
                    </div>
                    <span>{process.cpu.toFixed(1)}%</span>
                  </div>
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-mono ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-900'
                }`}>
                  {formatMemory(process.memory)}
                </td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-mono ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-900'
                }`}>
                  {formatUptime(process.uptime)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm">
                  <div className="flex space-x-1">
                    {getActionButton(
                      process, 
                      'start', 
                      'Start', 
                      'bg-green-600 hover:bg-green-700 text-white', 
                      onStart
                    )}
                    {getActionButton(
                      process, 
                      'stop', 
                      'Stop', 
                      'bg-yellow-600 hover:bg-yellow-700 text-white', 
                      onStop
                    )}
                    {getActionButton(
                      process, 
                      'restart', 
                      'Restart', 
                      'bg-blue-600 hover:bg-blue-700 text-white', 
                      onRestart
                    )}
                    {getActionButton(
                      process, 
                      'kill', 
                      'Kill', 
                      'bg-red-600 hover:bg-red-700 text-white', 
                      onKill
                    )}
                  </div>
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ProcessTable;
