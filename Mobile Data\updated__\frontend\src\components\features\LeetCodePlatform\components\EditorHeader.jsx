import React from 'react';
import {
  Copy,
  Terminal,
  Minimize2,
  Maximize2,
  EyeOff,
  X,
  FilePlus,
  Package,
  Globe,
  Zap,
  Monitor,
  Layers
} from 'lucide-react';

const EditorHeader = ({
  isEditorMinimized,
  showFileTree,
  showPreview,
  showApiTester,
  showConsole,
  onToggleMinimize,
  onToggleFileTree,
  onTogglePreview,
  onToggleApiTester,
  onToggleConsole,
  onCreateFile,
  onInstallPackage,
  onRefreshPreview,
  onCopyCode,
  onHideEditor,
  onCloseEditor,
  isDarkMode
}) => {
  return (
    <div className={`flex items-center justify-between px-4 py-3 border-b ${
      isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
    }`}>
      <div className="flex items-center gap-4">
        <h2 className="font-semibold">Code Editor</h2>
        
        {/* View Toggle Buttons */}
        {!isEditorMinimized && (
          <div className="flex items-center gap-2">
            <button
              onClick={onToggleFileTree}
              className={`p-2 rounded-lg transition-colors ${
                showFileTree
                  ? 'bg-blue-500 text-white'
                  : isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="Toggle File Tree"
            >
              <Layers size={16} />
            </button>
            
            <button
              onClick={onTogglePreview}
              className={`p-2 rounded-lg transition-colors ${
                showPreview
                  ? 'bg-green-500 text-white'
                  : isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="Toggle Preview"
            >
              <Monitor size={16} />
            </button>
            
            <button
              onClick={onToggleApiTester}
              className={`p-2 rounded-lg transition-colors ${
                showApiTester
                  ? 'bg-purple-500 text-white'
                  : isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="Toggle API Tester"
            >
              <Zap size={16} />
            </button>
          </div>
        )}
      </div>

      <div className="flex items-center gap-2">
        {/* File Management Actions */}
        {!isEditorMinimized && (
          <>
            <button
              onClick={onCreateFile}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                  : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="New File"
            >
              <FilePlus size={16} />
            </button>
            
            <button
              onClick={onInstallPackage}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                  : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="Install Package"
            >
              <Package size={16} />
            </button>

            <button
              onClick={onRefreshPreview}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                  : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="Refresh Preview"
            >
              <Globe size={16} />
            </button>

            <button
              onClick={onCopyCode}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                  : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="Copy Code"
            >
              <Copy size={16} />
            </button>

            <button
              onClick={onToggleConsole}
              className={`p-2 rounded-lg transition-colors ${
                showConsole
                  ? 'text-blue-500 bg-blue-500/10'
                  : isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
              }`}
              title="Toggle Console"
            >
              <Terminal size={16} />
            </button>
          </>
        )}

        {/* Minimize/Maximize Button */}
        <button
          onClick={onToggleMinimize}
          className={`p-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
              : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
          }`}
          title={isEditorMinimized ? "Maximize Editor" : "Minimize Editor"}
        >
          {isEditorMinimized ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
        </button>

        {/* Hide Button */}
        <button
          onClick={onHideEditor}
          className={`p-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
              : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
          }`}
          title="Hide Editor"
        >
          <EyeOff size={16} />
        </button>

        {/* Close Button */}
        <button
          onClick={onCloseEditor}
          className={`p-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'text-red-400 hover:text-red-300 hover:bg-red-900/20'
              : 'text-red-600 hover:text-red-700 hover:bg-red-100'
          }`}
          title="Close Editor"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );
};

export default EditorHeader;
