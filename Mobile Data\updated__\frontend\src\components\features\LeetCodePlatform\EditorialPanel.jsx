import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Clock, 
  Database, 
  Lightbulb, 
  Code, 
  ChevronRight,
  ChevronDown,
  Star,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';

const EditorialPanel = ({ isDarkMode }) => {
  const [expandedSections, setExpandedSections] = useState({
    approach1: true,
    approach2: false,
    approach3: false
  });

  const [selectedLanguage, setSelectedLanguage] = useState('javascript');

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const editorial = {
    overview: "This problem asks us to find two numbers in an array that add up to a specific target. We need to return the indices of these two numbers.",
    approaches: [
      {
        id: 'approach1',
        title: 'Approach 1: Brute Force',
        timeComplexity: 'O(n²)',
        spaceComplexity: 'O(1)',
        difficulty: 'Easy',
        intuition: 'The brute force approach is straightforward. We check every pair of numbers to see if they add up to the target.',
        algorithm: [
          'Loop through each element x and find if there is another value that equals to target - x',
          'Use nested loops to check all possible pairs',
          'Return the indices when a valid pair is found'
        ],
        implementation: {
          javascript: `var twoSum = function(nums, target) {
    for (let i = 0; i < nums.length; i++) {
        for (let j = i + 1; j < nums.length; j++) {
            if (nums[i] + nums[j] === target) {
                return [i, j];
            }
        }
    }
    return [];
};`,
          python: `def twoSum(nums, target):
    for i in range(len(nums)):
        for j in range(i + 1, len(nums)):
            if nums[i] + nums[j] == target:
                return [i, j]
    return []`,
          java: `public int[] twoSum(int[] nums, int target) {
    for (int i = 0; i < nums.length; i++) {
        for (int j = i + 1; j < nums.length; j++) {
            if (nums[i] + nums[j] == target) {
                return new int[]{i, j};
            }
        }
    }
    return new int[0];
}`
        },
        pros: ['Simple to understand and implement', 'No extra space needed'],
        cons: ['Time complexity is O(n²)', 'Not efficient for large arrays']
      },
      {
        id: 'approach2',
        title: 'Approach 2: Hash Map (Two-pass)',
        timeComplexity: 'O(n)',
        spaceComplexity: 'O(n)',
        difficulty: 'Medium',
        intuition: 'We can use a hash map to store the values and their indices. First pass to build the map, second pass to find the complement.',
        algorithm: [
          'Create a hash map to store value -> index mapping',
          'First pass: populate the hash map with all values and indices',
          'Second pass: for each element, check if target - element exists in the map',
          'Return the indices when found'
        ],
        implementation: {
          javascript: `var twoSum = function(nums, target) {
    const map = new Map();
    
    // First pass: build hash map
    for (let i = 0; i < nums.length; i++) {
        map.set(nums[i], i);
    }
    
    // Second pass: find complement
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement) && map.get(complement) !== i) {
            return [i, map.get(complement)];
        }
    }
    
    return [];
};`,
          python: `def twoSum(nums, target):
    # First pass: build hash map
    num_map = {}
    for i, num in enumerate(nums):
        num_map[num] = i
    
    # Second pass: find complement
    for i, num in enumerate(nums):
        complement = target - num
        if complement in num_map and num_map[complement] != i:
            return [i, num_map[complement]]
    
    return []`,
          java: `public int[] twoSum(int[] nums, int target) {
    Map<Integer, Integer> map = new HashMap<>();
    
    // First pass: build hash map
    for (int i = 0; i < nums.length; i++) {
        map.put(nums[i], i);
    }
    
    // Second pass: find complement
    for (int i = 0; i < nums.length; i++) {
        int complement = target - nums[i];
        if (map.containsKey(complement) && map.get(complement) != i) {
            return new int[] { i, map.get(complement) };
        }
    }
    
    return new int[0];
}`
        },
        pros: ['Linear time complexity', 'Easy to understand'],
        cons: ['Requires two passes', 'Extra space for hash map']
      },
      {
        id: 'approach3',
        title: 'Approach 3: Hash Map (One-pass)',
        timeComplexity: 'O(n)',
        spaceComplexity: 'O(n)',
        difficulty: 'Medium',
        intuition: 'We can solve this in one pass by checking if the complement exists while building the hash map.',
        algorithm: [
          'Iterate through the array once',
          'For each element, calculate its complement (target - current element)',
          'Check if the complement exists in the hash map',
          'If found, return the indices',
          'If not found, add the current element and its index to the hash map'
        ],
        implementation: {
          javascript: `var twoSum = function(nums, target) {
    const map = new Map();
    
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        
        map.set(nums[i], i);
    }
    
    return [];
};`,
          python: `def twoSum(nums, target):
    num_map = {}
    
    for i, num in enumerate(nums):
        complement = target - num
        
        if complement in num_map:
            return [num_map[complement], i]
        
        num_map[num] = i
    
    return []`,
          java: `public int[] twoSum(int[] nums, int target) {
    Map<Integer, Integer> map = new HashMap<>();
    
    for (int i = 0; i < nums.length; i++) {
        int complement = target - nums[i];
        
        if (map.containsKey(complement)) {
            return new int[] { map.get(complement), i };
        }
        
        map.put(nums[i], i);
    }
    
    return new int[0];
}`
        },
        pros: ['Optimal time complexity O(n)', 'Single pass through array', 'Most efficient solution'],
        cons: ['Extra space for hash map', 'Slightly more complex logic']
      }
    ],
    keyInsights: [
      'Hash maps provide O(1) average lookup time',
      'Trading space for time complexity is often worthwhile',
      'One-pass solutions are generally more efficient than multi-pass',
      'Always consider the complement when looking for pairs'
    ],
    followUp: 'Can you solve this problem if the input array is sorted? (Hint: Two pointers technique)'
  };

  const getComplexityColor = (complexity) => {
    if (complexity.includes('O(1)') || complexity.includes('O(n)')) {
      return 'text-green-500';
    } else if (complexity.includes('O(n²)')) {
      return 'text-red-500';
    } else {
      return 'text-yellow-500';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="p-6 h-full overflow-auto"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <BookOpen className="text-blue-500" size={24} />
          <h2 className="text-xl font-bold">Editorial</h2>
        </div>
        
        <div className="flex items-center gap-2">
          <ThumbsUp size={16} className="text-green-500" />
          <span className="text-sm">1.2k</span>
          <ThumbsDown size={16} className="text-red-500" />
          <span className="text-sm">45</span>
        </div>
      </div>

      {/* Overview */}
      <div className={`p-4 rounded-lg border mb-6 ${
        isDarkMode 
          ? 'bg-blue-900/20 border-blue-700/30' 
          : 'bg-blue-50 border-blue-200'
      }`}>
        <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-blue-400' : 'text-blue-700'}`}>
          📋 Problem Overview
        </h3>
        <p className={`text-sm ${isDarkMode ? 'text-blue-300' : 'text-blue-600'}`}>
          {editorial.overview}
        </p>
      </div>

      {/* Approaches */}
      <div className="space-y-4 mb-6">
        <h3 className="text-lg font-semibold">Solution Approaches</h3>
        
        {editorial.approaches.map((approach) => (
          <div
            key={approach.id}
            className={`border rounded-lg overflow-hidden ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}
          >
            {/* Approach Header */}
            <button
              onClick={() => toggleSection(approach.id)}
              className={`w-full p-4 text-left transition-colors ${
                isDarkMode 
                  ? 'bg-gray-800 hover:bg-gray-750' 
                  : 'bg-gray-50 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {expandedSections[approach.id] ? 
                    <ChevronDown size={16} /> : 
                    <ChevronRight size={16} />
                  }
                  <h4 className="font-semibold">{approach.title}</h4>
                </div>
                
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Clock size={14} />
                    <span className={getComplexityColor(approach.timeComplexity)}>
                      {approach.timeComplexity}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Database size={14} />
                    <span className={getComplexityColor(approach.spaceComplexity)}>
                      {approach.spaceComplexity}
                    </span>
                  </div>
                </div>
              </div>
            </button>

            {/* Approach Content */}
            {expandedSections[approach.id] && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="border-t border-gray-200 dark:border-gray-700"
              >
                <div className="p-4 space-y-4">
                  {/* Intuition */}
                  <div>
                    <h5 className="font-semibold mb-2 flex items-center gap-2">
                      <Lightbulb size={16} className="text-yellow-500" />
                      Intuition
                    </h5>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {approach.intuition}
                    </p>
                  </div>

                  {/* Algorithm */}
                  <div>
                    <h5 className="font-semibold mb-2">Algorithm</h5>
                    <ol className={`text-sm space-y-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {approach.algorithm.map((step, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-500 mr-2 font-semibold">{index + 1}.</span>
                          <span>{step}</span>
                        </li>
                      ))}
                    </ol>
                  </div>

                  {/* Implementation */}
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="font-semibold flex items-center gap-2">
                        <Code size={16} />
                        Implementation
                      </h5>
                      <select
                        value={selectedLanguage}
                        onChange={(e) => setSelectedLanguage(e.target.value)}
                        className={`px-2 py-1 text-xs rounded border ${
                          isDarkMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                      >
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                      </select>
                    </div>
                    
                    <div className={`rounded-lg border overflow-hidden ${
                      isDarkMode ? 'border-gray-700' : 'border-gray-200'
                    }`}>
                      <pre className={`p-4 text-sm font-mono overflow-auto ${
                        isDarkMode ? 'bg-gray-900 text-gray-300' : 'bg-gray-50 text-gray-800'
                      }`}>
                        {approach.implementation[selectedLanguage]}
                      </pre>
                    </div>
                  </div>

                  {/* Pros and Cons */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="font-semibold mb-2 text-green-500">✅ Pros</h5>
                      <ul className={`text-sm space-y-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {approach.pros.map((pro, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-green-500 mr-2">•</span>
                            <span>{pro}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 className="font-semibold mb-2 text-red-500">❌ Cons</h5>
                      <ul className={`text-sm space-y-1 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                        {approach.cons.map((con, index) => (
                          <li key={index} className="flex items-start">
                            <span className="text-red-500 mr-2">•</span>
                            <span>{con}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        ))}
      </div>

      {/* Key Insights */}
      <div className={`p-4 rounded-lg border mb-6 ${
        isDarkMode 
          ? 'bg-purple-900/20 border-purple-700/30' 
          : 'bg-purple-50 border-purple-200'
      }`}>
        <h3 className={`font-semibold mb-3 ${isDarkMode ? 'text-purple-400' : 'text-purple-700'}`}>
          💡 Key Insights
        </h3>
        <ul className={`space-y-2 ${isDarkMode ? 'text-purple-300' : 'text-purple-600'}`}>
          {editorial.keyInsights.map((insight, index) => (
            <li key={index} className="flex items-start text-sm">
              <Star size={14} className="mr-2 mt-0.5 flex-shrink-0" />
              <span>{insight}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* Follow-up */}
      <div className={`p-4 rounded-lg border ${
        isDarkMode 
          ? 'bg-yellow-900/20 border-yellow-700/30' 
          : 'bg-yellow-50 border-yellow-200'
      }`}>
        <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-yellow-400' : 'text-yellow-700'}`}>
          🚀 Follow-up Question
        </h3>
        <p className={`text-sm ${isDarkMode ? 'text-yellow-300' : 'text-yellow-600'}`}>
          {editorial.followUp}
        </p>
      </div>
    </motion.div>
  );
};

export default EditorialPanel;
