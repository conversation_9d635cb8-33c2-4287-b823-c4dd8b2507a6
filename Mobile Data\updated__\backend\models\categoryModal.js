import mongoose from "mongoose";

const categorySchema = new mongoose.Schema(
  {
    lab: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Lab",
      required: true,
    },
    labSection: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LabSection",
      required: true,
      index: true,
    },

    parentCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Category",
      default: null,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      unique: true,
      index: true,
    },
    description: {
      type: String,
    },
    icon: {
      public_id: String,
      secure_url: String,
    },
    order: {
      type: Number,
      default: 0,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    ancestors: [
      {
        _id: mongoose.Schema.Types.ObjectId,
        name: String,
        slug: String,
      },
    ],
    metaTitle: {
      type: String,
    },
    metaDescription: {
      type: String,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  { timestamps: true }
);

categorySchema.index({ lab: 1, labSection: 1, slug: 1 }, { unique: true });

const Category = mongoose.model("Category", categorySchema);
export default Category;
