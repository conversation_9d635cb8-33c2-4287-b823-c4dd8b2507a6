import express from "express";
import { connectDB } from "./utils/db.js";
import dotenv from "dotenv";
import cors from "cors";
import { ErrorMiddleware } from "./middleware/error.js";
import userRouter from "./routes/user.js";
import registrationRouter from "./routes/registration.js";
import internshipRouter from "./routes/internship.js";
import cookieParser from "cookie-parser";
import morgan from "morgan";
import taskRoutes from "./routes/taskRoutes.js";
import messageRoutes from "./routes/messageRoutes.js";
import questionRoutes from "./routes/questionRoutes.js";
import labRouter from "./routes/lab.js";
import labSectionRouter from "./routes/labSection.js";
import categoryRouter from "./routes/category.js";
import problemRouter from "./routes/problem.js";
import testCasesRouter from "./routes/testCases.js";
import introductionRouter from "./routes/introduction.js";
import chapterRouter from "./routes/chapters.js";
import contentRouter from "./routes/content.js";
// import { connectRabbitMQ, consumeFromQueue } from "./utils/rabbitMQ.js";
// import processJob from "./utils/listenerQueue.js";

dotenv.config();

console.log("app working");

const app = express();
const PORT = process.env.PORT || 5000;

const startServer = async () => {
  try {
    console.log("Connecting to DB...");
    await connectDB();

    // await connectRabbitMQ();
    // await consumeFromQueue(processJob);

    const allowedOrigins = [
      process.env.FRONTEND_URL,
      "http://localhost:8000",
      "http://localhost:5173",
    ];

    const corsOptions = {
      origin: function (origin, callback) {
        if (!origin || allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error("Not allowed by CORS"));
        }
      },
      credentials: true,
      methods: "GET,POST,PUT,DELETE,PATCH",
      allowedHeaders: "Content-Type,Authorization,Origin,Accept",
    };

    app.use(cookieParser());
    app.use(cors(corsOptions));
    app.use(express.json({ limit: "50mb" }));
    app.use(express.urlencoded({ extended: true, limit: "50mb" }));
    app.use(morgan("dev"));

    app.get("/", (req, res) => {
      res.status(200).json({
        success: true,
        message: "API WORKING",
      });
    });

    app.use("/api/v1/auth", userRouter);
    app.use("/api/v1", registrationRouter);
    app.use("/api/v1", internshipRouter);
    app.use("/api/tasks", taskRoutes);
    app.use("/api/v1/lab", labRouter);
    app.use("/api/v1/labsection", labSectionRouter);
    app.use("/api/v1/introduction", introductionRouter);
    app.use("/api/v1/category", categoryRouter);
    app.use("/api/v1/problem", problemRouter);
    app.use("/api/v1/testcases", testCasesRouter);
    app.use("/api/v1/chapters", chapterRouter);
    app.use("/api/questions", questionRoutes);
    app.use("/api/messages", messageRoutes);
    app.use("/api/v1/content",contentRouter)

    app.all("*", (req, res, next) => {
      const err = new Error(`Route ${req.originalUrl} not found`);
      err.statusCode = 404;
      next(err);
    });

    app.use(ErrorMiddleware);

    // await seedProblems(labId, labSectionId, categoryId, authorId);

    app.listen(PORT, () => {
      console.log(`Server is running on PORT ${PORT}`);
    });
  } catch (error) {
    console.log(error.message)
    process.exit(1)
  }
};

// 👇 Run the startup
startServer();
