# UpCoding Platform - Reusable Components

This directory contains the refactored, reusable components for the UpCoding Platform's Code Editor Section. The original monolithic component has been broken down into smaller, focused, and maintainable components.

## 📁 Component Structure

```
components/
├── ActionButtons.jsx      # Project management actions (export, reset, run, deploy)
├── ApiTester.jsx         # API testing panel with HTTP client functionality
├── Console.jsx           # Console output display with controls
├── EditorHeader.jsx      # Header with all toolbar buttons and toggles
├── FileTree.jsx          # File explorer sidebar with CRUD operations
├── LivePreview.jsx       # Live preview iframe with HTML/CSS/JS rendering
├── MonacoEditor.jsx      # Monaco code editor wrapper
└── README.md            # This documentation file

hooks/
└── useFileManager.js     # Custom hook for file system operations
```

## 🎯 Component Details

### 1. **EditorHeader.jsx**
**Purpose**: Main toolbar with all editor controls
**Props**:
- `isEditorMinimized`, `showFileTree`, `showPreview`, `showApiTester`, `showConsole` - UI state
- `onToggle*` functions - Toggle handlers for each panel
- `onCreateFile`, `onInstallPackage`, `onRefreshPreview` - Action handlers
- `onCopyCode`, `onHideEditor`, `onCloseEditor` - Editor actions
- `isDarkMode` - Theme state

### 2. **FileTree.jsx**
**Purpose**: File explorer sidebar with file management
**Props**:
- `files` - File system object
- `activeFile` - Currently selected file
- `onFileSelect`, `onFileDelete`, `onCreateFile` - File operation handlers
- `isDarkMode` - Theme state

### 3. **MonacoEditor.jsx**
**Purpose**: Code editor wrapper with syntax highlighting
**Props**:
- `code` - Current file content
- `activeFile` - Current file name
- `files` - File system for language detection
- `onChange` - Content change handler
- `isDarkMode` - Theme state

### 4. **LivePreview.jsx**
**Purpose**: Live preview of HTML/CSS/JS code
**Props**:
- `files` - File system for preview generation
- `previewKey` - Key for iframe refresh
- `onRefresh` - Refresh handler
- `isDarkMode` - Theme state

### 5. **ApiTester.jsx**
**Purpose**: HTTP API testing panel
**Props**:
- `isDarkMode` - Theme state
**Features**:
- Self-contained state management
- Support for GET, POST, PUT, DELETE, PATCH
- JSON headers and body support
- Response display with status codes

### 6. **Console.jsx**
**Purpose**: Console output display with controls
**Props**:
- `output` - Console output text
- `consoleHeight` - Current height
- `onClearOutput`, `onHeightChange`, `onHide`, `onClose` - Control handlers
- `isDarkMode` - Theme state

### 7. **ActionButtons.jsx**
**Purpose**: Project management actions
**Props**:
- `files`, `activeFile` - Project state
- `onResetProject`, `onRunProject` - Action handlers
- `isDarkMode` - Theme state
**Features**:
- Export project as JSON
- Reset to default files
- Run project (show preview)
- Deploy simulation

## 🔧 Custom Hook

### **useFileManager.js**
**Purpose**: Centralized file system operations
**Returns**:
- `files` - File system state
- `activeFile` - Current file
- `createNewFile`, `deleteFile`, `switchFile` - File operations
- `updateFileContent` - Content updates
- `installPackage` - Package management simulation
- `resetProject` - Reset to defaults

## 🎨 Benefits of Refactoring

### ✅ **Maintainability**
- **Single Responsibility**: Each component has one clear purpose
- **Smaller Files**: Easier to understand and modify
- **Clear Interfaces**: Well-defined props and responsibilities

### ✅ **Reusability**
- **Modular Design**: Components can be used independently
- **Configurable**: Props allow customization for different use cases
- **Testable**: Smaller components are easier to unit test

### ✅ **Performance**
- **Selective Re-renders**: Only affected components re-render
- **Code Splitting**: Potential for lazy loading
- **Memory Efficiency**: Better garbage collection

### ✅ **Developer Experience**
- **Better IntelliSense**: Clearer prop definitions
- **Easier Debugging**: Isolated component logic
- **Team Collaboration**: Multiple developers can work on different components

## 🚀 Usage Example

```jsx
import React, { useState } from 'react';
import EditorHeader from './components/EditorHeader';
import FileTree from './components/FileTree';
import MonacoEditor from './components/MonacoEditor';
import { useFileManager } from './hooks/useFileManager';

const MyCodeEditor = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const { files, activeFile, switchFile } = useFileManager();

  return (
    <div className="flex">
      <FileTree
        files={files}
        activeFile={activeFile}
        onFileSelect={switchFile}
        isDarkMode={isDarkMode}
      />
      <MonacoEditor
        files={files}
        activeFile={activeFile}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};
```

## 🔄 Migration from Monolithic Component

The original 1000+ line `CodeEditorSection.jsx` has been refactored into:
- **7 focused components** (average ~50-100 lines each)
- **1 custom hook** for state management
- **Clean separation of concerns**
- **All original functionality preserved**

## 📊 Component Metrics

| Component | Lines of Code | Primary Responsibility |
|-----------|---------------|----------------------|
| EditorHeader | ~150 | Toolbar and controls |
| FileTree | ~70 | File management UI |
| MonacoEditor | ~50 | Code editing |
| LivePreview | ~60 | HTML preview |
| ApiTester | ~120 | HTTP testing |
| Console | ~90 | Output display |
| ActionButtons | ~80 | Project actions |
| useFileManager | ~150 | File operations |

**Total**: ~770 lines (vs 1000+ original)
**Reduction**: ~25% code reduction with better organization

## 🎯 Future Enhancements

With this modular structure, future enhancements become easier:
- **Plugin System**: Add new panels as separate components
- **Theming**: Enhanced theme support per component
- **Testing**: Individual component testing
- **Documentation**: Component-specific documentation
- **Performance**: Lazy loading and code splitting
