export const topics = [
  {
    title: "Data and Expression",
    icon: "fas fa-database",
    sectionId: "data-expression",
    color: "from-blue-500 to-cyan-500",
  },
  {
    title: "Control Structure",
    icon: "fas fa-project-diagram",
    sectionId: "control-structure",
    color: "from-purple-500 to-pink-500",
  },
  { 
    title: "List", 
    icon: "fas fa-stream", 
    sectionId: "list",
    color: "from-green-500 to-teal-500",
  },
  { 
    title: "Functions", 
    icon: "fas fa-code", 
    sectionId: "functions",
    color: "from-orange-500 to-red-500",
  },
  { 
    title: "Objects", 
    icon: "fas fa-cube", 
    sectionId: "objects",
    color: "from-indigo-500 to-purple-500",
  },
  {
    title: "Modular Design",
    icon: "fas fa-th-large",
    sectionId: "modular-design",
    color: "from-pink-500 to-rose-500",
  },
  { 
    title: "Text Files", 
    icon: "fas fa-file-alt", 
    sectionId: "text-files",
    color: "from-yellow-500 to-orange-500",
  },
  { 
    title: "Dictionaries", 
    icon: "fas fa-key", 
    sectionId: "dictionaries",
    color: "from-cyan-500 to-blue-500",
  },
  { 
    title: "OOP", 
    icon: "fas fa-object-group", 
    sectionId: "oop",
    color: "from-emerald-500 to-green-500",
  },
  { 
    title: "Recursion", 
    icon: "fas fa-sync-alt", 
    sectionId: "recursion",
    color: "from-violet-500 to-purple-500",
  },
];

export const allCaseStudies = {
  "data-expression": [
    {
      title: "Basic Data Types and Variables",
      objective: "Understand and differentiate between basic data types in Python.",
      scenario: "Write a Python script to define variables of different types (integer, float, string, boolean). Print the type of each variable using the type() function.",
      keyConcepts: "Integers, Floats, Strings, Booleans, type() function.",
      solution: `# Defining variables
integer_var = 10
float_var = 10.5
string_var = "Hello, Python!"
boolean_var = True

# Printing types
print(type(integer_var))
print(type(float_var))
print(type(string_var))
print(type(boolean_var))`,
    },
    {
      title: "Arithmetic Operations",
      objective: "Perform and understand basic arithmetic operations in Python.",
      scenario: "Create a Python script that takes two numbers as input from the user and performs addition, subtraction, multiplication, division, and modulus operations. Display the results.",
      keyConcepts: "Arithmetic Operators (+, -, *, /, %), input() function.",
      solution: `# Taking input from user
num1 = float(input("Enter first number: "))
num2 = float(input("Enter second number: "))

# Performing operations
print("Addition:", num1 + num2)
print("Subtraction:", num1 - num2)
print("Multiplication:", num1 * num2)
print("Division:", num1 / num2)
print("Modulus:", num1 % num2)`,
    },
    {
      title: "String Operations",
      objective: "Learn basic string manipulation in Python.",
      scenario: "Write a Python script that demonstrates string concatenation, slicing, and common string methods.",
      keyConcepts: "String concatenation, slicing, string methods.",
      solution: `# String operations
text1 = "Hello"
text2 = "World"

# Concatenation
combined = text1 + " " + text2
print("Combined:", combined)

# Slicing
print("First 5 characters:", combined[:5])
print("Last 5 characters:", combined[-5:])

# String methods
print("Uppercase:", combined.upper())
print("Lowercase:", combined.lower())
print("Length:", len(combined))`,
    },
  ],
  "control-structure": [
    {
      title: "Conditional Statements",
      objective: "Implement conditional logic using if-else statements.",
      scenario: "Write a program that checks if a number is positive, negative, or zero, and determines if it's even or odd.",
      keyConcepts: "if-else statements, comparison operators, logical operators.",
      solution: `# Getting input
number = int(input("Enter a number: "))

# Checking positive/negative/zero
if number > 0:
    print("The number is positive")
elif number < 0:
    print("The number is negative")
else:
    print("The number is zero")

# Checking even/odd
if number % 2 == 0:
    print("The number is even")
else:
    print("The number is odd")`,
    },
    {
      title: "For Loops",
      objective: "Learn how to use for loops for iteration.",
      scenario: "Write a Python script that uses a for loop to print numbers from 1 to 10 and calculate their sum.",
      keyConcepts: "for loops, range() function, iteration.",
      solution: `# Using for loop to print numbers and calculate sum
total = 0
for i in range(1, 11):
    print(f"Number: {i}")
    total += i

print(f"Sum of numbers 1 to 10: {total}")`,
    },
    {
      title: "While Loops",
      objective: "Understand while loops and their applications.",
      scenario: "Create a simple guessing game using a while loop where the user has to guess a number between 1 and 100.",
      keyConcepts: "while loops, user input, random numbers.",
      solution: `import random

# Generate random number
secret_number = random.randint(1, 100)
guess = 0
attempts = 0

while guess != secret_number:
    guess = int(input("Guess a number between 1 and 100: "))
    attempts += 1
    
    if guess < secret_number:
        print("Too low!")
    elif guess > secret_number:
        print("Too high!")
    else:
        print(f"Congratulations! You guessed it in {attempts} attempts!")`,
    },
  ],
  "list": [
    {
      title: "Creating and Accessing Lists",
      objective: "Learn how to create and access list elements.",
      scenario: "Create a list of your favorite fruits and demonstrate different ways to access elements.",
      keyConcepts: "List creation, indexing, slicing.",
      solution: `# Creating a list of fruits
fruits = ["apple", "banana", "orange", "grape", "kiwi"]

# Accessing elements
print("First fruit:", fruits[0])
print("Last fruit:", fruits[-1])
print("First three fruits:", fruits[:3])
print("Last two fruits:", fruits[-2:])

# Accessing with loops
for i, fruit in enumerate(fruits):
    print(f"{i+1}. {fruit}")`,
    },
    {
      title: "List Methods",
      objective: "Master common list methods for manipulation.",
      scenario: "Demonstrate various list methods like append, insert, remove, and sort.",
      keyConcepts: "append(), insert(), remove(), sort(), reverse().",
      solution: `# Starting with a list
numbers = [3, 1, 4, 1, 5]

# Adding elements
numbers.append(9)
numbers.insert(0, 2)

# Removing elements
numbers.remove(1)  # removes first occurrence
popped = numbers.pop()  # removes last element

# Sorting and reversing
numbers.sort()
print("Sorted:", numbers)

numbers.reverse()
print("Reversed:", numbers)
print("Popped element:", popped)`,
    },
    {
      title: "List Comprehensions",
      objective: "Learn to create lists efficiently using comprehensions.",
      scenario: "Use list comprehensions to create lists of squares, even numbers, and filtered data.",
      keyConcepts: "List comprehensions, filtering, mapping.",
      solution: `# Basic list comprehension
squares = [x**2 for x in range(1, 11)]
print("Squares:", squares)

# List comprehension with condition
even_numbers = [x for x in range(1, 21) if x % 2 == 0]
print("Even numbers:", even_numbers)

# List comprehension with string manipulation
words = ["hello", "world", "python", "programming"]
upper_words = [word.upper() for word in words if len(word) > 5]
print("Long words in uppercase:", upper_words)`,
    },
  ],
  "functions": [
    {
      title: "Defining and Calling Functions",
      objective: "Learn the basics of function definition and calling.",
      scenario: "Create functions to perform basic calculations and call them with different arguments.",
      keyConcepts: "Function definition, parameters, return values.",
      solution: `# Defining functions
def greet(name):
    return f"Hello, {name}!"

def calculate_area(length, width):
    return length * width

def is_even(number):
    return number % 2 == 0

# Calling functions
print(greet("Alice"))
print(f"Area: {calculate_area(5, 3)}")
print(f"Is 4 even? {is_even(4)}")
print(f"Is 7 even? {is_even(7)}")`,
    },
    {
      title: "Function Parameters and Arguments",
      objective: "Understand different types of function parameters.",
      scenario: "Create functions with default parameters, keyword arguments, and variable-length arguments.",
      keyConcepts: "Default parameters, *args, **kwargs.",
      solution: `# Function with default parameters
def greet(name, greeting="Hello"):
    return f"{greeting}, {name}!"

# Function with variable arguments
def sum_all(*numbers):
    return sum(numbers)

# Function with keyword arguments
def create_profile(**info):
    for key, value in info.items():
        print(f"{key}: {value}")

# Using the functions
print(greet("Bob"))
print(greet("Alice", "Hi"))
print(f"Sum: {sum_all(1, 2, 3, 4, 5)}")
create_profile(name="John", age=25, city="New York")`,
    },
    {
      title: "Lambda Functions",
      objective: "Learn to use lambda functions for simple operations.",
      scenario: "Use lambda functions with map(), filter(), and sort() functions.",
      keyConcepts: "Lambda functions, map(), filter(), sort().",
      solution: `# Lambda functions
square = lambda x: x**2
add = lambda x, y: x + y

print(f"Square of 5: {square(5)}")
print(f"Sum of 3 and 7: {add(3, 7)}")

# Using lambda with map and filter
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

squares = list(map(lambda x: x**2, numbers))
even_numbers = list(filter(lambda x: x % 2 == 0, numbers))

print("Squares:", squares)
print("Even numbers:", even_numbers)

# Using lambda with sort
students = [("Alice", 85), ("Bob", 90), ("Charlie", 78)]
students.sort(key=lambda x: x[1])  # Sort by grade
print("Students sorted by grade:", students)`,
    },
  ],
  "objects": [
    {
      title: "Classes and Objects",
      objective: "Learn to create classes and instantiate objects.",
      scenario: "Create a simple class to represent a car with attributes and methods.",
      keyConcepts: "Class definition, __init__ method, instance variables.",
      solution: `# Defining a Car class
class Car:
    def __init__(self, make, model, year):
        self.make = make
        self.model = model
        self.year = year
        self.odometer = 0
    
    def drive(self, miles):
        self.odometer += miles
        print(f"Drove {miles} miles. Total: {self.odometer} miles")
    
    def display_info(self):
        print(f"{self.year} {self.make} {self.model}")
        print(f"Odometer: {self.odometer} miles")

# Creating and using objects
car1 = Car("Toyota", "Camry", 2022)
car2 = Car("Honda", "Civic", 2021)

car1.display_info()
car1.drive(100)
car1.drive(50)
car1.display_info()`,
    },
    {
      title: "Inheritance",
      objective: "Understand inheritance and method overriding.",
      scenario: "Create a base Animal class and derive specific animal classes from it.",
      keyConcepts: "Inheritance, super(), method overriding.",
      solution: `# Base class
class Animal:
    def __init__(self, name, species):
        self.name = name
        self.species = species
    
    def make_sound(self):
        return "Some generic animal sound"
    
    def info(self):
        return f"{self.name} is a {self.species}"

# Derived classes
class Dog(Animal):
    def __init__(self, name, breed):
        super().__init__(name, "Dog")
        self.breed = breed
    
    def make_sound(self):
        return "Woof!"

class Cat(Animal):
    def __init__(self, name, breed):
        super().__init__(name, "Cat")
        self.breed = breed
    
    def make_sound(self):
        return "Meow!"

# Using inheritance
dog = Dog("Buddy", "Golden Retriever")
cat = Cat("Whiskers", "Persian")

print(dog.info())
print(f"{dog.name} says: {dog.make_sound()}")
print(cat.info())
print(f"{cat.name} says: {cat.make_sound()}")`,
    },
  ],
  "modular-design": [
    {
      title: "Creating Modules",
      objective: "Learn to organize code into reusable modules.",
      scenario: "Create a math utilities module and import functions from it.",
      keyConcepts: "Module creation, import statements, code organization.",
      solution: `# math_utils.py (separate file)
def add(a, b):
    return a + b

def multiply(a, b):
    return a * b

def power(base, exponent):
    return base ** exponent

def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)

# main.py (using the module)
import math_utils

# Using functions from the module
print(f"Addition: {math_utils.add(5, 3)}")
print(f"Multiplication: {math_utils.multiply(4, 7)}")
print(f"Power: {math_utils.power(2, 8)}")
print(f"Factorial: {math_utils.factorial(5)}")`,
    },
    {
      title: "Package Organization",
      objective: "Understand how to organize modules into packages.",
      scenario: "Create a package structure for a simple calculator application.",
      keyConcepts: "Packages, __init__.py, package imports.",
      solution: `# Package structure:
# calculator/
#   __init__.py
#   basic_ops.py
#   advanced_ops.py

# calculator/__init__.py
from .basic_ops import add, subtract, multiply, divide
from .advanced_ops import power, sqrt, factorial

# calculator/basic_ops.py
def add(a, b):
    return a + b

def subtract(a, b):
    return a - b

def multiply(a, b):
    return a * b

def divide(a, b):
    if b != 0:
        return a / b
    return "Cannot divide by zero"

# calculator/advanced_ops.py
import math

def power(base, exponent):
    return base ** exponent

def sqrt(number):
    return math.sqrt(number)

def factorial(n):
    return math.factorial(n)

# Using the package
import calculator

print(calculator.add(10, 5))
print(calculator.power(2, 3))`,
    },
  ],
  "text-files": [
    {
      title: "Reading from Files",
      objective: "Learn different methods to read data from text files.",
      scenario: "Read and process data from a text file containing student names and grades.",
      keyConcepts: "File opening, reading methods, file closing.",
      solution: `# Reading entire file
with open('students.txt', 'r') as file:
    content = file.read()
    print("Entire file content:")
    print(content)

# Reading line by line
with open('students.txt', 'r') as file:
    print("\\nReading line by line:")
    for line_number, line in enumerate(file, 1):
        print(f"Line {line_number}: {line.strip()}")

# Reading all lines into a list
with open('students.txt', 'r') as file:
    lines = file.readlines()
    print(f"\\nTotal lines: {len(lines)}")
    for line in lines:
        print(line.strip())`,
    },
    {
      title: "Writing to Files",
      objective: "Learn to write data to text files.",
      scenario: "Create a program that writes user input to a journal file.",
      keyConcepts: "File writing, append mode, text formatting.",
      solution: `import datetime

# Writing to a new file
journal_entry = input("Write your journal entry: ")
current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# Write to file
with open('journal.txt', 'a') as file:
    file.write(f"{current_time}\\n")
    file.write(f"{journal_entry}\\n")
    file.write("-" * 50 + "\\n")

print("Journal entry saved!")

# Reading back the journal
print("\\nYour journal entries:")
try:
    with open('journal.txt', 'r') as file:
        print(file.read())
except FileNotFoundError:
    print("No journal entries found.")`,
    },
  ],
  "dictionaries": [
    {
      title: "Dictionary Basics",
      objective: "Learn to create and manipulate dictionaries.",
      scenario: "Create a student database using dictionaries to store information.",
      keyConcepts: "Dictionary creation, key-value pairs, accessing values.",
      solution: `# Creating dictionaries
student1 = {
    "name": "Alice",
    "age": 20,
    "grade": "A",
    "subjects": ["Math", "Physics", "Chemistry"]
}

student2 = {
    "name": "Bob",
    "age": 21,
    "grade": "B",
    "subjects": ["History", "English", "Art"]
}

# Accessing dictionary values
print(f"Student: {student1['name']}")
print(f"Age: {student1['age']}")
print(f"Subjects: {', '.join(student1['subjects'])}")

# Modifying dictionaries
student1["age"] = 21
student1["gpa"] = 3.8

# Adding new key-value pairs
student1["email"] = "<EMAIL>"

print(f"Updated info: {student1}")`,
    },
    {
      title: "Dictionary Methods",
      objective: "Master common dictionary methods.",
      scenario: "Use various dictionary methods to process a word frequency counter.",
      keyConcepts: "keys(), values(), items(), get(), update().",
      solution: `# Word frequency counter
text = "python is great python is powerful python is fun"
words = text.split()

# Count word frequencies
word_count = {}
for word in words:
    word_count[word] = word_count.get(word, 0) + 1

print("Word frequencies:")
for word, count in word_count.items():
    print(f"{word}: {count}")

# Dictionary methods
print(f"\\nAll words: {list(word_count.keys())}")
print(f"All counts: {list(word_count.values())}")
print(f"Most frequent word: {max(word_count, key=word_count.get)}")

# Updating dictionary
new_words = {"amazing": 2, "language": 1}
word_count.update(new_words)
print(f"\\nUpdated dictionary: {word_count}")`,
    },
  ],
  "oop": [
    {
      title: "Encapsulation",
      objective: "Learn about encapsulation and private attributes.",
      scenario: "Create a BankAccount class with private balance and public methods.",
      keyConcepts: "Private attributes, getter/setter methods, data hiding.",
      solution: `class BankAccount:
    def __init__(self, account_number, initial_balance=0):
        self.account_number = account_number
        self.__balance = initial_balance  # Private attribute
    
    def deposit(self, amount):
        if amount > 0:
            self.__balance += amount
            print(f"Deposited \\${amount}. New balance: \\${self.__balance}")
        else:
            print("Deposit amount must be positive")
    
    def withdraw(self, amount):
        if 0 < amount <= self.__balance:
            self.__balance -= amount
            print(f"Withdrew \\${amount}. New balance: \\${self.__balance}")
        else:
            print("Invalid withdrawal amount")
    
    def get_balance(self):
        return self.__balance
    
    def account_info(self):
        return f"Account {self.account_number}: \\${self.__balance}"

# Using encapsulation
account = BankAccount("12345", 1000)
print(account.account_info())
account.deposit(500)
account.withdraw(200)
print(f"Current balance: \\${account.get_balance()}")`,
    },
    {
      title: "Polymorphism",
      objective: "Understand polymorphism through method overriding.",
      scenario: "Create different shapes that implement a common interface.",
      keyConcepts: "Method overriding, polymorphism, abstract methods.",
      solution: `import math

class Shape:
    def area(self):
        pass
    
    def perimeter(self):
        pass

class Rectangle(Shape):
    def __init__(self, width, height):
        self.width = width
        self.height = height
    
    def area(self):
        return self.width * self.height
    
    def perimeter(self):
        return 2 * (self.width + self.height)

class Circle(Shape):
    def __init__(self, radius):
        self.radius = radius
    
    def area(self):
        return math.pi * self.radius ** 2
    
    def perimeter(self):
        return 2 * math.pi * self.radius

# Polymorphism in action
shapes = [
    Rectangle(5, 3),
    Circle(4),
    Rectangle(2, 8)
]

for i, shape in enumerate(shapes):
    print(f"Shape {i+1}:")
    print(f"  Area: {shape.area():.2f}")
    print(f"  Perimeter: {shape.perimeter():.2f}")`,
    },
  ],
  "recursion": [
    {
      title: "Basic Recursion",
      objective: "Understand the fundamentals of recursive functions.",
      scenario: "Implement factorial calculation using recursion.",
      keyConcepts: "Base case, recursive case, stack overflow prevention.",
      solution: `def factorial(n):
    # Base case
    if n <= 1:
        return 1
    # Recursive case
    else:
        return n * factorial(n - 1)

def fibonacci(n):
    # Base cases
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    # Recursive case
    else:
        return fibonacci(n - 1) + fibonacci(n - 2)

# Testing recursive functions
print("Factorial examples:")
for i in range(6):
    print(f"{i}! = {factorial(i)}")

print("\\nFibonacci sequence:")
for i in range(10):
    print(f"F({i}) = {fibonacci(i)}")`,
    },
    {
      title: "Recursion with Data Structures",
      objective: "Apply recursion to work with lists and trees.",
      scenario: "Use recursion to process nested lists and calculate sums.",
      keyConcepts: "Recursive data processing, tree traversal, nested structures.",
      solution: `def sum_nested_list(nested_list):
    total = 0
    for item in nested_list:
        if isinstance(item, list):
            # Recursive case: item is a list
            total += sum_nested_list(item)
        else:
            # Base case: item is a number
            total += item
    return total

def flatten_list(nested_list):
    result = []
    for item in nested_list:
        if isinstance(item, list):
            # Recursive case: extend with flattened sublist
            result.extend(flatten_list(item))
        else:
            # Base case: append the item
            result.append(item)
    return result

# Testing recursive list processing
nested = [1, [2, 3], [4, [5, 6]], 7]
print(f"Nested list: {nested}")
print(f"Sum: {sum_nested_list(nested)}")
print(f"Flattened: {flatten_list(nested)}")

# Binary tree example
class TreeNode:
    def __init__(self, value):
        self.value = value
        self.left = None
        self.right = None

def tree_sum(root):
    if root is None:
        return 0
    return root.value + tree_sum(root.left) + tree_sum(root.right)

# Creating a simple tree
root = TreeNode(1)
root.left = TreeNode(2)
root.right = TreeNode(3)
root.left.left = TreeNode(4)
root.left.right = TreeNode(5)

print(f"\\nTree sum: {tree_sum(root)}")`,
    },
  ],
};

export const topicNames = {
  "data-expression": "Data and Expression",
  "control-structure": "Control Structure", 
  "list": "Lists",
  "functions": "Functions",
  "objects": "Objects",
  "modular-design": "Modular Design",
  "text-files": "Text Files",
  "dictionaries": "Dictionaries",
  "oop": "Object-Oriented Programming",
  "recursion": "Recursion"
};

export const statsData = [
  {
    icon: "📖",
    number: "50+",
    label: "Case Studies",
    color: "from-orange-500 to-red-500",
  },
  {
    icon: "🎯",
    number: "10",
    label: "Core Topics",
    color: "from-blue-500 to-cyan-500",
  },
  {
    icon: "⭐",
    number: "100%",
    label: "Practical Focus",
    color: "from-green-500 to-teal-500",
  },
];
