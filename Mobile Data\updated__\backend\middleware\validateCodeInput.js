export default function validateCodeInput(req, res, next) {
  const { code, language, input } = req.body;

  const allowedLanguages = ["cpp", "java", "javascript", "python"];

  if (!code || typeof code !== "string") {
    return res.status(400).json({
      success: false,
      error: "Code is required and must be a string.",
    });
  }

  if (!language || !allowedLanguages.includes(language)) {
    return res.status(400).json({
      success: false,
      error: `Invalid or missing language. Allowed: ${allowedLanguages.join(", ")}`,
    });
  }

  if (input && typeof input !== "string") {
    return res.status(400).json({
      success: false,
      error: "Input must be a string if provided.",
    });
  }

  next();
}
