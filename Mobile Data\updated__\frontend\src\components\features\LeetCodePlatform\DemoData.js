// Demo data for the LeetCode platform
export const demoProblems = [
  {
    id: 1,
    title: "Two Sum",
    difficulty: "Easy",
    description: "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target. You may assume that each input would have exactly one solution, and you may not use the same element twice. You can return the answer in any order.",
    examples: [
      {
        input: "nums = [2,7,11,15], target = 9",
        output: "[0,1]",
        explanation: "Because nums[0] + nums[1] = 2 + 7 = 9, we return [0, 1]."
      },
      {
        input: "nums = [3,2,4], target = 6", 
        output: "[1,2]",
        explanation: "Because nums[1] + nums[2] = 2 + 4 = 6, we return [1, 2]."
      },
      {
        input: "nums = [3,3], target = 6",
        output: "[0,1]",
        explanation: "Because nums[0] + nums[1] = 3 + 3 = 6, we return [0, 1]."
      }
    ],
    constraints: [
      "2 ≤ nums.length ≤ 10⁴",
      "-10⁹ ≤ nums[i] ≤ 10⁹", 
      "-10⁹ ≤ target ≤ 10⁹",
      "Only one valid answer exists."
    ],
    starterCode: {
      javascript: `/**
 * @param {number[]} nums
 * @param {number} target
 * @return {number[]}
 */
var twoSum = function(nums, target) {
    // Write your solution here
    
};`,
      python: `def two_sum(nums, target):
    """
    :type nums: List[int]
    :type target: int
    :rtype: List[int]
    """
    # Write your solution here
    pass`,
      java: `class Solution {
    public int[] twoSum(int[] nums, int target) {
        // Write your solution here
        
    }
}`,
      cpp: `class Solution {
public:
    vector<int> twoSum(vector<int>& nums, int target) {
        // Write your solution here
        
    }
};`
    },
    tags: ["Array", "Hash Table"],
    companies: ["Amazon", "Google", "Microsoft", "Facebook"],
    acceptanceRate: 49.2,
    totalSubmissions: 8234567,
    totalAccepted: 4051234
  },
  {
    id: 2,
    title: "Add Two Numbers",
    difficulty: "Medium",
    description: "You are given two non-empty linked lists representing two non-negative integers. The digits are stored in reverse order, and each of their nodes contains a single digit. Add the two numbers and return the sum as a linked list.",
    examples: [
      {
        input: "l1 = [2,4,3], l2 = [5,6,4]",
        output: "[7,0,8]",
        explanation: "342 + 465 = 807."
      },
      {
        input: "l1 = [0], l2 = [0]",
        output: "[0]",
        explanation: "0 + 0 = 0."
      }
    ],
    constraints: [
      "The number of nodes in each linked list is in the range [1, 100].",
      "0 ≤ Node.val ≤ 9",
      "It is guaranteed that the list represents a number that does not have leading zeros."
    ],
    starterCode: {
      javascript: `/**
 * Definition for singly-linked list.
 * function ListNode(val, next) {
 *     this.val = (val===undefined ? 0 : val)
 *     this.next = (next===undefined ? null : next)
 * }
 */
/**
 * @param {ListNode} l1
 * @param {ListNode} l2
 * @return {ListNode}
 */
var addTwoNumbers = function(l1, l2) {
    // Write your solution here
    
};`,
      python: `# Definition for singly-linked list.
# class ListNode:
#     def __init__(self, val=0, next=None):
#         self.val = val
#         self.next = next
class Solution:
    def addTwoNumbers(self, l1: Optional[ListNode], l2: Optional[ListNode]) -> Optional[ListNode]:
        # Write your solution here
        pass`,
      java: `/**
 * Definition for singly-linked list.
 * public class ListNode {
 *     int val;
 *     ListNode next;
 *     ListNode() {}
 *     ListNode(int val) { this.val = val; }
 *     ListNode(int val, ListNode next) { this.val = val; this.next = next; }
 * }
 */
class Solution {
    public ListNode addTwoNumbers(ListNode l1, ListNode l2) {
        // Write your solution here
        
    }
}`
    },
    tags: ["Linked List", "Math", "Recursion"],
    companies: ["Amazon", "Microsoft", "Apple"],
    acceptanceRate: 38.7,
    totalSubmissions: 5234567,
    totalAccepted: 2025678
  }
];

export const demoSubmissions = [
  {
    id: 1,
    problemId: 1,
    status: 'Accepted',
    language: 'JavaScript',
    runtime: '64 ms',
    memory: '15.3 MB',
    timestamp: '2024-01-15 14:30:25',
    code: `var twoSum = function(nums, target) {
    const map = new Map();
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
    return [];
};`,
    testCases: '57/57',
    beats: '85.2%'
  },
  {
    id: 2,
    problemId: 1,
    status: 'Wrong Answer',
    language: 'Python',
    runtime: 'N/A',
    memory: 'N/A',
    timestamp: '2024-01-15 14:25:10',
    code: `def twoSum(nums, target):
    for i in range(len(nums)):
        for j in range(i + 1, len(nums)):
            if nums[i] + nums[j] == target:
                return [i, j]
    return []`,
    testCases: '54/57',
    error: 'Time Limit Exceeded on test case 55'
  }
];

export const demoDiscussions = [
  {
    id: 1,
    problemId: 1,
    author: {
      name: 'CodeMaster2024',
      avatar: '👨‍💻',
      reputation: 15420,
      badge: 'Expert'
    },
    content: 'Great problem! Here\'s my O(n) solution using HashMap. The key insight is to store the complement while iterating through the array.',
    code: `var twoSum = function(nums, target) {
    const map = new Map();
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
};`,
    timestamp: '2 hours ago',
    upvotes: 45,
    downvotes: 2,
    replies: [
      {
        id: 11,
        author: {
          name: 'NewbieCoder',
          avatar: '🧑‍🎓',
          reputation: 250,
          badge: 'Beginner'
        },
        content: 'Thanks for the explanation! Can you explain why we check for the complement first before adding to the map?',
        timestamp: '1 hour ago',
        upvotes: 8,
        downvotes: 0
      }
    ],
    isPinned: true,
    tags: ['HashMap', 'Optimal', 'JavaScript']
  }
];

export const demoEditorial = {
  overview: "This problem asks us to find two numbers in an array that add up to a specific target. We need to return the indices of these two numbers.",
  approaches: [
    {
      id: 'approach1',
      title: 'Approach 1: Brute Force',
      timeComplexity: 'O(n²)',
      spaceComplexity: 'O(1)',
      difficulty: 'Easy',
      intuition: 'The brute force approach is straightforward. We check every pair of numbers to see if they add up to the target.',
      algorithm: [
        'Loop through each element x and find if there is another value that equals to target - x',
        'Use nested loops to check all possible pairs',
        'Return the indices when a valid pair is found'
      ],
      implementation: {
        javascript: `var twoSum = function(nums, target) {
    for (let i = 0; i < nums.length; i++) {
        for (let j = i + 1; j < nums.length; j++) {
            if (nums[i] + nums[j] === target) {
                return [i, j];
            }
        }
    }
    return [];
};`,
        python: `def twoSum(nums, target):
    for i in range(len(nums)):
        for j in range(i + 1, len(nums)):
            if nums[i] + nums[j] == target:
                return [i, j]
    return []`
      },
      pros: ['Simple to understand and implement', 'No extra space needed'],
      cons: ['Time complexity is O(n²)', 'Not efficient for large arrays']
    },
    {
      id: 'approach2',
      title: 'Approach 2: Hash Map (One-pass)',
      timeComplexity: 'O(n)',
      spaceComplexity: 'O(n)',
      difficulty: 'Medium',
      intuition: 'We can solve this in one pass by checking if the complement exists while building the hash map.',
      algorithm: [
        'Iterate through the array once',
        'For each element, calculate its complement (target - current element)',
        'Check if the complement exists in the hash map',
        'If found, return the indices',
        'If not found, add the current element and its index to the hash map'
      ],
      implementation: {
        javascript: `var twoSum = function(nums, target) {
    const map = new Map();
    
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        
        map.set(nums[i], i);
    }
    
    return [];
};`,
        python: `def twoSum(nums, target):
    num_map = {}
    
    for i, num in enumerate(nums):
        complement = target - num
        
        if complement in num_map:
            return [num_map[complement], i]
        
        num_map[num] = i
    
    return []`
      },
      pros: ['Optimal time complexity O(n)', 'Single pass through array', 'Most efficient solution'],
      cons: ['Extra space for hash map', 'Slightly more complex logic']
    }
  ],
  keyInsights: [
    'Hash maps provide O(1) average lookup time',
    'Trading space for time complexity is often worthwhile',
    'One-pass solutions are generally more efficient than multi-pass',
    'Always consider the complement when looking for pairs'
  ],
  followUp: 'Can you solve this problem if the input array is sorted? (Hint: Two pointers technique)'
};

export const testCases = [
  {
    input: { nums: [2, 7, 11, 15], target: 9 },
    expectedOutput: [0, 1],
    explanation: "nums[0] + nums[1] = 2 + 7 = 9"
  },
  {
    input: { nums: [3, 2, 4], target: 6 },
    expectedOutput: [1, 2],
    explanation: "nums[1] + nums[2] = 2 + 4 = 6"
  },
  {
    input: { nums: [3, 3], target: 6 },
    expectedOutput: [0, 1],
    explanation: "nums[0] + nums[1] = 3 + 3 = 6"
  }
];
