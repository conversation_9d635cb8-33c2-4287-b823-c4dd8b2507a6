const reinforcementLearningCaseStudies = [
  {
    title: "Q-Learning Implementation",
    objective: "Learn basic Q-learning algorithm",
    scenario: "Train an agent to navigate a grid world",
    keyConcepts: ["State-Action Values", "Exploration vs Exploitation", "Reward Function", "Policy"],
    solution: `# Q-Learning Example
import numpy as np

class QLearning:
    def __init__(self, states, actions, alpha=0.1, gamma=0.9):
        self.q_table = np.zeros((states, actions))
        self.alpha = alpha  # learning rate
        self.gamma = gamma  # discount factor
    
    def update(self, state, action, reward, next_state):
        old_value = self.q_table[state, action]
        next_max = np.max(self.q_table[next_state])
        
        # Q-learning formula
        new_value = (1 - self.alpha) * old_value + \
                   self.alpha * (reward + self.gamma * next_max)
        
        self.q_table[state, action] = new_value
    
    def get_action(self, state, epsilon=0.1):
        if np.random.random() < epsilon:
            return np.random.randint(self.q_table.shape[1])
        return np.argmax(self.q_table[state])
`
  },
  {
    title: "Deep Q-Network (DQN)",
    objective: "Implement DQN algorithm",
    scenario: "Train an agent to play a simple game",
    keyConcepts: ["Neural Networks", "Experience Replay", "Target Network", "Epsilon-Greedy Policy"],
    solution: `# DQN Example
import torch
import torch.nn as nn

class DQN(nn.Module):
    def __init__(self, input_size, output_size):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(input_size, 64),
            nn.ReLU(),
            nn.Linear(64, 64),
            nn.ReLU(),
            nn.Linear(64, output_size)
        )
    
    def forward(self, x):
        return self.network(x)

# Create DQN agent
input_size = 4  # state space size
output_size = 2  # action space size
model = DQN(input_size, output_size)
print(model)
`
  }
];

export default reinforcementLearningCaseStudies;
