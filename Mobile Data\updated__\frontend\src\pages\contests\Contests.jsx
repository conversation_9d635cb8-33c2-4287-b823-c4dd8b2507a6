import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const Contests = () => {
  const [activeTab, setActiveTab] = useState('upcoming');
  const [selectedContest, setSelectedContest] = useState(null);

  const contests = {
    upcoming: [
      {
        id: 1,
        title: "Weekly Coding Challenge #45",
        date: "2024-01-20",
        time: "14:00 UTC",
        duration: "2 hours",
        participants: 1250,
        difficulty: "Medium",
        prize: "$500",
        status: "upcoming",
        description: "Solve algorithmic problems focusing on dynamic programming and graph theory.",
        problems: 4,
        tags: ["Algorithms", "DP", "Graphs"]
      },
      {
        id: 2,
        title: "System Design Sprint",
        date: "2024-01-22",
        time: "16:00 UTC",
        duration: "3 hours",
        participants: 890,
        difficulty: "Hard",
        prize: "$1000",
        status: "upcoming",
        description: "Design scalable systems for real-world scenarios.",
        problems: 2,
        tags: ["System Design", "Architecture", "Scalability"]
      }
    ],
    live: [
      {
        id: 3,
        title: "AI/ML Challenge 2024",
        date: "2024-01-15",
        time: "12:00 UTC",
        duration: "4 hours",
        participants: 2100,
        difficulty: "Hard",
        prize: "$2000",
        status: "live",
        description: "Build and optimize machine learning models for computer vision tasks.",
        problems: 3,
        tags: ["Machine Learning", "Computer Vision", "Python"],
        timeLeft: "2h 45m"
      }
    ],
    past: [
      {
        id: 4,
        title: "Data Structures Mastery",
        date: "2024-01-10",
        time: "15:00 UTC",
        duration: "2.5 hours",
        participants: 1800,
        difficulty: "Medium",
        prize: "$750",
        status: "completed",
        description: "Master advanced data structures and their applications.",
        problems: 5,
        tags: ["Data Structures", "Trees", "Heaps"],
        winner: "CodeMaster2024"
      }
    ]
  };

  const getDifficultyColor = (difficulty) => {
    switch(difficulty) {
      case 'Easy': return 'bg-green-900 text-green-300';
      case 'Medium': return 'bg-yellow-900 text-yellow-300';
      case 'Hard': return 'bg-red-900 text-red-300';
      default: return 'bg-blue-900 text-blue-300';
    }
  };

  const getStatusColor = (status) => {
    switch(status) {
      case 'upcoming': return 'bg-blue-900 text-blue-300';
      case 'live': return 'bg-green-900 text-green-300';
      case 'completed': return 'bg-gray-700 text-gray-300';
      default: return 'bg-gray-700 text-gray-300';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white py-8">
        <div className="container mx-auto px-6">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h1 className="text-3xl md:text-4xl font-bold mb-2">Programming Contests</h1>
            <p className="text-lg text-blue-100">Compete, Learn, and Win Amazing Prizes</p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-6 py-6">
        {/* Tabs */}
        <div className="mb-6">
          <div className="flex gap-2 p-2 bg-gray-800 rounded-lg border border-gray-700">
            {['upcoming', 'live', 'past'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 capitalize ${
                  activeTab === tab
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                    : 'text-gray-300 hover:bg-gray-700'
                }`}
              >
                {tab} ({contests[tab].length})
              </button>
            ))}
          </div>
        </div>

        {/* Contest Cards */}
        <AnimatePresence mode="wait">
          <motion.div 
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
          >
            {contests[activeTab].map((contest) => (
              <motion.div
                key={contest.id}
                layout
                className="bg-gray-800 border border-gray-700 rounded-lg p-4 hover:border-gray-600 transition-all duration-300 cursor-pointer"
                onClick={() => setSelectedContest(contest)}
              >
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-lg font-bold text-white">{contest.title}</h3>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(contest.status)}`}>
                    {contest.status}
                  </span>
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-gray-300 text-sm">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {contest.date} at {contest.time}
                  </div>
                  <div className="flex items-center text-gray-300 text-sm">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Duration: {contest.duration}
                  </div>
                  <div className="flex items-center text-gray-300 text-sm">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    {contest.participants} participants
                  </div>
                </div>

                <div className="flex justify-between items-center mb-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(contest.difficulty)}`}>
                    {contest.difficulty}
                  </span>
                  <span className="text-green-400 font-bold">{contest.prize}</span>
                </div>

                <div className="flex flex-wrap gap-1 mb-3">
                  {contest.tags.map((tag, idx) => (
                    <span key={idx} className="px-2 py-1 bg-blue-900 text-blue-300 rounded-full text-xs">
                      {tag}
                    </span>
                  ))}
                </div>

                {contest.status === 'live' && contest.timeLeft && (
                  <div className="bg-green-900 text-green-300 px-3 py-2 rounded-lg text-center font-medium">
                    Time Left: {contest.timeLeft}
                  </div>
                )}

                {contest.status === 'upcoming' && (
                  <button className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-2 rounded-lg font-medium hover:shadow-md transition-all">
                    Register Now
                  </button>
                )}

                {contest.status === 'completed' && contest.winner && (
                  <div className="bg-yellow-900 text-yellow-300 px-3 py-2 rounded-lg text-center">
                    Winner: {contest.winner}
                  </div>
                )}
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Contest Details Modal */}
        <AnimatePresence>
          {selectedContest && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              onClick={() => setSelectedContest(null)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-gray-800 rounded-xl p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto border border-gray-700"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex justify-between items-start mb-4">
                  <h2 className="text-2xl font-bold text-white">{selectedContest.title}</h2>
                  <button
                    onClick={() => setSelectedContest(null)}
                    className="text-gray-400 hover:text-white"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <p className="text-gray-300 mb-4">{selectedContest.description}</p>

                <div className="grid md:grid-cols-2 gap-4 mb-6">
                  <div className="space-y-2">
                    <div className="text-gray-300">
                      <span className="font-medium">Date:</span> {selectedContest.date}
                    </div>
                    <div className="text-gray-300">
                      <span className="font-medium">Time:</span> {selectedContest.time}
                    </div>
                    <div className="text-gray-300">
                      <span className="font-medium">Duration:</span> {selectedContest.duration}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-gray-300">
                      <span className="font-medium">Problems:</span> {selectedContest.problems}
                    </div>
                    <div className="text-gray-300">
                      <span className="font-medium">Prize:</span> <span className="text-green-400">{selectedContest.prize}</span>
                    </div>
                    <div className="text-gray-300">
                      <span className="font-medium">Participants:</span> {selectedContest.participants}
                    </div>
                  </div>
                </div>

                <div className="flex gap-3">
                  {selectedContest.status === 'upcoming' && (
                    <button className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 rounded-lg font-medium hover:shadow-md transition-all">
                      Register for Contest
                    </button>
                  )}
                  {selectedContest.status === 'live' && (
                    <button className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-lg font-medium hover:shadow-md transition-all">
                      Join Contest
                    </button>
                  )}
                  {selectedContest.status === 'completed' && (
                    <button className="flex-1 bg-gradient-to-r from-gray-600 to-gray-700 text-white py-3 rounded-lg font-medium hover:shadow-md transition-all">
                      View Results
                    </button>
                  )}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Contests;