import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronDown } from "react-icons/fa";

const SQLFAQS = ({ onBackToCourse }) => {
  const [openFaq, setOpenFaq] = useState(null);
  
  const faqs = [
    {
      question: "What is the difference between INNER JOIN and LEFT JOIN?",
      answer: "An INNER JOIN returns only the rows where there is a match in both tables based on the join condition. A LEFT JOIN returns all rows from the left table and matching rows from the right table. If there's no match in the right table, NULL values are returned for those columns."
    },
    {
      question: "How do I optimize SQL queries for better performance?",
      answer: "To optimize SQL queries: 1) Use appropriate indexes, 2) Avoid SELECT * and only retrieve needed columns, 3) Use WHERE clauses to filter data early, 4) Avoid using functions in WHERE clauses, 5) Use EXPLAIN to analyze query execution plans, 6) Consider query caching, and 7) Optimize JOINs by joining on indexed columns."
    },
    {
      question: "What are window functions in SQL?",
      answer: "Window functions perform calculations across a set of table rows related to the current row. They allow you to compute running totals, moving averages, rankings, and more without collapsing the result set like GROUP BY does. Examples include ROW_NUMBER(), RANK(), DENSE_RANK(), LEAD(), LAG(), and aggregate functions with OVER clauses."
    },
    {
      question: "What is database normalization?",
      answer: "Database normalization is the process of organizing data to reduce redundancy and improve data integrity. It involves dividing large tables into smaller, related tables and defining relationships between them. The main normal forms are 1NF (atomic values), 2NF (no partial dependencies), 3NF (no transitive dependencies), BCNF, 4NF, and 5NF."
    },
    {
      question: "How do I handle NULL values in SQL?",
      answer: "NULL values in SQL represent missing or unknown data. To handle them: 1) Use IS NULL or IS NOT NULL instead of = NULL, 2) Use COALESCE() to provide default values, 3) Use NULLIF() to convert specific values to NULL, 4) Be aware that NULL in calculations typically results in NULL, and 5) Consider using NOT NULL constraints when designing tables."
    },
    {
      question: "What's the difference between DELETE, TRUNCATE, and DROP?",
      answer: "DELETE removes specific rows based on a condition, logs the operation, and can be rolled back. TRUNCATE removes all rows from a table without logging individual row deletions, is faster than DELETE, and can't be easily rolled back. DROP removes the entire table structure including all data, indexes, and constraints from the database."
    }
  ];

  const toggleFaq = (index) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-900/70 to-indigo-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">Frequently Asked Questions</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>
      
      {/* Content */}
      <div className="p-6 text-white">
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Common SQL Questions</h3>
          <p className="text-gray-300">
            Find answers to frequently asked questions about SQL, database design, and query optimization.
          </p>
        </div>
        
        {/* FAQs */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
              className="border border-gray-700/30 rounded-lg overflow-hidden"
            >
              <button
                onClick={() => toggleFaq(index)}
                className="w-full flex items-center justify-between p-4 text-left bg-gray-800/30 hover:bg-gray-700/40 transition-colors"
              >
                <h4 className="font-medium text-white">{faq.question}</h4>
                <motion.div
                  animate={{ rotate: openFaq === index ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <FaChevronDown className="text-gray-300" />
                </motion.div>
              </button>
              
              <AnimatePresence>
                {openFaq === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: "auto", opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="p-4 bg-gray-800/20">
                      <p className="text-gray-300">{faq.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SQLFAQS;