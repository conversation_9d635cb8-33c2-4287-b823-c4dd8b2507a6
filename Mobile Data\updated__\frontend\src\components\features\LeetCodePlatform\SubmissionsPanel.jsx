import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye,
  Filter,
  Calendar,
  Code,
  Zap,
  Database
} from 'lucide-react';

const SubmissionsPanel = ({ isDarkMode }) => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSubmission, setSelectedSubmission] = useState(null);

  // Sample submissions data
  const submissions = [
    {
      id: 1,
      status: 'Accepted',
      language: 'JavaScript',
      runtime: '64 ms',
      memory: '15.3 MB',
      timestamp: '2024-01-15 14:30:25',
      code: `var twoSum = function(nums, target) {
    const map = new Map();
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
    return [];
};`,
      testCases: '57/57',
      beats: '85.2%'
    },
    {
      id: 2,
      status: 'Wrong Answer',
      language: 'Python',
      runtime: 'N/A',
      memory: 'N/A',
      timestamp: '2024-01-15 14:25:10',
      code: `def twoSum(nums, target):
    for i in range(len(nums)):
        for j in range(i + 1, len(nums)):
            if nums[i] + nums[j] == target:
                return [i, j]
    return []`,
      testCases: '54/57',
      error: 'Time Limit Exceeded on test case 55'
    },
    {
      id: 3,
      status: 'Runtime Error',
      language: 'Java',
      runtime: 'N/A',
      memory: 'N/A',
      timestamp: '2024-01-15 14:20:45',
      code: `class Solution {
    public int[] twoSum(int[] nums, int target) {
        for (int i = 0; i < nums.length; i++) {
            for (int j = i + 1; j < nums.length; j++) {
                if (nums[i] + nums[j] == target) {
                    return new int[]{i, j};
                }
            }
        }
        return new int[0];
    }
}`,
      testCases: '12/57',
      error: 'ArrayIndexOutOfBoundsException on line 6'
    },
    {
      id: 4,
      status: 'Accepted',
      language: 'C++',
      runtime: '8 ms',
      memory: '10.1 MB',
      timestamp: '2024-01-15 13:45:20',
      code: `class Solution {
public:
    vector<int> twoSum(vector<int>& nums, int target) {
        unordered_map<int, int> map;
        for (int i = 0; i < nums.size(); i++) {
            int complement = target - nums[i];
            if (map.find(complement) != map.end()) {
                return {map[complement], i};
            }
            map[nums[i]] = i;
        }
        return {};
    }
};`,
      testCases: '57/57',
      beats: '95.8%'
    }
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Accepted':
        return <CheckCircle className="text-green-500" size={16} />;
      case 'Wrong Answer':
        return <XCircle className="text-red-500" size={16} />;
      case 'Runtime Error':
        return <XCircle className="text-orange-500" size={16} />;
      case 'Time Limit Exceeded':
        return <Clock className="text-yellow-500" size={16} />;
      default:
        return <XCircle className="text-gray-500" size={16} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Accepted':
        return 'text-green-500';
      case 'Wrong Answer':
        return 'text-red-500';
      case 'Runtime Error':
        return 'text-orange-500';
      case 'Time Limit Exceeded':
        return 'text-yellow-500';
      default:
        return 'text-gray-500';
    }
  };

  const filteredSubmissions = submissions.filter(submission => {
    if (selectedFilter === 'all') return true;
    if (selectedFilter === 'accepted') return submission.status === 'Accepted';
    if (selectedFilter === 'wrong') return submission.status !== 'Accepted';
    return true;
  });

  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="p-6 h-full overflow-auto"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold">My Submissions</h2>
        
        {/* Filter Buttons */}
        <div className="flex items-center gap-2">
          <Filter size={16} className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
          <div className="flex rounded-lg overflow-hidden border">
            {[
              { key: 'all', label: 'All' },
              { key: 'accepted', label: 'Accepted' },
              { key: 'wrong', label: 'Wrong' }
            ].map((filter) => (
              <button
                key={filter.key}
                onClick={() => setSelectedFilter(filter.key)}
                className={`px-3 py-1 text-sm font-medium transition-colors ${
                  selectedFilter === filter.key
                    ? 'bg-blue-500 text-white'
                    : isDarkMode
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Submissions Table */}
      <div className={`rounded-lg border overflow-hidden ${
        isDarkMode ? 'border-gray-700' : 'border-gray-200'
      }`}>
        <div className={`grid grid-cols-6 gap-4 p-4 text-sm font-medium border-b ${
          isDarkMode 
            ? 'bg-gray-800 border-gray-700 text-gray-300' 
            : 'bg-gray-50 border-gray-200 text-gray-700'
        }`}>
          <div>Status</div>
          <div>Language</div>
          <div>Runtime</div>
          <div>Memory</div>
          <div>Submitted</div>
          <div>Actions</div>
        </div>

        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredSubmissions.map((submission) => (
            <div
              key={submission.id}
              className={`grid grid-cols-6 gap-4 p-4 text-sm hover:bg-opacity-50 transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-800' 
                  : 'hover:bg-gray-50'
              }`}
            >
              {/* Status */}
              <div className="flex items-center gap-2">
                {getStatusIcon(submission.status)}
                <span className={getStatusColor(submission.status)}>
                  {submission.status}
                </span>
              </div>

              {/* Language */}
              <div className="flex items-center gap-2">
                <Code size={14} className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
                <span>{submission.language}</span>
              </div>

              {/* Runtime */}
              <div className="flex items-center gap-2">
                <Zap size={14} className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
                <span>{submission.runtime}</span>
                {submission.beats && (
                  <span className="text-xs text-green-500">({submission.beats})</span>
                )}
              </div>

              {/* Memory */}
              <div className="flex items-center gap-2">
                <Database size={14} className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
                <span>{submission.memory}</span>
              </div>

              {/* Timestamp */}
              <div className="flex items-center gap-2">
                <Calendar size={14} className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
                <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {formatDate(submission.timestamp)}
                </span>
              </div>

              {/* Actions */}
              <div>
                <button
                  onClick={() => setSelectedSubmission(submission)}
                  className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-medium transition-colors ${
                    isDarkMode
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <Eye size={12} />
                  View
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Submission Details Modal */}
      {selectedSubmission && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedSubmission(null)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className={`max-w-4xl w-full max-h-[80vh] rounded-lg overflow-hidden ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className={`p-4 border-b ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(selectedSubmission.status)}
                  <h3 className="text-lg font-semibold">
                    Submission Details - {selectedSubmission.status}
                  </h3>
                </div>
                <button
                  onClick={() => setSelectedSubmission(null)}
                  className={`p-2 rounded-lg transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  ×
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-4 overflow-auto max-h-[60vh]">
              {/* Submission Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className={`p-3 rounded-lg ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
                }`}>
                  <div className="text-sm text-gray-500">Language</div>
                  <div className="font-semibold">{selectedSubmission.language}</div>
                </div>
                <div className={`p-3 rounded-lg ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
                }`}>
                  <div className="text-sm text-gray-500">Runtime</div>
                  <div className="font-semibold">{selectedSubmission.runtime}</div>
                </div>
                <div className={`p-3 rounded-lg ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
                }`}>
                  <div className="text-sm text-gray-500">Memory</div>
                  <div className="font-semibold">{selectedSubmission.memory}</div>
                </div>
                <div className={`p-3 rounded-lg ${
                  isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
                }`}>
                  <div className="text-sm text-gray-500">Test Cases</div>
                  <div className="font-semibold">{selectedSubmission.testCases}</div>
                </div>
              </div>

              {/* Error Message */}
              {selectedSubmission.error && (
                <div className="mb-6 p-4 rounded-lg bg-red-500/10 border border-red-500/20">
                  <div className="text-red-500 font-semibold mb-2">Error:</div>
                  <div className="text-sm">{selectedSubmission.error}</div>
                </div>
              )}

              {/* Code */}
              <div>
                <h4 className="font-semibold mb-3">Submitted Code:</h4>
                <div className={`rounded-lg border overflow-hidden ${
                  isDarkMode ? 'border-gray-700' : 'border-gray-200'
                }`}>
                  <pre className={`p-4 text-sm font-mono overflow-auto ${
                    isDarkMode ? 'bg-gray-900 text-gray-300' : 'bg-gray-50 text-gray-800'
                  }`}>
                    {selectedSubmission.code}
                  </pre>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Empty State */}
      {filteredSubmissions.length === 0 && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📝</div>
          <h3 className="text-lg font-semibold mb-2">No submissions found</h3>
          <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {selectedFilter === 'all' 
              ? 'You haven\'t made any submissions yet.'
              : `No ${selectedFilter} submissions found.`
            }
          </p>
        </div>
      )}
    </motion.div>
  );
};

export default SubmissionsPanel;
