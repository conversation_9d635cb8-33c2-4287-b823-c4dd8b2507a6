import React, { useState } from 'react';
import { Terminal } from 'lucide-react';

// Import reusable components
import EditorHeader from './components/EditorHeader';
import FileTree from './components/FileTree';
import MonacoEditor from './components/MonacoEditor';
import LivePreview from './components/LivePreview';
import ApiTester from './components/ApiTester';
import Console from './components/Console';
import ActionButtons from './components/ActionButtons';

// Import custom hook
import { useFileManager } from './hooks/useFileManager';

const CodeEditorSection = ({
  code,
  setCode,
  output,
  onClearOutput,
  onHideEditor,
  isDarkMode
}) => {
  // UI State
  const [showConsole, setShowConsole] = useState(true);
  const [consoleHeight, setConsoleHeight] = useState(200);
  const [isEditorMinimized, setIsEditorMinimized] = useState(false);
  const [showFileTree, setShowFileTree] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [showApiTester, setShowApiTester] = useState(false);
  const [previewKey, setPreviewKey] = useState(0);

  // File Management Hook
  const {
    files,
    activeFile,
    createNewFile,
    deleteFile,
    switchFile,
    updateFileContent,
    installPackage,
    resetProject
  } = useFileManager(setCode);

  // Event Handlers
  const handleEditorChange = (value) => {
    const content = value || '';
    setCode(content);
    updateFileContent(activeFile, content);
  };

  const handleFileSelect = (fileName) => {
    switchFile(fileName);
  };

  const handleFileDelete = (fileName) => {
    deleteFile(fileName);
  };

  const refreshPreview = () => {
    setPreviewKey(prev => prev + 1);
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText(code);
  };

  const handleCloseEditor = () => {
    if (window.confirm('Are you sure you want to close the code editor? Your code will be lost.')) {
      setCode('');
      onHideEditor();
    }
  };

  const handleConsoleClose = () => {
    if (window.confirm('Are you sure you want to close the console? Output will be cleared.')) {
      setShowConsole(false);
      onClearOutput();
    }
  };

  const handleRunProject = () => {
    setShowPreview(true);
    refreshPreview();
  };

  return (
    <div className="flex flex-col h-full">
      {/* Editor Header */}
      <EditorHeader
        isEditorMinimized={isEditorMinimized}
        showFileTree={showFileTree}
        showPreview={showPreview}
        showApiTester={showApiTester}
        showConsole={showConsole}
        onToggleMinimize={() => setIsEditorMinimized(!isEditorMinimized)}
        onToggleFileTree={() => setShowFileTree(!showFileTree)}
        onTogglePreview={() => setShowPreview(!showPreview)}
        onToggleApiTester={() => setShowApiTester(!showApiTester)}
        onToggleConsole={() => setShowConsole(!showConsole)}
        onCreateFile={createNewFile}
        onInstallPackage={installPackage}
        onRefreshPreview={refreshPreview}
        onCopyCode={handleCopyCode}
        onHideEditor={onHideEditor}
        onCloseEditor={handleCloseEditor}
        isDarkMode={isDarkMode}
      />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Editor and Console Container */}
        <div className="flex-1 flex">
          {/* File Tree Sidebar */}
          {showFileTree && !isEditorMinimized && (
            <FileTree
              files={files}
              activeFile={activeFile}
              onFileSelect={handleFileSelect}
              onFileDelete={handleFileDelete}
              onCreateFile={createNewFile}
              isDarkMode={isDarkMode}
            />
          )}

          {/* Main Editor Area */}
          <div className="flex-1 flex flex-col">
            {/* Monaco Editor */}
            {!isEditorMinimized && (
              <div className={`flex-1 ${showConsole ? '' : 'border-b'} ${
                isDarkMode ? 'border-gray-700' : 'border-gray-200'
              }`}>
                <div className="flex h-full">
                  {/* Code Editor */}
                  <div className={`${showPreview ? 'w-1/2 border-r' : 'w-full'} ${
                    isDarkMode ? 'border-gray-700' : 'border-gray-200'
                  }`}>
                    <MonacoEditor
                      code={code}
                      activeFile={activeFile}
                      files={files}
                      onChange={handleEditorChange}
                      isDarkMode={isDarkMode}
                    />
                  </div>

                  {/* Live Preview */}
                  {showPreview && (
                    <LivePreview
                      files={files}
                      previewKey={previewKey}
                      onRefresh={refreshPreview}
                      isDarkMode={isDarkMode}
                    />
                  )}
                </div>
              </div>
            )}
          </div>

          {/* API Tester Panel */}
          {showApiTester && !isEditorMinimized && (
            <ApiTester isDarkMode={isDarkMode} />
          )}
        </div>

        {/* Minimized Editor Placeholder */}
        {isEditorMinimized && (
          <div className={`flex items-center justify-center p-8 border-b ${
            isDarkMode ? 'border-gray-700 bg-gray-800/50' : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="text-center">
              <div className={`text-4xl mb-2 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`}>
                📝
              </div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Code editor is minimized
              </p>
              <button
                onClick={() => setIsEditorMinimized(false)}
                className={`mt-2 px-3 py-1 text-xs rounded transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Expand Editor
              </button>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {!isEditorMinimized && (
          <ActionButtons
            files={files}
            activeFile={activeFile}
            onResetProject={resetProject}
            onRunProject={handleRunProject}
            isDarkMode={isDarkMode}
          />
        )}

        {/* Console Output */}
        {showConsole && (
          <Console
            output={output}
            consoleHeight={consoleHeight}
            onClearOutput={onClearOutput}
            onHeightChange={setConsoleHeight}
            onHide={() => setShowConsole(false)}
            onClose={handleConsoleClose}
            isDarkMode={isDarkMode}
          />
        )}

        {/* Show Console Button when hidden */}
        {!showConsole && !isEditorMinimized && (
          <div className={`flex items-center justify-center p-3 border-t ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
          }`}>
            <button
              onClick={() => setShowConsole(true)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <Terminal size={16} />
              Show Console
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeEditorSection;
