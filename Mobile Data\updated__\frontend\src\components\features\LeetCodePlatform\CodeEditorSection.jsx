import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Editor } from '@monaco-editor/react';
import {
  Play,
  Send,
  RotateCcw,
  Copy,
  Download,
  Settings,
  Terminal,
  ChevronUp,
  ChevronDown,
  X,
  Minimize2,
  Maximize2,
  Eye,
  EyeOff
} from 'lucide-react';

const CodeEditorSection = ({
  code,
  setCode,
  selectedLanguage,
  setSelectedLanguage,
  languages,
  output,
  isRunning,
  isSubmitting,
  onRunCode,
  onSubmit,
  onReset,
  onClearOutput,
  isDarkMode
}) => {
  const [showConsole, setShowConsole] = useState(true);
  const [consoleHeight, setConsoleHeight] = useState(200);
  const [customInput, setCustomInput] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [isEditorMinimized, setIsEditorMinimized] = useState(false);
  const [isEditorHidden, setIsEditorHidden] = useState(false);

  const handleEditorChange = (value) => {
    setCode(value || '');
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  const handleDownloadCode = () => {
    const fileExtensions = {
      javascript: 'js',
      python: 'py',
      java: 'java',
      cpp: 'cpp'
    };
    
    const extension = fileExtensions[selectedLanguage] || 'txt';
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `solution.${extension}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Don't render anything if editor is hidden
  if (isEditorHidden) {
    return (
      <div className={`flex items-center justify-center p-4 border-t ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        <button
          onClick={() => setIsEditorHidden(false)}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <Eye size={16} />
          Show Code Editor
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Editor Header */}
      <div className={`flex items-center justify-between px-4 py-3 border-b ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        <div className="flex items-center gap-4">
          <h2 className="font-semibold">Code Editor</h2>

          {/* Language Selector */}
          {!isEditorMinimized && (
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              className={`px-3 py-2 rounded-lg border text-sm font-medium ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
            >
              {languages.map((lang) => (
                <option key={lang.value} value={lang.value}>
                  {lang.label}
                </option>
              ))}
            </select>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Editor Actions */}
          {!isEditorMinimized && (
            <>
              <button
                onClick={handleCopyCode}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Copy Code"
              >
                <Copy size={16} />
              </button>

              <button
                onClick={handleDownloadCode}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Download Code"
              >
                <Download size={16} />
              </button>

              <button
                onClick={() => setShowConsole(!showConsole)}
                className={`p-2 rounded-lg transition-colors ${
                  showConsole
                    ? 'text-blue-500 bg-blue-500/10'
                    : isDarkMode
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Toggle Console"
              >
                <Terminal size={16} />
              </button>
            </>
          )}

          {/* Minimize/Maximize Button */}
          <button
            onClick={() => setIsEditorMinimized(!isEditorMinimized)}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
            }`}
            title={isEditorMinimized ? "Maximize Editor" : "Minimize Editor"}
          >
            {isEditorMinimized ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
          </button>

          {/* Hide Button */}
          <button
            onClick={() => setIsEditorHidden(true)}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
            }`}
            title="Hide Editor"
          >
            <EyeOff size={16} />
          </button>

          {/* Close Button */}
          <button
            onClick={() => {
              if (window.confirm('Are you sure you want to close the code editor? Your code will be lost.')) {
                setCode('');
                setIsEditorHidden(true);
              }
            }}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'text-red-400 hover:text-red-300 hover:bg-red-900/20'
                : 'text-red-600 hover:text-red-700 hover:bg-red-100'
            }`}
            title="Close Editor"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {/* Editor and Console Container */}
      <div className="flex-1 flex flex-col">
        {/* Monaco Editor */}
        {!isEditorMinimized && (
          <div className={`flex-1 ${showConsole ? '' : 'border-b'} ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <Editor
              height="100%"
              language={selectedLanguage === 'cpp' ? 'cpp' : selectedLanguage}
              value={code}
              onChange={handleEditorChange}
              theme={isDarkMode ? 'vs-dark' : 'light'}
              options={{
                minimap: { enabled: false },
                fontSize: 14,
                lineNumbers: 'on',
                roundedSelection: false,
                scrollBeyondLastLine: false,
                automaticLayout: true,
                tabSize: 2,
                insertSpaces: true,
                wordWrap: 'on',
                contextmenu: true,
                selectOnLineNumbers: true,
                glyphMargin: false,
                folding: true,
                lineDecorationsWidth: 10,
                lineNumbersMinChars: 3,
                renderLineHighlight: 'line',
                scrollbar: {
                  vertical: 'visible',
                  horizontal: 'visible',
                  useShadows: false,
                  verticalHasArrows: false,
                  horizontalHasArrows: false,
                },
              }}
            />
          </div>
        )}

        {/* Minimized Editor Placeholder */}
        {isEditorMinimized && (
          <div className={`flex items-center justify-center p-8 border-b ${
            isDarkMode ? 'border-gray-700 bg-gray-800/50' : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="text-center">
              <div className={`text-4xl mb-2 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`}>
                📝
              </div>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Code editor is minimized
              </p>
              <button
                onClick={() => setIsEditorMinimized(false)}
                className={`mt-2 px-3 py-1 text-xs rounded transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Expand Editor
              </button>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {!isEditorMinimized && (
          <div className={`flex items-center justify-between px-4 py-3 border-t ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center gap-3">
              {/* Custom Input Toggle */}
              <button
                onClick={() => setShowCustomInput(!showCustomInput)}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  showCustomInput
                    ? 'bg-blue-500 text-white'
                    : isDarkMode
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Custom Input
              </button>

              {/* Reset Button */}
              <button
                onClick={onReset}
                className={`flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <RotateCcw size={16} />
                Reset
              </button>
            </div>

            <div className="flex items-center gap-3">
              {/* Run Button */}
              <button
                onClick={onRunCode}
                disabled={isRunning}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  isRunning
                    ? 'bg-gray-500 text-white cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {isRunning ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                    Running...
                  </>
                ) : (
                  <>
                    <Play size={16} />
                    Run
                  </>
                )}
              </button>

              {/* Submit Button */}
              <button
                onClick={onSubmit}
                disabled={isSubmitting}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  isSubmitting
                    ? 'bg-gray-500 text-white cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send size={16} />
                    Submit
                  </>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Custom Input Panel */}
        {showCustomInput && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className={`border-t ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'}`}
          >
            <div className="p-4">
              <label className="block text-sm font-medium mb-2">Custom Test Case Input:</label>
              <textarea
                value={customInput}
                onChange={(e) => setCustomInput(e.target.value)}
                placeholder="Enter your test case input here..."
                className={`w-full h-20 px-3 py-2 rounded-lg border text-sm font-mono ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none`}
              />
            </div>
          </motion.div>
        )}

        {/* Console Output */}
        {showConsole && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: consoleHeight }}
            className={`border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} flex flex-col`}
          >
            {/* Console Header */}
            <div className={`flex items-center justify-between px-4 py-2 border-b ${
              isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
            }`}>
              <div className="flex items-center gap-2">
                <Terminal size={16} />
                <span className="text-sm font-medium">Console</span>
              </div>

              <div className="flex items-center gap-2">
                {/* Resize Controls */}
                <button
                  onClick={() => setConsoleHeight(Math.max(100, consoleHeight - 50))}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Decrease Console Height"
                >
                  <ChevronDown size={14} />
                </button>
                <button
                  onClick={() => setConsoleHeight(Math.min(400, consoleHeight + 50))}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Increase Console Height"
                >
                  <ChevronUp size={14} />
                </button>

                {/* Clear Console Button */}
                <button
                  onClick={onClearOutput}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Clear Console"
                >
                  <RotateCcw size={14} />
                </button>

                {/* Hide Console Button */}
                <button
                  onClick={() => setShowConsole(false)}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Hide Console"
                >
                  <EyeOff size={14} />
                </button>

                {/* Close Console Button */}
                <button
                  onClick={() => {
                    if (window.confirm('Are you sure you want to close the console? Output will be cleared.')) {
                      setShowConsole(false);
                      onClearOutput();
                    }
                  }}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-red-400 hover:text-red-300'
                      : 'text-red-600 hover:text-red-700'
                  }`}
                  title="Close Console"
                >
                  <X size={14} />
                </button>
              </div>
            </div>

            {/* Console Content */}
            <div className={`flex-1 p-4 overflow-auto ${
              isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
            }`}>
              {output ? (
                <pre className={`text-sm font-mono whitespace-pre-wrap ${
                  isDarkMode ? 'text-green-400' : 'text-green-600'
                }`}>
                  {output}
                </pre>
              ) : (
                <div className={`text-sm ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                  Console output will appear here...
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Show Console Button when hidden */}
        {!showConsole && !isEditorMinimized && (
          <div className={`flex items-center justify-center p-3 border-t ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
          }`}>
            <button
              onClick={() => setShowConsole(true)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <Terminal size={16} />
              Show Console
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeEditorSection;
