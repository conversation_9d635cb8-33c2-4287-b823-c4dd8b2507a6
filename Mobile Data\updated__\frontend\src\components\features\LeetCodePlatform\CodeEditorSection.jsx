import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Editor } from '@monaco-editor/react';
import {
  Play,
  RotateCcw,
  Copy,
  Download,
  Terminal,
  ChevronUp,
  ChevronDown,
  X,
  Minimize2,
  Maximize2,
  EyeOff,
  FileText,
  FilePlus,
  Trash2,
  Globe,
  Package,
  Zap,
  Monitor,
  Layers
} from 'lucide-react';

const CodeEditorSection = ({
  code,
  setCode,
  output,
  onClearOutput,
  onHideEditor,
  isDarkMode
}) => {
  const [showConsole, setShowConsole] = useState(true);
  const [consoleHeight, setConsoleHeight] = useState(200);
  const [isEditorMinimized, setIsEditorMinimized] = useState(false);

  // Virtual file system state
  const [files, setFiles] = useState({
    'index.html': {
      name: 'index.html',
      content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UpCoding Project</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <h1>Welcome to UpCoding!</h1>
        <p>Start building your project here.</p>
        <button onclick="greet()">Click me!</button>
    </div>
    <script src="script.js"></script>
</body>
</html>`,
      type: 'html'
    },
    'style.css': {
      name: 'style.css',
      content: `body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

#app {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
}

button:hover {
    background: #45a049;
}`,
      type: 'css'
    },
    'script.js': {
      name: 'script.js',
      content: `function greet() {
    alert('Hello from UpCoding!');
}

// Add your JavaScript code here
console.log('UpCoding project loaded!');

// Example API call
async function fetchData() {
    try {
        const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
        const data = await response.json();
        console.log('API Response:', data);
    } catch (error) {
        console.error('API Error:', error);
    }
}

// Uncomment to test API
// fetchData();`,
      type: 'javascript'
    },
    'package.json': {
      name: 'package.json',
      content: `{
  "name": "upcoding-project",
  "version": "1.0.0",
  "description": "A UpCoding project",
  "main": "index.html",
  "scripts": {
    "start": "live-server",
    "build": "webpack --mode production",
    "dev": "webpack serve --mode development"
  },
  "dependencies": {
    "axios": "^1.6.0",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "webpack": "^5.89.0",
    "webpack-cli": "^5.1.4",
    "webpack-dev-server": "^4.15.1"
  },
  "keywords": ["upcoding", "web", "development"],
  "author": "UpCoding User",
  "license": "MIT"
}`,
      type: 'json'
    }
  });

  const [activeFile, setActiveFile] = useState('index.html');
  const [showFileTree, setShowFileTree] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [showApiTester, setShowApiTester] = useState(false);
  const [previewKey, setPreviewKey] = useState(0);

  // API Tester state
  const [apiUrl, setApiUrl] = useState('https://jsonplaceholder.typicode.com/posts/1');
  const [apiMethod, setApiMethod] = useState('GET');
  const [apiHeaders, setApiHeaders] = useState('{"Content-Type": "application/json"}');
  const [apiBody, setApiBody] = useState('');
  const [apiResponse, setApiResponse] = useState('');
  const [isApiLoading, setIsApiLoading] = useState(false);

  const handleEditorChange = (value) => {
    const content = value || '';
    setCode(content);
    // Update the active file content
    setFiles(prev => ({
      ...prev,
      [activeFile]: {
        ...prev[activeFile],
        content
      }
    }));
  };

  // File management functions
  const createNewFile = () => {
    const fileName = prompt('Enter file name (e.g., newfile.js, styles.css):');
    if (fileName && !files[fileName]) {
      const extension = fileName.split('.').pop().toLowerCase();
      let fileType = 'text';
      let defaultContent = '';

      switch (extension) {
        case 'html':
          fileType = 'html';
          defaultContent = '<!DOCTYPE html>\n<html>\n<head>\n    <title>New Page</title>\n</head>\n<body>\n    \n</body>\n</html>';
          break;
        case 'css':
          fileType = 'css';
          defaultContent = '/* Add your styles here */\n';
          break;
        case 'js':
          fileType = 'javascript';
          defaultContent = '// Add your JavaScript code here\n';
          break;
        case 'json':
          fileType = 'json';
          defaultContent = '{\n  \n}';
          break;
        default:
          fileType = 'text';
      }

      setFiles(prev => ({
        ...prev,
        [fileName]: {
          name: fileName,
          content: defaultContent,
          type: fileType
        }
      }));
      setActiveFile(fileName);
      setCode(defaultContent);
    }
  };

  const deleteFile = (fileName) => {
    if (Object.keys(files).length <= 1) {
      alert('Cannot delete the last file!');
      return;
    }

    if (confirm(`Are you sure you want to delete ${fileName}?`)) {
      const newFiles = { ...files };
      delete newFiles[fileName];
      setFiles(newFiles);

      if (activeFile === fileName) {
        const firstFile = Object.keys(newFiles)[0];
        setActiveFile(firstFile);
        setCode(newFiles[firstFile].content);
      }
    }
  };

  const switchFile = (fileName) => {
    setActiveFile(fileName);
    setCode(files[fileName].content);
  };

  // Package management simulation
  const installPackage = () => {
    const packageName = prompt('Enter package name to install (e.g., axios, lodash):');
    if (packageName) {
      const packageJson = JSON.parse(files['package.json'].content);
      packageJson.dependencies[packageName] = '^1.0.0';

      setFiles(prev => ({
        ...prev,
        'package.json': {
          ...prev['package.json'],
          content: JSON.stringify(packageJson, null, 2)
        }
      }));

      // Simulate installation
      setTimeout(() => {
        alert(`Package "${packageName}" installed successfully!`);
      }, 1000);
    }
  };

  // Generate live preview
  const generatePreview = () => {
    const html = files['index.html']?.content || '';
    const css = files['style.css']?.content || '';
    const js = files['script.js']?.content || '';

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>${css}</style>
      </head>
      <body>
        ${html.replace(/<script.*?src.*?<\/script>/gi, '').replace(/<link.*?rel="stylesheet".*?>/gi, '')}
        <script>
          // Wrap in try-catch to prevent errors from breaking the preview
          try {
            ${js}
          } catch (error) {
            console.error('Preview Error:', error);
          }
        </script>
      </body>
      </html>
    `;
  };

  const refreshPreview = () => {
    setPreviewKey(prev => prev + 1);
  };

  // API Tester functions
  const testApi = async () => {
    setIsApiLoading(true);
    try {
      const headers = JSON.parse(apiHeaders);
      const options = {
        method: apiMethod,
        headers
      };

      if (apiMethod !== 'GET' && apiBody) {
        options.body = apiBody;
      }

      const response = await fetch(apiUrl, options);
      const data = await response.text();

      setApiResponse(`Status: ${response.status} ${response.statusText}\n\nResponse:\n${data}`);
    } catch (error) {
      setApiResponse(`Error: ${error.message}`);
    }
    setIsApiLoading(false);
  };

  const handleCopyCode = () => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };



  return (
    <div className="flex flex-col h-full">
      {/* Editor Header */}
      <div className={`flex items-center justify-between px-4 py-3 border-b ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        <div className="flex items-center gap-4">
          <h2 className="font-semibold">Code Editor</h2>

          {/* View Toggle Buttons */}
          {!isEditorMinimized && (
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowFileTree(!showFileTree)}
                className={`p-2 rounded-lg transition-colors ${
                  showFileTree
                    ? 'bg-blue-500 text-white'
                    : isDarkMode
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Toggle File Tree"
              >
                <Layers size={16} />
              </button>

              <button
                onClick={() => setShowPreview(!showPreview)}
                className={`p-2 rounded-lg transition-colors ${
                  showPreview
                    ? 'bg-green-500 text-white'
                    : isDarkMode
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Toggle Preview"
              >
                <Monitor size={16} />
              </button>

              <button
                onClick={() => setShowApiTester(!showApiTester)}
                className={`p-2 rounded-lg transition-colors ${
                  showApiTester
                    ? 'bg-purple-500 text-white'
                    : isDarkMode
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Toggle API Tester"
              >
                <Zap size={16} />
              </button>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* File Management Actions */}
          {!isEditorMinimized && (
            <>
              <button
                onClick={createNewFile}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="New File"
              >
                <FilePlus size={16} />
              </button>

              <button
                onClick={installPackage}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Install Package"
              >
                <Package size={16} />
              </button>

              <button
                onClick={refreshPreview}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Refresh Preview"
              >
                <Globe size={16} />
              </button>

              <button
                onClick={handleCopyCode}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                    : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Copy Code"
              >
                <Copy size={16} />
              </button>

              <button
                onClick={() => setShowConsole(!showConsole)}
                className={`p-2 rounded-lg transition-colors ${
                  showConsole
                    ? 'text-blue-500 bg-blue-500/10'
                    : isDarkMode
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                }`}
                title="Toggle Console"
              >
                <Terminal size={16} />
              </button>
            </>
          )}

          {/* Minimize/Maximize Button */}
          <button
            onClick={() => setIsEditorMinimized(!isEditorMinimized)}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
            }`}
            title={isEditorMinimized ? "Maximize Editor" : "Minimize Editor"}
          >
            {isEditorMinimized ? <Maximize2 size={16} /> : <Minimize2 size={16} />}
          </button>

          {/* Hide Button */}
          <button
            onClick={onHideEditor}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
            }`}
            title="Hide Editor"
          >
            <EyeOff size={16} />
          </button>

          {/* Close Button */}
          <button
            onClick={() => {
              if (window.confirm('Are you sure you want to close the code editor? Your code will be lost.')) {
                setCode('');
                onHideEditor();
              }
            }}
            className={`p-2 rounded-lg transition-colors ${
              isDarkMode
                ? 'text-red-400 hover:text-red-300 hover:bg-red-900/20'
                : 'text-red-600 hover:text-red-700 hover:bg-red-100'
            }`}
            title="Close Editor"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {/* Editor and Console Container */}
      <div className="flex-1 flex">
        {/* File Tree Sidebar */}
        {showFileTree && !isEditorMinimized && (
          <div className={`w-64 border-r ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'} flex flex-col`}>
            <div className={`p-3 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold">Files</h3>
                <button
                  onClick={createNewFile}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                      : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                  }`}
                  title="New File"
                >
                  <FilePlus size={14} />
                </button>
              </div>
            </div>

            <div className="flex-1 overflow-auto p-2">
              {Object.entries(files).map(([fileName]) => (
                <div
                  key={fileName}
                  className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
                    activeFile === fileName
                      ? isDarkMode
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-100 text-blue-800'
                      : isDarkMode
                        ? 'hover:bg-gray-700 text-gray-300'
                        : 'hover:bg-gray-200 text-gray-700'
                  }`}
                  onClick={() => switchFile(fileName)}
                >
                  <div className="flex items-center gap-2">
                    <FileText size={14} />
                    <span className="text-sm">{fileName}</span>
                  </div>
                  {Object.keys(files).length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteFile(fileName);
                      }}
                      className={`p-1 rounded transition-colors ${
                        isDarkMode
                          ? 'text-gray-400 hover:text-red-400'
                          : 'text-gray-500 hover:text-red-500'
                      }`}
                      title="Delete File"
                    >
                      <Trash2 size={12} />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Monaco Editor */}
          {!isEditorMinimized && (
            <div className={`flex-1 ${showConsole ? '' : 'border-b'} ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <div className="flex h-full">
                {/* Code Editor */}
                <div className={`${showPreview ? 'w-1/2 border-r' : 'w-full'} ${
                  isDarkMode ? 'border-gray-700' : 'border-gray-200'
                }`}>
                  <div className={`p-2 border-b text-sm ${
                    isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-300' : 'border-gray-200 bg-gray-50 text-gray-700'
                  }`}>
                    <span>{activeFile}</span>
                  </div>
                  <Editor
                    height="calc(100% - 40px)"
                    language={files[activeFile]?.type === 'javascript' ? 'javascript' : files[activeFile]?.type}
                    value={code}
                    onChange={handleEditorChange}
                    theme={isDarkMode ? 'vs-dark' : 'light'}
                    options={{
                      minimap: { enabled: false },
                      fontSize: 14,
                      lineNumbers: 'on',
                      roundedSelection: false,
                      scrollBeyondLastLine: false,
                      automaticLayout: true,
                      tabSize: 2,
                      insertSpaces: true,
                      wordWrap: 'on',
                      contextmenu: true,
                      selectOnLineNumbers: true,
                      glyphMargin: false,
                      folding: true,
                      lineDecorationsWidth: 10,
                      lineNumbersMinChars: 3,
                      renderLineHighlight: 'line',
                      scrollbar: {
                        vertical: 'visible',
                        horizontal: 'visible',
                        useShadows: false,
                        verticalHasArrows: false,
                        horizontalHasArrows: false,
                      },
                    }}
                  />
                </div>

                {/* Live Preview */}
                {showPreview && (
                  <div className="w-1/2 flex flex-col">
                    <div className={`p-2 border-b text-sm flex items-center justify-between ${
                      isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-300' : 'border-gray-200 bg-gray-50 text-gray-700'
                    }`}>
                      <span>Live Preview</span>
                      <button
                        onClick={refreshPreview}
                        className={`p-1 rounded transition-colors ${
                          isDarkMode
                            ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                            : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
                        }`}
                        title="Refresh Preview"
                      >
                        <RotateCcw size={14} />
                      </button>
                    </div>
                    <iframe
                      key={previewKey}
                      srcDoc={generatePreview()}
                      className="flex-1 bg-white"
                      title="Live Preview"
                      sandbox="allow-scripts allow-same-origin"
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* API Tester Panel */}
        {showApiTester && !isEditorMinimized && (
          <div className={`w-80 border-l ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'} flex flex-col`}>
            <div className={`p-3 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <h3 className="text-sm font-semibold">API Tester</h3>
            </div>

            <div className="flex-1 overflow-auto p-3 space-y-3">
              {/* URL Input */}
              <div>
                <label className="block text-xs font-medium mb-1">URL</label>
                <input
                  type="text"
                  value={apiUrl}
                  onChange={(e) => setApiUrl(e.target.value)}
                  className={`w-full px-2 py-1 text-xs rounded border ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:ring-1 focus:ring-blue-500`}
                  placeholder="https://api.example.com/endpoint"
                />
              </div>

              {/* Method Selector */}
              <div>
                <label className="block text-xs font-medium mb-1">Method</label>
                <select
                  value={apiMethod}
                  onChange={(e) => setApiMethod(e.target.value)}
                  className={`w-full px-2 py-1 text-xs rounded border ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:ring-1 focus:ring-blue-500`}
                >
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="DELETE">DELETE</option>
                  <option value="PATCH">PATCH</option>
                </select>
              </div>

              {/* Headers */}
              <div>
                <label className="block text-xs font-medium mb-1">Headers (JSON)</label>
                <textarea
                  value={apiHeaders}
                  onChange={(e) => setApiHeaders(e.target.value)}
                  className={`w-full px-2 py-1 text-xs rounded border h-16 resize-none ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:ring-1 focus:ring-blue-500`}
                  placeholder='{"Content-Type": "application/json"}'
                />
              </div>

              {/* Body */}
              {apiMethod !== 'GET' && (
                <div>
                  <label className="block text-xs font-medium mb-1">Body</label>
                  <textarea
                    value={apiBody}
                    onChange={(e) => setApiBody(e.target.value)}
                    className={`w-full px-2 py-1 text-xs rounded border h-20 resize-none ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-1 focus:ring-blue-500`}
                    placeholder='{"key": "value"}'
                  />
                </div>
              )}

              {/* Test Button */}
              <button
                onClick={testApi}
                disabled={isApiLoading}
                className={`w-full py-2 px-3 text-xs rounded font-medium transition-colors ${
                  isApiLoading
                    ? 'bg-gray-500 text-white cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isApiLoading ? 'Testing...' : 'Test API'}
              </button>

              {/* Response */}
              {apiResponse && (
                <div>
                  <label className="block text-xs font-medium mb-1">Response</label>
                  <pre className={`w-full px-2 py-1 text-xs rounded border h-32 overflow-auto ${
                    isDarkMode
                      ? 'bg-gray-900 border-gray-600 text-green-400'
                      : 'bg-gray-50 border-gray-300 text-gray-800'
                  }`}>
                    {apiResponse}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Minimized Editor Placeholder */}
      {isEditorMinimized && (
        <div className={`flex items-center justify-center p-8 border-b ${
          isDarkMode ? 'border-gray-700 bg-gray-800/50' : 'border-gray-200 bg-gray-50'
        }`}>
          <div className="text-center">
            <div className={`text-4xl mb-2 ${isDarkMode ? 'text-gray-600' : 'text-gray-400'}`}>
              📝
            </div>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Code editor is minimized
            </p>
            <button
              onClick={() => setIsEditorMinimized(false)}
              className={`mt-2 px-3 py-1 text-xs rounded transition-colors ${
                isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Expand Editor
            </button>
          </div>
        </div>
      )}

        {/* Action Buttons */}
        {!isEditorMinimized && (
          <div className={`flex items-center justify-between px-4 py-3 border-t ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center gap-3">
              {/* Project Actions */}
              <button
                onClick={() => {
                  const projectData = {
                    files,
                    activeFile,
                    timestamp: new Date().toISOString()
                  };
                  const blob = new Blob([JSON.stringify(projectData, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = 'upcoding-project.json';
                  document.body.appendChild(a);
                  a.click();
                  document.body.removeChild(a);
                  URL.revokeObjectURL(url);
                }}
                className={`flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <Download size={16} />
                Export Project
              </button>

              {/* Reset Button */}
              <button
                onClick={() => {
                  if (confirm('Reset all files to default? This cannot be undone.')) {
                    // Reset to default files
                    setFiles({
                      'index.html': files['index.html'],
                      'style.css': files['style.css'],
                      'script.js': files['script.js'],
                      'package.json': files['package.json']
                    });
                    setActiveFile('index.html');
                    setCode(files['index.html'].content);
                  }
                }}
                className={`flex items-center gap-2 px-3 py-2 text-sm rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                <RotateCcw size={16} />
                Reset Project
              </button>
            </div>

            <div className="flex items-center gap-3">
              {/* Run Project */}
              <button
                onClick={() => {
                  setShowPreview(true);
                  refreshPreview();
                }}
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium bg-green-600 text-white hover:bg-green-700 transition-colors"
              >
                <Play size={16} />
                Run Project
              </button>

              {/* Deploy Simulation */}
              <button
                onClick={() => {
                  alert('🚀 Project deployed successfully!\n\nYour project is now live at:\nhttps://your-project.upcoding.dev');
                }}
                className="flex items-center gap-2 px-4 py-2 rounded-lg font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors"
              >
                <Globe size={16} />
                Deploy
              </button>
            </div>
          </div>
        )}

        {/* Console Output */}
        {showConsole && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: consoleHeight }}
            className={`border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} flex flex-col`}
          >
            {/* Console Header */}
            <div className={`flex items-center justify-between px-4 py-2 border-b ${
              isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
            }`}>
              <div className="flex items-center gap-2">
                <Terminal size={16} />
                <span className="text-sm font-medium">Console</span>
              </div>

              <div className="flex items-center gap-2">
                {/* Resize Controls */}
                <button
                  onClick={() => setConsoleHeight(Math.max(100, consoleHeight - 50))}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Decrease Console Height"
                >
                  <ChevronDown size={14} />
                </button>
                <button
                  onClick={() => setConsoleHeight(Math.min(400, consoleHeight + 50))}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Increase Console Height"
                >
                  <ChevronUp size={14} />
                </button>

                {/* Clear Console Button */}
                <button
                  onClick={onClearOutput}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Clear Console"
                >
                  <RotateCcw size={14} />
                </button>

                {/* Hide Console Button */}
                <button
                  onClick={() => setShowConsole(false)}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Hide Console"
                >
                  <EyeOff size={14} />
                </button>

                {/* Close Console Button */}
                <button
                  onClick={() => {
                    if (window.confirm('Are you sure you want to close the console? Output will be cleared.')) {
                      setShowConsole(false);
                      onClearOutput();
                    }
                  }}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-red-400 hover:text-red-300'
                      : 'text-red-600 hover:text-red-700'
                  }`}
                  title="Close Console"
                >
                  <X size={14} />
                </button>
              </div>
            </div>

            {/* Console Content */}
            <div className={`flex-1 p-4 overflow-auto ${
              isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
            }`}>
              {output ? (
                <pre className={`text-sm font-mono whitespace-pre-wrap ${
                  isDarkMode ? 'text-green-400' : 'text-green-600'
                }`}>
                  {output}
                </pre>
              ) : (
                <div className={`text-sm ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                  Console output will appear here...
                  <br />
                  <br />
                  💡 Tips:
                  <br />
                  • Use console.log() in your JavaScript files
                  <br />
                  • Test API calls with the API Tester panel
                  <br />
                  • View live preview of your HTML/CSS/JS
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Console Output */}
        {showConsole && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: consoleHeight }}
            className={`border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} flex flex-col`}
          >
            {/* Console Header */}
            <div className={`flex items-center justify-between px-4 py-2 border-b ${
              isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
            }`}>
              <div className="flex items-center gap-2">
                <Terminal size={16} />
                <span className="text-sm font-medium">Console</span>
              </div>

              <div className="flex items-center gap-2">
                {/* Resize Controls */}
                <button
                  onClick={() => setConsoleHeight(Math.max(100, consoleHeight - 50))}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Decrease Console Height"
                >
                  <ChevronDown size={14} />
                </button>
                <button
                  onClick={() => setConsoleHeight(Math.min(400, consoleHeight + 50))}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Increase Console Height"
                >
                  <ChevronUp size={14} />
                </button>

                {/* Clear Console Button */}
                <button
                  onClick={onClearOutput}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Clear Console"
                >
                  <RotateCcw size={14} />
                </button>

                {/* Hide Console Button */}
                <button
                  onClick={() => setShowConsole(false)}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-gray-400 hover:text-gray-300'
                      : 'text-gray-600 hover:text-gray-700'
                  }`}
                  title="Hide Console"
                >
                  <EyeOff size={14} />
                </button>

                {/* Close Console Button */}
                <button
                  onClick={() => {
                    if (window.confirm('Are you sure you want to close the console? Output will be cleared.')) {
                      setShowConsole(false);
                      onClearOutput();
                    }
                  }}
                  className={`p-1 rounded transition-colors ${
                    isDarkMode
                      ? 'text-red-400 hover:text-red-300'
                      : 'text-red-600 hover:text-red-700'
                  }`}
                  title="Close Console"
                >
                  <X size={14} />
                </button>
              </div>
            </div>

            {/* Console Content */}
            <div className={`flex-1 p-4 overflow-auto ${
              isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
            }`}>
              {output ? (
                <pre className={`text-sm font-mono whitespace-pre-wrap ${
                  isDarkMode ? 'text-green-400' : 'text-green-600'
                }`}>
                  {output}
                </pre>
              ) : (
                <div className={`text-sm ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                  Console output will appear here...
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Show Console Button when hidden */}
        {!showConsole && !isEditorMinimized && (
          <div className={`flex items-center justify-center p-3 border-t ${
            isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
          }`}>
            <button
              onClick={() => setShowConsole(true)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <Terminal size={16} />
              Show Console
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeEditorSection;
