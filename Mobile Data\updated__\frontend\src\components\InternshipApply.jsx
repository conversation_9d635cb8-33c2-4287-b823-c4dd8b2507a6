import React from "react";

const InternshipApply = () => {
  return (
    <div className="w-full max-w-md mx-auto mt-8 p-6 bg-gradient-to-br from-[#1e293b]/80 to-[#0d1117]/90 border border-blue-400/30 rounded-2xl shadow-xl flex flex-col items-center text-center transition-all duration-300 hover:shadow-2xl hover:border-blue-500/60">
      <h2 className="text-xl md:text-2xl font-bold text-white mb-4 flex items-center gap-2">
        <span className="text-2xl">🚀</span> Apply Here for Internship
      </h2>
      <a
        href="https://codexuslabs.com/internship"
        target="_blank"
        rel="noopener noreferrer"
        className="inline-block px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg shadow-md hover:scale-105 hover:bg-blue-600 transition-all duration-300 border border-white/10"
      >
        Go to Internship Portal
      </a>
    </div>
  );
};

export default InternshipApply;
