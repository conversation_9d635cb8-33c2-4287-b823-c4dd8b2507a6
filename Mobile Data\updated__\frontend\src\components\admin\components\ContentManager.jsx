import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, FileText, Upload, Download, Image } from 'lucide-react';
import RichTextEditor from './RichTextEditor';

const ContentManager = ({ theme }) => {
  const [contents, setContents] = useState([
    {
      id: 1,
      title: 'Getting Started Guide',
      explanation: '<h2>Welcome to Our Platform</h2><p>This comprehensive guide will help you get started with our learning platform. Follow these steps to make the most of your learning experience.</p><h3>Step 1: Set Up Your Profile</h3><p>Complete your profile information to personalize your learning journey.</p>',
      attachments: [
        { name: 'setup-guide.pdf', type: 'file', size: '2.3 MB' },
        { name: 'welcome-banner.jpg', type: 'image', size: '1.1 MB' }
      ],
      category: 'Documentation',
      status: 'published',
      createdAt: '2024-01-15',
      updatedAt: '2024-01-15'
    },
    {
      id: 2,
      title: 'Course Completion Certificate Template',
      explanation: '<h2>Certificate Information</h2><p>This template is used for generating course completion certificates.</p><ul><li>Includes student name and course details</li><li>Features official branding and signatures</li><li>Available in PDF format</li></ul>',
      attachments: [
        { name: 'certificate-template.pdf', type: 'file', size: '856 KB' },
        { name: 'logo-high-res.png', type: 'image', size: '445 KB' }
      ],
      category: 'Templates',
      status: 'draft',
      createdAt: '2024-01-14',
      updatedAt: '2024-01-14'
    }
  ]);

  const [showEditor, setShowEditor] = useState(false);
  const [editingContent, setEditingContent] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    explanation: '',
    category: 'Documentation',
    status: 'draft',
    attachments: []
  });

  const categories = ['Documentation', 'Templates', 'Resources', 'Guides', 'Policies'];
  const statuses = ['draft', 'published', 'archived'];



  const handleAdd = () => {
    setEditingContent(null);
    setFormData({
      title: '',
      explanation: '',
      category: 'Documentation',
      status: 'draft',
      attachments: []
    });
    setShowEditor(true);
  };

  const handleEdit = (content) => {
    setEditingContent(content);
    setFormData({
      title: content.title,
      explanation: content.explanation,
      category: content.category,
      status: content.status,
      attachments: [...content.attachments]
    });
    setShowEditor(true);
  };

  const handleSave = () => {
    if (!formData.title.trim() || !formData.explanation.trim()) {
      alert('Please fill in all required fields');
      return;
    }

    const now = new Date().toISOString().split('T')[0];

    if (editingContent) {
      setContents(prev => prev.map(content => 
        content.id === editingContent.id 
          ? { ...content, ...formData, updatedAt: now }
          : content
      ));
    } else {
      const newContent = {
        id: Date.now(),
        ...formData,
        createdAt: now,
        updatedAt: now
      };
      setContents(prev => [...prev, newContent]);
    }
    setShowEditor(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this content?')) {
      setContents(prev => prev.filter(content => content.id !== id));
    }
  };

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files);
    const newAttachments = files.map(file => ({
      name: file.name,
      type: file.type.startsWith('image/') ? 'image' : 'file',
      size: (file.size / (1024 * 1024)).toFixed(1) + ' MB'
    }));
    
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...newAttachments]
    }));
  };

  const removeAttachment = (index) => {
    setFormData(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published': return 'text-green-600 bg-green-100';
      case 'draft': return 'text-yellow-600 bg-yellow-100';
      case 'archived': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getCategoryColor = (category) => {
    const colors = {
      'Documentation': 'bg-blue-100 text-blue-800',
      'Templates': 'bg-purple-100 text-purple-800',
      'Resources': 'bg-green-100 text-green-800',
      'Guides': 'bg-orange-100 text-orange-800',
      'Policies': 'bg-red-100 text-red-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Content Manager
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Manage content with rich text and file attachments
          </p>
        </div>
        
        <button
          onClick={handleAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add Content</span>
        </button>
      </div>

      {/* Content List */}
      <div className="space-y-4">
        {contents.map((content) => (
          <div
            key={content.id}
            className={`rounded-lg border p-6 ${
              theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-3">
                  <FileText size={20} className={theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} />
                  <h3 className={`text-lg font-semibold ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {content.title}
                  </h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(content.category)}`}>
                    {content.category}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(content.status)}`}>
                    {content.status.charAt(0).toUpperCase() + content.status.slice(1)}
                  </span>
                </div>

                {/* Content Preview */}
                <div className={`prose prose-sm max-w-none mb-4 ${
                  theme === 'dark' ? 'prose-invert' : ''
                }`}>
                  <div 
                    dangerouslySetInnerHTML={{ 
                      __html: content.explanation.substring(0, 300) + (content.explanation.length > 300 ? '...' : '') 
                    }} 
                  />
                </div>

                {/* Attachments */}
                {content.attachments.length > 0 && (
                  <div className="mb-4">
                    <p className={`text-sm font-medium mb-2 ${
                      theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Attachments ({content.attachments.length})
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {content.attachments.map((attachment, index) => (
                        <div
                          key={index}
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg ${
                            theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'
                          }`}
                        >
                          {attachment.type === 'image' ? (
                            <Image size={14} className="text-green-600" />
                          ) : (
                            <FileText size={14} className="text-blue-600" />
                          )}
                          <span className={`text-sm ${
                            theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            {attachment.name}
                          </span>
                          <span className={`text-xs ${
                            theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
                          }`}>
                            ({attachment.size})
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className={`text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                  Created: {content.createdAt} • Updated: {content.updatedAt}
                </div>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => handleEdit(content)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => handleDelete(content.id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                      : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                  }`}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Editor Modal */}
      {showEditor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className={`rounded-lg p-6 w-full max-w-6xl max-h-[90vh] overflow-y-auto ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {editingContent ? 'Edit Content' : 'Add New Content'}
              </h3>
              <button
                onClick={() => setShowEditor(false)}
                className={`p-2 rounded-lg transition-colors ${
                  theme === 'dark'
                    ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                }`}
              >
                ×
              </button>
            </div>

            <div className="space-y-6">
              {/* Title and Meta */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                    placeholder="Enter content title..."
                  />
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Category
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  >
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Rich Text Editor */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Content *
                </label>
                <RichTextEditor
                  value={formData.explanation}
                  onChange={(content) => setFormData(prev => ({ ...prev, explanation: content }))}
                  theme={theme}
                  placeholder="Enter content..."
                />
              </div>

              {/* File Attachments */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Attachments
                </label>
                <div className={`border-2 border-dashed rounded-lg p-6 ${
                  theme === 'dark' ? 'border-gray-600' : 'border-gray-300'
                }`}>
                  <div className="text-center">
                    <Upload className={`mx-auto h-12 w-12 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-gray-400'
                    }`} />
                    <div className="mt-4">
                      <label className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-blue-600 hover:text-blue-500">
                          Choose files to upload
                        </span>
                        <input
                          type="file"
                          multiple
                          onChange={handleFileUpload}
                          className="hidden"
                          accept="image/*,.pdf,.doc,.docx,.txt"
                        />
                      </label>
                      <p className={`mt-2 text-sm ${
                        theme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        PNG, JPG, PDF, DOC up to 10MB each
                      </p>
                    </div>
                  </div>
                </div>

                {/* Attachment List */}
                {formData.attachments.length > 0 && (
                  <div className="mt-4 space-y-2">
                    {formData.attachments.map((attachment, index) => (
                      <div
                        key={index}
                        className={`flex items-center justify-between p-3 rounded-lg ${
                          theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          {attachment.type === 'image' ? (
                            <Image size={16} className="text-green-600" />
                          ) : (
                            <FileText size={16} className="text-blue-600" />
                          )}
                          <span className={`text-sm ${
                            theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            {attachment.name}
                          </span>
                          <span className={`text-xs ${
                            theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
                          }`}>
                            ({attachment.size})
                          </span>
                        </div>
                        <button
                          onClick={() => removeAttachment(index)}
                          className={`p-1 rounded transition-colors ${
                            theme === 'dark'
                              ? 'text-red-400 hover:bg-red-900/20'
                              : 'text-red-500 hover:bg-red-50'
                          }`}
                        >
                          <Trash2 size={14} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Status */}
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                >
                  {statuses.map(status => (
                    <option key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowEditor(false)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Save size={16} />
                <span>{editingContent ? 'Update' : 'Create'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentManager;
