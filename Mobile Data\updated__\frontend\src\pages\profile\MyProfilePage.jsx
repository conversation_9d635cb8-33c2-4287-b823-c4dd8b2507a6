import {
  FaCamera,
  FaFacebook,
  FaTwitter,
  FaInstagram,
  FaGlobe,
  FaMapMarkerAlt,
  FaGithub,
  FaLinkedin,
} from "react-icons/fa";
import { Link } from "react-router-dom";
import { Bar, Line } from "react-chartjs-2";
import { Doughnut } from "react-chartjs-2";
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  Title,
  Tooltip,
} from "chart.js/auto";
import { useSelector } from "react-redux";

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

const MyProfilePage = () => {
  const { user } = useSelector((state) => state.auth);

  const data = {
    labels: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    datasets: [
      {
        label: "Progress",
        data: [0, 50, 100, 150, 200],
        fill: false,
        borderColor: "rgb(75, 192, 192)",
        tension: 0.1,
      },
    ],
  };

  const problemData = {
    labels: ["Easy", "Medium", "Hard"],
    datasets: [
      {
        label: "Problems Solved",
        data: [50, 30, 20],
        backgroundColor: ["#4CAF50", "#FFC107", "#F44336"],
        borderColor: ["#388E3C", "#FFA000", "#D32F2F"],
        borderWidth: 2,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: "bottom",
      },
    },
  };

  const errorData = {
    labels: ["Correct Answer", "Compilation Error", "Wrong Answer"],
    datasets: [
      {
        label: "Submission Analysis",
        data: [50, 30, 20],
        backgroundColor: ["#3498DB", "#5DADE2", "#1B4F72"],
        borderColor: ["#2E86C1", "#2874A6", "#154360"],
        borderWidth: 2,
      },
    ],
  };

  const errorOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "bottom",
      },
    },
  };

  const courseData = {
    labels: [
      "Python Course",
      "Full Stack Course",
      "Data Science Course",
      "AI Course",
      "ML Course",
      "SQL Course",
      "Python DSA",
    ],
    datasets: [
      {
        label: "Students Enrolled",
        data: [120, 95, 85, 90, 75, 110, 150],
        backgroundColor: [
          "#3498DB",
          "#00D9FF",
          "#1ABC9C",
          "#9B59B6",
          "#E67E22",
          "#E74C3C",
          "#F39C12",
        ],
        borderColor: [
          "#2980B9",
          "#00A8CC",
          "#16A085",
          "#8E44AD",
          "#D35400",
          "#C0392B",
          "#E67E22",
        ],
        borderWidth: 2,
      },
    ],
  };

  const courseOptions = {
    indexAxis: "y",
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
      },
    },
    scales: {
      x: {
        beginAtZero: true,
      },
    },
  };

  const topicsData = [
    {
      name: "LinkedList Problems",
      easy: 10,
      medium: 5,
      hard: 8,
    },
    {
      name: "Array Manipulation",
      easy: 15,
      medium: 10,
      hard: 6,
    },
    {
      name: "Binary Trees",
      easy: 8,
      medium: 12,
      hard: 5,
    },
    {
      name: "Dynamic Programming",
      easy: 7,
      medium: 14,
      hard: 9,
    },
    {
      name: "Graph Algorithms",
      easy: 5,
      medium: 7,
      hard: 10,
    },
    {
      name: "Searching Algorithms",
      easy: 20,
      medium: 12,
      hard: 6,
    },
    {
      name: "Sorting Algorithms",
      easy: 18,
      medium: 9,
      hard: 4,
    },
    {
      name: "Recursion Problems",
      easy: 14,
      medium: 8,
      hard: 5,
    },
    {
      name: "Greedy Algorithms",
      easy: 12,
      medium: 9,
      hard: 6,
    },
    {
      name: "Backtracking Problems",
      easy: 8,
      medium: 6,
      hard: 7,
    },
  ];

  return (
    <div className="min-h-screen w-full pt-4 pb-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      <div className="max-w-7xl mx-auto flex flex-col gap-6">
        {/* My Profile Card */}
        <div className="relative bg-gradient-to-br from-slate-800/90 via-blue-900/80 to-purple-900/70 backdrop-blur-xl rounded-3xl shadow-2xl p-8 flex flex-col md:flex-row items-center gap-8 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500 group overflow-hidden">
          {/* Animated background */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-cyan-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl transform translate-x-16 -translate-y-16"></div>

          {/* Profile Image & Edit Button */}
          <div className="relative flex flex-col items-center gap-4 z-10">
            <div className="relative w-36 h-36 group/avatar">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-cyan-500 rounded-full p-1 animate-pulse">
                <img
                  src={user ? user.image : "/images/logonew.png"}
                  alt="user"
                  className="rounded-full w-full h-full object-cover bg-slate-800"
                />
              </div>
              <div className="absolute bottom-2 right-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full p-3 shadow-lg cursor-pointer border-2 border-white/20 hover:scale-110 transition-transform duration-300">
                <FaCamera className="text-white text-sm" />
              </div>
            </div>

            <button className="relative px-6 py-3 bg-gradient-to-r from-blue-600 via-purple-600 to-cyan-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold tracking-wide">
              <Link
                to="/profile/edit-profile"
                className="text-white no-underline"
              >
                ✨ Edit Profile
              </Link>
            </button>
          </div>

          {/* Profile Info */}
          <div className="relative flex-1 flex flex-col items-center md:items-start gap-4 z-10 text-white">
            <h2 className="text-4xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-2">
              {user?.name || "Welcome User"}
            </h2>

            <p className="text-blue-200 text-xl font-medium">{user?.email}</p>

            <div className="flex items-center gap-2 mt-1 text-sm">
              <span className="inline-block w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
              <span className="text-slate-300">
                Active Member since Aug 2002
              </span>
            </div>

            {/* Stats */}
            <div className="flex gap-6 mt-4">
              {[
                { label: "Problems Solved", value: 127 },
                { label: "Courses Completed", value: 5 },
                { label: "Success Rate", value: "89%" },
              ].map((item, idx) => (
                <div key={idx} className="text-center">
                  <div className="text-2xl font-bold text-white">
                    {item.value}
                  </div>
                  <div className="text-xs text-slate-400">{item.label}</div>
                </div>
              ))}
            </div>

            {/* Location */}
            <div className="mt-4 flex items-center gap-3 text-sm text-slate-300">
              <FaMapMarkerAlt className="text-blue-400" />
              <span>
                {user?.city}, {user?.country} -{" "}
              </span>
            </div>

            {/* Social Links */}
            <div className="mt-4 flex gap-6 text-xl items-center">
              {user?.socialMedia?.linkedin && (
                <a
                  href={user.socialMedia.linkedin}
                  target="_blank"
                  rel="noreferrer"
                  className="flex flex-col items-center text-blue-600 hover:scale-110 transition-transform"
                >
                  <FaLinkedin />
                  <span className="text-sm text-white font-bold mt-1">LinkedIn</span>
                  <span className="text-xs text-white font-bold break-all text-center">
                    {user.socialMedia.linkedin}
                  </span>
                </a>
              )}
              {user?.socialMedia?.twitter && (
                <a
                  href={user.socialMedia.twitter}
                  target="_blank"
                  rel="noreferrer"
                  className="flex flex-col items-center text-sky-400 hover:scale-110 transition-transform"
                >
                  <FaTwitter />
                  <span className="text-sm text-white font-bold mt-1">Twitter</span>
                  <span className="text-xs text-white font-bold break-all text-center">
                    {user.socialMedia.twitter}
                  </span>
                </a>
              )}
              {user?.socialMedia?.github && (
                <a
                  href={user.socialMedia.github}
                  target="_blank"
                  rel="noreferrer"
                  className="flex flex-col items-center text-gray-700 hover:scale-110 transition-transform"
                >
                  <FaGithub />
                  <span className="text-sm text-white font-bold mt-1">GitHub</span>
                  <span className="text-xs text-white font-bold break-all text-center">
                    {user.socialMedia.github}
                  </span>
                </a>
              )}
              {user?.socials?.website && (
                <a
                  href={user.socials.website}
                  target="_blank"
                  rel="noreferrer"
                  className="flex flex-col items-center text-cyan-500 hover:scale-110 transition-transform"
                >
                  <FaGlobe />
                  <span className="text-sm mt-1">Website</span>
                  <span className="text-xs break-all text-center text-cyan-600">
                    {user.socials.website}
                  </span>
                </a>
              )}
            </div>
          </div>
        </div>

        {/* Activities & Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Progress Line Chart */}
          <div className="relative bg-gradient-to-br from-blue-900/60 via-slate-800/80 to-purple-900/60 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-blue-500/30 hover:border-blue-400/50 transition-all duration-500 group overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-2xl"></div>

            <div className="relative flex items-center gap-3 mb-6 w-full z-10">
              <div className="w-3 h-8 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full group-hover:scale-110 transition-transform duration-300"></div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-200 to-purple-200 bg-clip-text text-transparent tracking-wide">
                📈 Weekly Progress
              </h3>
            </div>
            <div className="relative z-10">
              <Line data={data} />
            </div>
          </div>

          {/* Solved Problem List */}
          <div className="relative bg-gradient-to-br from-green-900/60 via-slate-800/80 to-emerald-900/60 backdrop-blur-xl rounded-3xl shadow-2xl p-8 border border-green-500/30 hover:border-green-400/50 transition-all duration-500 group overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-green-600/5 to-emerald-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-full blur-2xl"></div>

            <div className="relative flex items-center gap-3 mb-6 w-full z-10">
              <div className="w-3 h-8 bg-gradient-to-b from-green-400 to-emerald-500 rounded-full group-hover:scale-110 transition-transform duration-300"></div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-green-200 to-emerald-200 bg-clip-text text-transparent tracking-wide">
                🎯 Solved Problems
              </h3>
            </div>
            <div className="relative flex flex-col gap-3 max-h-80 overflow-y-auto pr-2 z-10 custom-scrollbar">
              {topicsData.map((topic, index) => (
                <div
                  className="flex flex-col md:flex-row md:items-center justify-between bg-slate-900/50 backdrop-blur-sm rounded-xl px-5 py-3 hover:bg-slate-800/60 transition-all duration-300 border border-slate-700/50 hover:border-green-500/30 group/item"
                  key={index}
                >
                  <span className="font-semibold text-white group-hover/item:text-green-200 transition-colors duration-300">
                    {topic.name}
                  </span>
                  <div className="flex gap-4 mt-2 md:mt-0">
                    <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs font-bold rounded-lg border border-green-500/30">
                      Easy: {topic.easy}
                    </span>
                    <span className="px-2 py-1 bg-yellow-500/20 text-yellow-300 text-xs font-bold rounded-lg border border-yellow-500/30">
                      Medium: {topic.medium}
                    </span>
                    <span className="px-2 py-1 bg-red-500/20 text-red-300 text-xs font-bold rounded-lg border border-red-500/30">
                      Hard: {topic.hard}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Doughnut and Bar Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Problem Solved Chart */}
          <div className="relative bg-gradient-to-br from-green-900/70 via-emerald-800/60 to-slate-800/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 flex flex-col items-center border border-green-500/40 hover:border-green-400/60 transition-all duration-500 group overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-green-600/10 to-emerald-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="absolute top-4 right-4 text-green-300/40 text-4xl pointer-events-none select-none group-hover:text-green-300/60 transition-colors duration-300">
              ✨
            </div>
            <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-green-400/20 to-emerald-400/20 rounded-full blur-2xl"></div>

            <div className="relative flex items-center gap-3 mb-6 w-full justify-center z-10">
              <div className="w-4 h-12 bg-gradient-to-b from-green-400 to-emerald-500 rounded-full group-hover:scale-110 transition-transform duration-300"></div>
              <h3 className="text-2xl font-extrabold bg-gradient-to-r from-green-200 to-emerald-200 bg-clip-text text-transparent tracking-wide drop-shadow-lg">
                Problems Solved
              </h3>
            </div>
            <div className="relative w-full flex-1 flex items-center justify-center z-10">
              <Doughnut data={problemData} options={options} />
            </div>
          </div>
          {/* Submission Analysis Chart */}
          <div className="relative bg-gradient-to-br from-blue-900/70 via-cyan-800/60 to-slate-800/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 flex flex-col items-center border border-blue-500/40 hover:border-cyan-400/60 transition-all duration-500 group overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-cyan-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="absolute top-4 right-4 text-blue-300/40 text-4xl pointer-events-none select-none group-hover:text-cyan-300/60 transition-colors duration-300">
              📊
            </div>
            <div className="absolute bottom-0 right-0 w-20 h-20 bg-gradient-to-tl from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl"></div>

            <div className="relative flex items-center gap-3 mb-6 w-full justify-center z-10">
              <div className="w-4 h-12 bg-gradient-to-b from-blue-400 to-cyan-500 rounded-full group-hover:scale-110 transition-transform duration-300"></div>
              <h3 className="text-2xl font-extrabold bg-gradient-to-r from-blue-200 to-cyan-200 bg-clip-text text-transparent tracking-wide drop-shadow-lg">
                Submission Analysis
              </h3>
            </div>
            <div className="relative w-full flex-1 flex items-center justify-center z-10">
              <Doughnut data={errorData} options={errorOptions} />
            </div>
          </div>
          {/* Course Enrollment Analysis Bar Chart */}
          <div className="relative bg-gradient-to-br from-amber-900/70 via-orange-800/60 to-slate-800/80 backdrop-blur-xl rounded-3xl shadow-2xl p-8 flex flex-col items-center border border-amber-500/40 hover:border-orange-400/60 transition-all duration-500 group overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-amber-600/10 to-orange-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="absolute top-4 right-4 text-amber-300/40 text-4xl pointer-events-none select-none group-hover:text-orange-300/60 transition-colors duration-300">
              🎓
            </div>
            <div className="absolute bottom-0 left-0 w-20 h-20 bg-gradient-to-tr from-amber-400/20 to-orange-400/20 rounded-full blur-2xl"></div>

            <div className="relative flex items-center gap-3 mb-6 w-full justify-center z-10">
              <div className="w-4 h-12 bg-gradient-to-b from-amber-400 to-orange-500 rounded-full group-hover:scale-110 transition-transform duration-300"></div>
              <h3 className="text-2xl font-extrabold bg-gradient-to-r from-amber-200 to-orange-200 bg-clip-text text-transparent tracking-wide drop-shadow-lg">
                Course Enrollment
              </h3>
            </div>
            <div className="relative w-full flex-1 flex items-center justify-center z-10">
              <Bar data={courseData} options={courseOptions} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyProfilePage;
