import Docker from "dockerode";
import { v4 as uuidv4 } from "uuid";
import logger from "../../utils/logger.js";

const docker = new Docker();

const MAX_CONTAINERS = 20;
const CONTAINER_IDLE_TIMEOUT = 5 * 60 * 1000;
const QUEUE_WARNING_THRESHOLD = 10;

const languageConfigs = {
  cpp: {
    image: "code-runner-cpp:latest",
    cmd: ["tail", "-f", "/dev/null"],
  },
  python: {
    image: "code-runner-python:latest",
    cmd: ["tail", "-f", "/dev/null"],
  },
  java: {
    image: "code-runner-java:latest",
    cmd: ["tail", "-f", "/dev/null"],
  },
  javascript: {
    image: "code-runner-js:latest",
    cmd: ["tail", "-f", "/dev/null"],
  },
};

class ContainerPool {
  constructor() {
    this.pool = new Map();
    this.activeCount = 0;
    this.queue = [];

    for (const lang of Object.keys(languageConfigs)) {
      this.pool.set(lang, []);
    }

    this.startCleanupScheduler();
  }

  async getContainer(language) {
    const containerObj = await this.tryGetAvailableContainer(language);
    if (containerObj) {
      logger.debug(`Reusing container for language: ${language}`);
      return containerObj;
    }

    if (this.activeCount >= MAX_CONTAINERS) {
      logger.warn(`Max container limit reached. Queuing request for ${language}`);
      if (this.queue.length >= QUEUE_WARNING_THRESHOLD) {
        logger.warn(`Queue length high (${this.queue.length}). Consider scaling.`);
      }

      return new Promise((resolve, reject) => {
        this.queue.push({ language, resolve, reject });
      });
    }

    return this.createNewContainer(language);
  }

  async tryGetAvailableContainer(language) {
    const langPool = this.pool.get(language) || [];
    while (langPool.length > 0) {
      const containerObj = langPool.pop();
      try {
        const inspect = await containerObj.container.inspect();
        if (inspect?.State?.Running && !inspect.State.Paused) {
          const exec = await containerObj.container.exec({
            Cmd: ["echo", "ping"],
            AttachStdout: true,
            AttachStderr: true,
          });
          const stream = await exec.start();
          stream.resume()
          await new Promise((res) => setTimeout(res, 100)); 
          containerObj.lastUsed = Date.now();
          return containerObj;
        } else {
          await this.forceRemoveContainer(containerObj);
        }
      } catch (err) {
        logger.warn(`${err.message}`);
        await this.forceRemoveContainer(containerObj);
      }
    }
    return null;
  }

  async createNewContainer(language) {
    const config = languageConfigs[language];
    if (!config) throw new Error(`Unsupported language ${language}`);

    try {
      await docker.getImage(config.image).inspect();
    } catch {
      throw new Error(`Docker image not found ${config.image}`);
    }

    const container = await docker.createContainer({
      Image: config.image,
      Tty: false,
      Cmd: config.cmd,
      HostConfig: {
        AutoRemove: false,
        Memory: 256 * 1024 * 1024,
        NanoCpus: 500000000,
        Tmpfs: {
          "/app": "rw,noexec,nosuid,size=64m",
        },
      },
    });

    await container.start();
    await new Promise((res) => setTimeout(res, 100));
    this.activeCount++;
    logger.info(`New container created for ${language}. Active count: ${this.activeCount}`);

    return {
      id: uuidv4(),
      container,
      language,
      lastUsed: Date.now(),
    };
  }

  async releaseContainer(containerObj) {
    try {
      const inspect = await containerObj.container.inspect();
      if (inspect?.State?.Running && !inspect.State.Paused) {
        containerObj.lastUsed = Date.now();
        this.pool.get(containerObj.language).push(containerObj);
        logger.debug(`Released container back to pool for ${containerObj.language}`);
      } else {
        await this.forceRemoveContainer(containerObj);
      }
    } catch (err) {
      logger.warn(`Release failed ${err.message}`);
      await this.forceRemoveContainer(containerObj);
    }

    this.processQueue();
  }

  async processQueue() {
    if (this.queue.length === 0) return;

    const remainingQueue = [];
    for (const job of this.queue) {
      if (this.activeCount < MAX_CONTAINERS) {
        try {
          const containerObj = await this.tryGetAvailableContainer(job.language);
          if (containerObj) {
            job.resolve(containerObj);
          } else {
            const newContainer = await this.createNewContainer(job.language);
            job.resolve(newContainer);
          }
        } catch (err) {
          logger.error(`Queue processing error ${err.message}`);
          job.reject(err);
        }
      } else {
        remainingQueue.push(job);
      }
    }
    this.queue = remainingQueue;
  }

  async cleanupIdleContainers() {
    const now = Date.now();

    for (const [language, containers] of this.pool.entries()) {
      const stillAlive = [];

      for (const containerObj of containers) {
        const idleTime = now - containerObj.lastUsed;

        if (idleTime > CONTAINER_IDLE_TIMEOUT) {
          try {
            await containerObj.container.stop();
            await this.forceRemoveContainer(containerObj);
            logger.info(`Idle container cleaned up ${containerObj.id}`);
          } catch (err) {
            logger.warn(`Cleanup failed for container ${containerObj.id} ${err.message}`);
          }
        } else {
          stillAlive.push(containerObj);
        }
      }

      this.pool.set(language, stillAlive);
    }
  }

  async forceRemoveContainer(containerObj) {
    try {
      await containerObj.container.remove({ force: true });
      this.activeCount = Math.max(this.activeCount - 1, 0);
      logger.debug(`Force removed container ${containerObj.id}`);
    } catch (err) {
      logger.error(`Force remove failed for ${containerObj.id} ${err.message}`);
    }
  }

  startCleanupScheduler() {
    setInterval(() => this.cleanupIdleContainers(), 60 * 1000);
  }
}

const containerPool = new ContainerPool();
export default containerPool;
