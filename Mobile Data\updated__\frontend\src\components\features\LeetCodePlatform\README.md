# UpCoding Platform

A comprehensive coding platform UI built with React, TailwindCSS, and Monaco Editor, featuring a modern LeetCode-style interface.

## 🚀 Features

### 📱 Layout & Design
- **Two-section layout**: 40% top for problem info, 60% bottom for code editor
- **Responsive design** with mobile and desktop support
- **Modern UI** with rounded cards, shadowed containers, and consistent spacing
- **Dark/Light mode toggle** for better user experience
- **Fullscreen mode** for distraction-free coding

### 🧑‍💻 Code Editor
- **Monaco Editor** integration with syntax highlighting
- **Multi-language support**: JavaScript, Python, Java, C++
- **Auto-indentation** and code formatting
- **Line numbers** and syntax highlighting
- **Custom input** for test cases
- **Resizable console** with adjustable height
- **Code actions**: Copy, Download, Reset, Format

### 📝 Problem Management
- **Tabbed interface** with 4 main sections:
  - **Problem Description**: Detailed problem statement with examples
  - **Submissions**: History of code submissions with status tracking
  - **Editorial**: Step-by-step solution explanations
  - **Discussion**: Community forum with threaded comments

### ⏱️ Additional Features
- **Timer** for contest mode with start/pause functionality
- **Bookmark system** to save favorite problems
- **Progress tracking** with local storage
- **Figma design preview** toggle for UI mockups
- **Test case execution** with output console
- **Submission tracking** with runtime and memory stats

## 🎯 Accessing the Platform

The UpCoding platform is accessible at: `http://localhost:5174/labs/687e295faf9115150c209f2e`

## 🏗️ Component Structure

```
LeetCodePlatform/
├── LeetCodePlatform.jsx          # Main platform component
├── ProblemDescription.jsx        # Problem statement and examples
├── CodeEditorSection.jsx         # Monaco editor with controls
├── SubmissionsPanel.jsx          # Submission history and details
├── EditorialPanel.jsx            # Solution explanations
├── DiscussionPanel.jsx           # Community discussion forum
└── README.md                     # This documentation
```

## 🎨 UI Components

### Header Section
- Platform title and branding
- Timer with start/pause controls
- Bookmark toggle button
- Figma design preview toggle
- Dark/Light mode switch
- Fullscreen mode toggle

### Problem Info Panel (Top 40%)
- **Description Tab**: Problem statement, examples, constraints
- **Submissions Tab**: Submission history with filtering
- **Editorial Tab**: Multiple solution approaches with complexity analysis
- **Discussion Tab**: Community forum with voting and replies

### Code Editor Panel (Bottom 60%)
- **Monaco Editor**: Full-featured code editor
- **Language Selector**: Support for multiple programming languages
- **Action Buttons**: Run, Submit, Reset code
- **Console Output**: Test results and execution feedback
- **Custom Input**: Add custom test cases

## 🔧 Technical Implementation

### Dependencies
- **React 19.0.0**: Core framework
- **@monaco-editor/react**: Code editor component
- **framer-motion**: Smooth animations
- **lucide-react**: Modern icon library
- **tailwindcss**: Utility-first CSS framework

### Key Features Implementation

#### Responsive Layout
```jsx
// Two-section layout with proper height distribution
<div className="flex flex-col h-[calc(100vh-73px)]">
  <div className="h-2/5"> {/* Problem Info - 40% */}
    {/* Tabbed content */}
  </div>
  <div className="flex-1"> {/* Code Editor - 60% */}
    {/* Monaco editor and console */}
  </div>
</div>
```

#### Monaco Editor Integration
```jsx
<Editor
  height="100%"
  language={selectedLanguage}
  value={code}
  onChange={handleEditorChange}
  theme={isDarkMode ? 'vs-dark' : 'light'}
  options={{
    minimap: { enabled: false },
    fontSize: 14,
    wordWrap: 'on',
    // ... other options
  }}
/>
```

#### Theme System
```jsx
// Dynamic theme switching
const [isDarkMode, setIsDarkMode] = useState(true);

// Conditional styling throughout components
className={`${isDarkMode ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'}`}
```

## 🎯 Problem Data Structure

```javascript
const problemData = {
  id: 1,
  title: "Two Sum",
  difficulty: "Easy", // Easy, Medium, Hard
  description: "Problem statement...",
  examples: [
    {
      input: "nums = [2,7,11,15], target = 9",
      output: "[0,1]",
      explanation: "Because nums[0] + nums[1] = 2 + 7 = 9"
    }
  ],
  constraints: ["2 ≤ nums.length ≤ 10⁴"],
  starterCode: {
    javascript: "// starter code",
    python: "# starter code",
    java: "// starter code",
    cpp: "// starter code"
  }
};
```

## 🚀 Usage Examples

### Running Code
1. Select your preferred programming language
2. Write your solution in the Monaco editor
3. Click "Run" to execute with sample test cases
4. View results in the console output

### Submitting Solutions
1. Complete your solution
2. Click "Submit" to run against all test cases
3. View submission results with runtime/memory stats
4. Check submission history in the Submissions tab

### Exploring Solutions
1. Navigate to the Editorial tab
2. Explore different solution approaches
3. Compare time/space complexity
4. View implementation in multiple languages

### Community Interaction
1. Visit the Discussion tab
2. Read community solutions and explanations
3. Upvote helpful posts
4. Add your own comments and solutions

## 🎨 Styling Guidelines

### Color Scheme
- **Primary**: Blue (#3B82F6)
- **Success**: Green (#10B981)
- **Warning**: Yellow (#F59E0B)
- **Error**: Red (#EF4444)
- **Dark Mode**: Gray-900 background, Gray-100 text
- **Light Mode**: Gray-50 background, Gray-900 text

### Typography
- **Headings**: Inter font family, bold weights
- **Body**: Inter font family, normal weights
- **Code**: Monaco, Consolas, monospace fonts

### Spacing
- **Grid**: 8px base unit (Tailwind's spacing scale)
- **Padding**: Consistent 12px, 16px, 24px
- **Margins**: 8px, 16px, 24px for component spacing

## 🔮 Future Enhancements

- [ ] **Real-time collaboration** for pair programming
- [ ] **Video explanations** embedded in editorial
- [ ] **Code comparison** tool for different solutions
- [ ] **Performance analytics** with detailed metrics
- [ ] **Custom problem creation** for educators
- [ ] **Contest mode** with leaderboards
- [ ] **AI-powered hints** and code suggestions
- [ ] **Integration with GitHub** for solution sharing

## 📱 Mobile Responsiveness

The platform is fully responsive with:
- **Collapsible sections** on mobile devices
- **Touch-friendly controls** for mobile interaction
- **Optimized layout** for tablets and phones
- **Swipe gestures** for tab navigation (future enhancement)

## 🎯 Accessibility

- **Keyboard navigation** support
- **Screen reader** compatibility
- **High contrast** mode support
- **Focus indicators** for interactive elements
- **ARIA labels** for complex components

---

Built with ❤️ by UpCoding using React, TailwindCSS, and Monaco Editor
