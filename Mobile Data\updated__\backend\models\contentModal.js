import mongoose from "mongoose";

const contentSchema = new mongoose.Schema(
  {
    title: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    slug: { type: String, required: true, unique: true, index: true },

    body: { type: String, required: true },
    code: { type: String },  

    checklist: [
      {
        text: { type: String, required: true },
        isChecked: { type: Boolean, default: false },
      },
    ],

    chapterId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Chapter",
      required: true,
    },
    labId: { type: mongoose.Schema.Types.ObjectId, ref: "Lab", required: true },
    labsectionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LabSection",
      required: true,
    },

    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },

    tags: [{ type: String, index: true }],
    category: {
      type: String,
      enum: ["article", "guide", "tutorial", "blog"],
      default: "article",
    },

    metaTitle: { type: String },
    metaDescription: { type: String },
    keywords: [{ type: String }],

    thumbnail: {
      public_id: { type: String, required: true },
      secure_url: { type: String, required: true },
    },

    attachments: [
      {
        public_id: { type: String, required: true },
        secure_url: { type: String, required: true },
      },
    ],
    status: {
      type: String,
      enum: ["draft", "published", "archived"],
      default: "draft",
    },
    visibility: {
      type: String,
      enum: ["public", "private", "restricted"],
      default: "public",
    },
    rolesAllowed: [
      { type: String, enum: ["student", "teacher", "admin"], default: [] },
    ],

    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    bookmarks: { type: Number, default: 0 },
    commentsCount: { type: Number, default: 0 },

    isPremium: { type: Boolean, default: false },

    version: { type: Number, default: 1 },

    isActive: { type: Boolean, default: true },
    isDeleted: { type: Boolean, default: false },

    createdBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
    updatedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  },
  { timestamps: true }
);

contentSchema.index({
  title: "text",
  body: "text",
  tags: "text",
  keywords: "text",
});

const Content = mongoose.model("Content", contentSchema);
export default Content;
