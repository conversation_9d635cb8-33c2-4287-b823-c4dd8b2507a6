import { Spinner } from "../../components/ui";
import toast from "react-hot-toast";
import { useState } from "react";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import {
  FaEnvelope,
  FaShieldAlt,
  FaArrowRight,
  FaArrowLeft,
  FaCheckCircle,
  FaPaperPlane,
  FaLock
} from "react-icons/fa";
import axiosInstance from "../../utils/axiosInstance";
import "./two-factor.css";

const TwoFactorEmail = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4,
      },
    },
  };

  const validateFields = () => {
    if (!email) {
      toast.error("Please enter your email address");
      return false;
    }
    if (!/\S+@\S+\.\S+/.test(email)) {
      toast.error("Please enter a valid email address");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateFields()) return;
    setLoading(true);
    try {
      const { data } = await axiosInstance.post(
        "http://localhost:8000/api/v1/auth/two-fa-token",
        { email },
        { withCredentials: true }
      );
      if (data.success) {
        toast.success(data.message || "Verification email sent successfully!");
        setEmailSent(true);
      } else {
        toast.error(data.error || "Failed to send verification email");
      }
    } catch (error) {
      toast.error(error.response?.data?.message || "Something went wrong");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-blue-900 flex items-center justify-center px-4 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-10 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
        <div className="absolute top-10 right-10 w-32 h-32 bg-purple-500/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-cyan-500/10 rounded-full blur-2xl"></div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative z-10 w-full max-w-md"
      >
        {/* Main Card */}
        <div className="bg-gradient-to-br from-slate-800/95 via-slate-700/90 to-slate-800/95 backdrop-blur-xl rounded-2xl shadow-2xl p-6 border border-slate-600/50 hover:border-blue-400/50 transition-all duration-500">

          {/* Header Section */}
          <motion.div variants={itemVariants} className="text-center mb-5">
            <div className="flex items-center justify-center gap-2 mb-2">
              <FaEnvelope className="text-2xl text-blue-400" />
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-cyan-200 bg-clip-text text-transparent">
                Email Verification
              </h1>
            </div>
            <p className="text-slate-300 text-sm">
              Enter your email to receive 2FA setup link
            </p>
          </motion.div>

          {!emailSent ? (
            <>
              {/* Email Input Form */}
              <motion.div variants={itemVariants} className="mb-5">
                <div className="bg-gradient-to-r from-blue-900/40 to-purple-900/40 rounded-xl p-4 border border-blue-500/20 mb-4">
                  <p className="text-slate-200 text-center text-sm">
                    We'll send you a secure link to enable 2FA on your account.
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="relative">
                    <FaEnvelope className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                    <input
                      type="email"
                      placeholder="Enter your email address"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 focus:border-blue-400/50 focus:ring-2 focus:ring-blue-400/20 focus:outline-none transition-all duration-300"
                      disabled={loading}
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={loading || !email}
                    className="w-full px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 font-semibold flex items-center justify-center gap-2 group disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                  >
                    {loading ? (
                      <>
                        <Spinner />
                        <span>Sending...</span>
                      </>
                    ) : (
                      <>
                        <FaPaperPlane className="text-sm" />
                        <span>Send Verification Email</span>
                        <FaArrowRight className="text-xs group-hover:translate-x-1 transition-transform duration-300" />
                      </>
                    )}
                  </button>
                </form>
              </motion.div>
            </>
          ) : (
            /* Success State */
            <motion.div variants={itemVariants} className="mb-5 text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <FaCheckCircle className="text-white text-2xl" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Email Sent!</h3>
              <div className="bg-gradient-to-r from-green-900/40 to-emerald-900/40 rounded-xl p-4 border border-green-500/20 mb-4">
                <p className="text-slate-200 text-sm">
                  We've sent a verification link to <strong className="text-green-300">{email}</strong>
                </p>
              </div>
              <p className="text-slate-400 text-sm">
                Check your inbox and click the link to complete 2FA setup.
              </p>
            </motion.div>
          )}

          {/* Security Features */}
          <motion.div variants={itemVariants} className="mb-4">
            <div className="grid grid-cols-3 gap-2">
              <div className="text-center p-2 bg-slate-900/30 rounded-lg border border-slate-600/30">
                <FaShieldAlt className="text-green-400 text-sm mx-auto mb-1" />
                <p className="text-xs text-slate-300">Secure</p>
              </div>
              <div className="text-center p-2 bg-slate-900/30 rounded-lg border border-slate-600/30">
                <FaLock className="text-blue-400 text-sm mx-auto mb-1" />
                <p className="text-xs text-slate-300">Encrypted</p>
              </div>
              <div className="text-center p-2 bg-slate-900/30 rounded-lg border border-slate-600/30">
                <FaEnvelope className="text-purple-400 text-sm mx-auto mb-1" />
                <p className="text-xs text-slate-300">Verified</p>
              </div>
            </div>
          </motion.div>

          {/* Back Button */}
          <motion.div variants={itemVariants}>
            <Link to="/two-fa" className="block">
              <button className="w-full px-4 py-2 bg-slate-700/50 hover:bg-slate-600/50 text-slate-300 hover:text-white rounded-xl border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300 font-medium text-sm flex items-center justify-center gap-2">
                <FaArrowLeft className="text-xs" />
                <span>Back to 2FA Setup</span>
              </button>
            </Link>
          </motion.div>

          {/* Footer Note */}
          <motion.div variants={itemVariants} className="mt-4 pt-3 border-t border-slate-600/30 text-center">
            <p className="text-slate-400 text-xs">
              Didn't receive the email? Check your spam folder
            </p>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default TwoFactorEmail;
