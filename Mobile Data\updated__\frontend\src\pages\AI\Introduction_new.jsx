import React, { useState } from "react";
import { motion } from "framer-motion";

const Introduction = ({ showPremiumOverlay, onBackToCourse }) => {
  const [showMore, setShowMore] = useState(false);

  const toggleReadMore = () => {
    setShowMore(!showMore);
  };

  // CSS styles for animations
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.95); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    .animate-fadeIn {
      animation: fadeIn 0.6s ease-out forwards;
    }
    
    .animate-scaleIn {
      animation: scaleIn 0.5s ease-out forwards;
    }
    
    .hover-scale {
      transition: transform 0.3s ease;
    }
    
    .hover-scale:hover {
      transform: scale(1.05);
    }
  `;

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="py-8"
    >
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        {/* Back Button */}
        <div className="mb-8">
          <button
            onClick={() => {
              if (onBackToCourse) {
                onBackToCourse();
              }
            }}
            className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105"
          >
            <span className="text-xl">←</span>
            <span>Back to Course</span>
          </button>
        </div>

        {/* Header Section */}
        <div className="text-center mb-16 animate-fadeIn">
          <div className="inline-block px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg">
            🤖 AI Fundamentals
          </div>
          
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Master{" "}
            <span className="bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
              Artificial Intelligence
            </span>
          </h2>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            Build intelligent systems with cutting-edge AI technologies and machine learning algorithms
          </p>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Left Column */}
          <div className="space-y-8 animate-fadeIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🎯</span>
                Course Overview
              </h3>
              <p className="text-white/80 leading-relaxed">
                In this course, we will guide you through the essential concepts of artificial intelligence (AI) programming using video-based lessons. Gaining a solid grasp of core AI principles is crucial for mastering AI programming and excelling in related interviews.
              </p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">💡</span>
                Why Artificial Intelligence?
              </h3>
              <p className="text-white/80 leading-relaxed">
                Every AI system has its unique aspects, and more complex models often deviate from standard coding approaches. However, in AI programming interviews, you're not expected to know every detail. Instead, you'll focus on fundamental principles and problem-solving strategies.
              </p>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🚀</span>
                What You'll Learn
              </h3>
              <p className="text-white/80 leading-relaxed">
                This course emphasizes understanding AI algorithms, neural networks, and machine learning concepts that form the foundation of modern artificial intelligence systems.
              </p>
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-8 animate-fadeIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">📊</span>
                Key Topics
              </h3>
              <ul className="space-y-3 text-white/80">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Neural Networks & Deep Learning
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Computer Vision
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Natural Language Processing
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  Reinforcement Learning
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                  AI Ethics & Safety
                </li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🔧</span>
                Tools & Frameworks
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <span className="text-2xl mb-2 block">🐍</span>
                  <span className="text-white/80 text-sm">Python</span>
                </div>
                <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <span className="text-2xl mb-2 block">🧠</span>
                  <span className="text-white/80 text-sm">TensorFlow</span>
                </div>
                <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <span className="text-2xl mb-2 block">⚡</span>
                  <span className="text-white/80 text-sm">PyTorch</span>
                </div>
                <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                  <span className="text-2xl mb-2 block">🤖</span>
                  <span className="text-white/80 text-sm">OpenAI</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Content Section */}
        {showMore && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16 animate-scaleIn">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">🏆</span>
                Advanced AI Concepts
              </h3>
              <p className="text-white/80 leading-relaxed mb-4">
                Dive deep into cutting-edge AI technologies including transformer architectures, attention mechanisms, and generative models that power modern AI systems like GPT and DALL-E.
              </p>
              <ul className="space-y-2 text-white/80">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-indigo-400 rounded-full mr-3"></span>
                  Transformer Architecture
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-indigo-400 rounded-full mr-3"></span>
                  Generative AI Models
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-indigo-400 rounded-full mr-3"></span>
                  Large Language Models
                </li>
              </ul>
            </div>

            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <span className="mr-3 text-3xl">💼</span>
                Real-World Applications
              </h3>
              <p className="text-white/80 leading-relaxed">
                Learn how AI is transforming industries from healthcare and finance to autonomous vehicles and creative arts. Build practical projects that demonstrate your understanding of AI principles and their applications.
              </p>
            </div>
          </div>
        )}

        {/* Read More Button */}
        <div className="text-center mb-12">
          <button
            onClick={toggleReadMore}
            className="bg-white/10 backdrop-blur-lg border border-white/20 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:bg-white/20"
          >
            {showMore ? "Show Less" : "Read More"}
          </button>
        </div>

        {/* Call to Action */}
        <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-12 animate-scaleIn">
          <div className="text-center">
            <h3 className="text-3xl font-bold text-white mb-4">
              Ready to Master AI?
            </h3>
            <p className="text-white/80 text-lg mb-6 max-w-2xl mx-auto">
              Join thousands of students who have successfully launched their AI careers with our comprehensive program.
            </p>
            <button
              onClick={showPremiumOverlay}
              className="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl hover-scale"
            >
              Get Premium Access →
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default Introduction;
