import express from "express";
import Task from "../models/Task.js";

const router = express.Router();

// Get all tasks
router.get("/", async (req, res) => {
  try {
    const { page = 1, limit = 3 } = req.query;
    const skip = (page - 1) * limit;  // Calculate the number of tasks to skip based on the page number

    const tasks = await Task.find().skip(skip).limit(limit);  // Paginate tasks based on skip and limit
    const totalTasks = await Task.countDocuments();  // Get the total number of tasks in the database
    const totalPages = Math.ceil(totalTasks / limit);  // Calculate the total number of pages

    res.json({
      tasks,
      totalPages,  // Send the total pages in the response
    });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Add new task
router.post("/", async (req, res) => {
  try {
    
    const { text } = req.body;
    console.log("task recieved")
    console.log(text)
    const newTask = new Task({ text });
    console.log(newTask)
    await newTask.save();
    res.json(newTask);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
});


// Update task completion status
router.put("/:id", async (req, res) => {
  console.log("req")
  try {
    const task = await Task.findById(req.params.id);
    task.completed = !task.completed;
    await task.save();
    res.json(task);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
});

// Delete task by ID
router.delete('/:id', async (req, res) => {
  console.log("req recieved for delete")
  try {
    const taskId = req.params.id;
    if (!taskId) {
      return res.status(400).json({ error: 'Task ID is required' });
    }
    const result = await Task.findByIdAndDelete(taskId);
    if (!result) {
      return res.status(404).json({ error: 'Task not found' });
    }
    res.json({ message: 'Task deleted successfully' });
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

export default router;