import express from "express";
import { isAuthenticated } from "../middleware/auth.js";
import {
    bulkUploadTestCases,
  createTestCase,
  getAllTestCases,
  getVisibleTestCases,
} from "../controllers/testCases.js";

const testCasesRouter = express.Router();

testCasesRouter.post("/create", isAuthenticated, createTestCase);

testCasesRouter.post("/bulk-upload/:problemId", isAuthenticated, bulkUploadTestCases);

testCasesRouter.get("/visible-testcases/:problemId", isAuthenticated, getVisibleTestCases);

testCasesRouter.get("/all-testcases/:problemId", isAuthenticated, getAllTestCases);




export default testCasesRouter;
