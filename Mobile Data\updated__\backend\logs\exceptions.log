[2025-07-10 03:55:53] ERROR: uncaughtException: s is not defined
ReferenceError: s is not defined
    at file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/routes/testCases.js:12:66
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-10 03:57:08] ERROR: uncaughtException: getAll is not defined
ReferenceError: getAll is not defined
    at file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/routes/testCases.js:17:56
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-10 04:04:20] ERROR: uncaughtException: run is not defined
ReferenceError: run is not defined
    at file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/routes/problem.js:18:53
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-17 15:01:14] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:01:29] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:01:42] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:02:49] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:03:19] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:03:41] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:07:06] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:08:09] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:09:29] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-17 15:42:38] ERROR: uncaughtException: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
UpstashError: WRONGTYPE Operation against a key holding the wrong kind of value, command was: ["zrange","jobs:queue",0,0]
    at HttpClient.request (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:180:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async ZRangeCommand.exec (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/node_modules/@upstash/redis/chunk-AIBLSL5D.mjs:342:31)
    at async JobQueue.fetchNextJob (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:71:18)
    at async JobQueue.processLoop (file:///C:/Users/<USER>/Music/upcoding/codexus-lab/backend/execution-engine/execution/jobQueue.js:42:21)
[2025-07-28 13:24:31] ERROR: uncaughtException: CatchAsyncError is not defined
ReferenceError: CatchAsyncError is not defined
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/controllers/chapters.js:14:30
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-28 13:24:46] ERROR: uncaughtException: CatchAsyncError is not defined
ReferenceError: CatchAsyncError is not defined
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/controllers/chapters.js:14:30
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-28 13:26:15] ERROR: uncaughtException: CatchAsyncError is not defined
ReferenceError: CatchAsyncError is not defined
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/controllers/chapters.js:14:30
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-28 13:26:31] ERROR: uncaughtException: CatchAsyncError is not defined
ReferenceError: CatchAsyncError is not defined
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/controllers/chapters.js:14:30
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-28 13:27:29] ERROR: uncaughtException: CatchAsyncError is not defined
ReferenceError: CatchAsyncError is not defined
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/controllers/chapters.js:14:30
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-28 13:27:45] ERROR: uncaughtException: CatchAsyncError is not defined
ReferenceError: CatchAsyncError is not defined
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/controllers/chapters.js:14:30
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-28 13:28:18] ERROR: uncaughtException: CatchAsyncError is not defined
ReferenceError: CatchAsyncError is not defined
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/controllers/chapters.js:14:30
    at ModuleJob.run (node:internal/modules/esm/module_job:271:25)
    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:578:26)
    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)
[2025-07-28 22:13:33] ERROR: uncaughtException: The "path" argument must be of type string or an instance of Buffer or URL. Received [Object: null prototype]
TypeError [ERR_INVALID_ARG_TYPE]: The "path" argument must be of type string or an instance of Buffer or URL. Received [Object: null prototype]
    at Module.unlink (node:fs:1944:18)
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/utils/uploadOnCloudinary.js:16:8
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
[2025-07-29 15:37:02] ERROR: uncaughtException: The "path" argument must be of type string or an instance of Buffer or URL. Received [Object: null prototype] {}
TypeError [ERR_INVALID_ARG_TYPE]: The "path" argument must be of type string or an instance of Buffer or URL. Received [Object: null prototype] {}
    at Module.unlink (node:fs:1944:18)
    at file:///C:/Users/<USER>/OneDrive/Desktop/upcoding/codexus-lab/backend/utils/uploadOnCloudinary.js:16:8
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
