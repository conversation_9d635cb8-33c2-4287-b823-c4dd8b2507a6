# System Design Module

This module contains all components and logic related to the System Design interview questions page.

## 📁 Folder Structure

```
src/pages/system-design/
├── SystemDesign.jsx           # Main component
├── system-design.css          # Legacy CSS (can be removed)
├── README.md                  # This documentation
├── components/                # Reusable UI components
│   ├── index.js              # Barrel exports
│   ├── SystemDesignHeader.jsx # Header with navigation
│   ├── SystemDesignHero.jsx   # Hero section with title and logo
│   ├── NavigationButtons.jsx  # Navigation cards
│   ├── QuestionCard.jsx       # Individual question card
│   ├── DifficultySection.jsx  # Section grouped by difficulty
│   └── InterviewChecklist.jsx # Expandable FAQ section
└── hooks/                     # Custom hooks
    └── useSystemDesignData.js # Data management hook
```

## 🧩 Components

### SystemDesignHeader
- Fixed header with gradient background
- Logo and navigation
- Matches home page theme

### SystemDesignHero
- Hero section with animated title
- System design logo with hover effects
- Gradient backgrounds

### NavigationButtons
- Interactive navigation cards
- Blue and purple gradient themes
- Hover animations

### QuestionCard
- Individual question display
- Tags and key points
- Hover effects and animations

### DifficultySection
- Groups questions by difficulty level
- Color-coded (Green/Orange/Red)
- Contains multiple QuestionCards

### InterviewChecklist
- Expandable FAQ-style component
- Smooth accordion animations
- 15 common interview questions

## 🎣 Hooks

### useSystemDesignData
- Centralized data management
- Returns systemDesignQuestions and interviewChecklist
- Easy to maintain and update

## 🎨 Design Features

- **Color Theme**: Matches home page blue-purple gradient
- **Animations**: Framer Motion for smooth interactions
- **Responsive**: Works on all screen sizes
- **Modern**: Glassmorphism effects and shadows
- **Accessible**: Proper contrast and focus states

## 🚀 Usage

```jsx
import { SystemDesign } from './pages/system-design/SystemDesign';

// Or import individual components
import { 
  SystemDesignHeader, 
  QuestionCard 
} from './pages/system-design/components';
```

## 📱 Responsive Design

- Mobile-first approach
- Flexible grid layouts
- Touch-friendly interactions
- Optimized for all devices
