import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaPlus, FaChartBar, FaComments, FaTimes } from 'react-icons/fa';
import ReviewForm from './ReviewForm';
import ReviewList from './ReviewList';
import ReviewStats from './ReviewStats';
import reviewService from '../../services/reviewService';

const ReviewManager = ({ 
  courseId, 
  courseName, 
  showWriteReview = true,
  showStats = true,
  maxReviewsToShow = null 
}) => {
  const [reviews, setReviews] = useState([]);
  const [stats, setStats] = useState({});
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [activeTab, setActiveTab] = useState('reviews');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadReviews();
  }, [courseId]);

  const loadReviews = () => {
    setLoading(true);
    try {
      const courseReviews = reviewService.getCourseReviews(courseId, true);
      const courseStats = reviewService.getCourseStats(courseId);
      
      setReviews(maxReviewsToShow ? courseReviews.slice(0, maxReviewsToShow) : courseReviews);
      setStats(courseStats);
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReviewSubmitted = (newReview) => {
    // Immediately add the new review to the list for instant feedback
    setReviews(prevReviews => [newReview, ...prevReviews]);

    // Update stats
    const updatedStats = reviewService.getCourseStats(courseId);
    setStats(updatedStats);

    // Close the form
    setShowReviewForm(false);

    // Refresh all reviews to ensure consistency
    setTimeout(() => {
      loadReviews();
    }, 100);
  };

  const tabs = [
    { id: 'reviews', label: 'Reviews', icon: FaComments, count: stats.totalReviews },
    ...(showStats ? [{ id: 'stats', label: 'Statistics', icon: FaChartBar }] : [])
  ];

  if (loading) {
    return (
      <div className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-600 p-8 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
        <p className="text-gray-300">Loading reviews...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Course Reviews</h2>
          <p className="text-gray-300 mt-1">
            See what students are saying about {courseName}
          </p>
        </div>
        
        {showWriteReview && !showReviewForm && (
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowReviewForm(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium flex items-center space-x-2 hover:bg-blue-700 transition-colors"
          >
            <FaPlus />
            <span>Write Review</span>
          </motion.button>
        )}
      </div>

      {/* Review Form Modal */}
      <AnimatePresence>
        {showReviewForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowReviewForm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <ReviewForm
                courseId={courseId}
                courseName={courseName}
                onReviewSubmitted={handleReviewSubmitted}
                onClose={() => setShowReviewForm(false)}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Tabs */}
      {tabs.length > 1 && (
        <div className="border-b border-gray-600">
          <nav className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;

              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 transition-colors ${
                    isActive
                      ? 'border-blue-400 text-blue-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-500'
                  }`}
                >
                  <Icon />
                  <span>{tab.label}</span>
                  {tab.count !== undefined && (
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      isActive ? 'bg-blue-900/50 text-blue-300' : 'bg-gray-700 text-gray-300'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      )}

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        {activeTab === 'reviews' && (
          <motion.div
            key="reviews"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <ReviewList reviews={reviews} />
            
            {maxReviewsToShow && reviews.length >= maxReviewsToShow && (
              <div className="text-center mt-6">
                <button
                  onClick={() => setMaxReviewsToShow(null)}
                  className="px-4 py-2 text-blue-400 hover:text-blue-300 font-medium"
                >
                  View All Reviews ({stats.totalReviews})
                </button>
              </div>
            )}
          </motion.div>
        )}

        {activeTab === 'stats' && showStats && (
          <motion.div
            key="stats"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <ReviewStats stats={stats} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ReviewManager;
