import React, { useState } from 'react';
import AILayout from './components/AILayout';
import ArtificialIntelligence from './ArtificialIntelligence';
import InternshipApply from "../../components/InternshipApply";

const AICourseWrapper = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <AILayout isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar}>
      <ArtificialIntelligence />
    </AILayout>
  );
};

export default AICourseWrapper;
