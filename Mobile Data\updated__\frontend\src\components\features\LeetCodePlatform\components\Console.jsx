import React from 'react';
import { motion } from 'framer-motion';
import { Terminal, ChevronUp, ChevronDown, RotateCcw, EyeOff, X } from 'lucide-react';

const Console = ({
  output,
  consoleHeight,
  onClearOutput,
  onHeightChange,
  onHide,
  onClose,
  isDarkMode
}) => {
  return (
    <motion.div
      initial={{ height: 0 }}
      animate={{ height: consoleHeight }}
      className={`border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'} flex flex-col`}
    >
      {/* Console Header */}
      <div className={`flex items-center justify-between px-4 py-2 border-b ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        <div className="flex items-center gap-2">
          <Terminal size={16} />
          <span className="text-sm font-medium">Console</span>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Resize Controls */}
          <button
            onClick={() => onHeightChange(Math.max(100, consoleHeight - 50))}
            className={`p-1 rounded transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300'
                : 'text-gray-600 hover:text-gray-700'
            }`}
            title="Decrease Console Height"
          >
            <ChevronDown size={14} />
          </button>
          <button
            onClick={() => onHeightChange(Math.min(400, consoleHeight + 50))}
            className={`p-1 rounded transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300'
                : 'text-gray-600 hover:text-gray-700'
            }`}
            title="Increase Console Height"
          >
            <ChevronUp size={14} />
          </button>

          {/* Clear Console Button */}
          <button
            onClick={onClearOutput}
            className={`p-1 rounded transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300'
                : 'text-gray-600 hover:text-gray-700'
            }`}
            title="Clear Console"
          >
            <RotateCcw size={14} />
          </button>

          {/* Hide Console Button */}
          <button
            onClick={onHide}
            className={`p-1 rounded transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300'
                : 'text-gray-600 hover:text-gray-700'
            }`}
            title="Hide Console"
          >
            <EyeOff size={14} />
          </button>

          {/* Close Console Button */}
          <button
            onClick={onClose}
            className={`p-1 rounded transition-colors ${
              isDarkMode
                ? 'text-red-400 hover:text-red-300'
                : 'text-red-600 hover:text-red-700'
            }`}
            title="Close Console"
          >
            <X size={14} />
          </button>
        </div>
      </div>

      {/* Console Content */}
      <div className={`flex-1 p-4 overflow-auto ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-50'
      }`}>
        {output ? (
          <pre className={`text-sm font-mono whitespace-pre-wrap ${
            isDarkMode ? 'text-green-400' : 'text-green-600'
          }`}>
            {output}
          </pre>
        ) : (
          <div className={`text-sm ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
            Console output will appear here...
            <br />
            <br />
            💡 Tips:
            <br />
            • Use console.log() in your JavaScript files
            <br />
            • Test API calls with the API Tester panel
            <br />
            • View live preview of your HTML/CSS/JS
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default Console;
