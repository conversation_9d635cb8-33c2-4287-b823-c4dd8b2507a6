export const sanitizeString = (
  value,
  options = { stripHtml: true, escape: true }
) => {
  if (typeof value !== "string") return "";

  let result = value.trim();

  if (options.stripHtml) {
    result = result.replace(/<\/?[^>]+(>|$)/g, "");
  }

  if (options.escape) {
    const map = {
      "&": "&amp;",
      "<": "&lt;",
      ">": "&gt;",
      '"': "&quot;",
      "'": "&#039;",
      "/": "&#x2F;",
      "`": "&#x60;",
      "=": "&#x3D;",
    };
    result = result.replace(/[&<>"'`=\/]/g, (char) => map[char]);
  }

  return result;
};

export const sanitizeObject = (obj, schema = {}) => {
  const sanitized = {};

  for (const key in schema) {
    if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;

    const rule = schema[key];

    if (rule === "string") {
      sanitized[key] = sanitizeString(obj[key]); // default strip + escape
    } else if (typeof rule === "object" && rule.type === "string") {
      sanitized[key] = sanitizeString(obj[key], {
        stripHtml: rule.stripHtml ?? true,
        escape: rule.escape ?? true,
      });
    } else if (rule === "number") {
      sanitized[key] =
        typeof obj[key] === "number" ? obj[key] : parseFloat(obj[key]) || 0;
    } else if (rule === "boolean") {
      sanitized[key] = obj[key] === "true" || obj[key] === true;
    } else {
      sanitized[key] = obj[key];
    }
  }

  return sanitized;
};

