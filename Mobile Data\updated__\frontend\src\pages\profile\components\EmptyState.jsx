import { FaCode } from "react-icons/fa";

const EmptyState = ({ activeTab }) => {
  const getEmptyStateMessage = () => {
    switch (activeTab) {
      case 'active':
        return "Start a new project to see it here";
      case 'completed':
        return "Complete some projects to see them here";
      case 'paused':
        return "No projects are currently paused";
      default:
        return "No projects found";
    }
  };

  return (
    <div className="text-center py-16">
      <div className="w-24 h-24 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full flex items-center justify-center mx-auto mb-6">
        <FaCode className="text-slate-300 text-3xl" />
      </div>
      <h3 className="text-2xl font-bold text-slate-300 mb-2">
        No {activeTab} projects
      </h3>
      <p className="text-slate-400 mb-6">
        {getEmptyStateMessage()}
      </p>
      <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 font-semibold">
        + Add New Project
      </button>
    </div>
  );
};

export default EmptyState;
