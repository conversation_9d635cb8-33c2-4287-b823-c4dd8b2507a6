import React from 'react';
import { FileText, FilePlus, Trash2 } from 'lucide-react';

const FileTree = ({
  files,
  activeFile,
  onFileSelect,
  onFileDelete,
  onCreateFile,
  isDarkMode
}) => {
  return (
    <div className={`w-64 border-r ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'} flex flex-col`}>
      <div className={`p-3 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold">Files</h3>
          <button
            onClick={onCreateFile}
            className={`p-1 rounded transition-colors ${
              isDarkMode
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
            }`}
            title="New File"
          >
            <FilePlus size={14} />
          </button>
        </div>
      </div>
      
      <div className="flex-1 overflow-auto p-2">
        {Object.entries(files).map(([fileName]) => (
          <div
            key={fileName}
            className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
              activeFile === fileName
                ? isDarkMode
                  ? 'bg-blue-600 text-white'
                  : 'bg-blue-100 text-blue-800'
                : isDarkMode
                  ? 'hover:bg-gray-700 text-gray-300'
                  : 'hover:bg-gray-200 text-gray-700'
            }`}
            onClick={() => onFileSelect(fileName)}
          >
            <div className="flex items-center gap-2">
              <FileText size={14} />
              <span className="text-sm">{fileName}</span>
            </div>
            {Object.keys(files).length > 1 && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onFileDelete(fileName);
                }}
                className={`p-1 rounded transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-red-400'
                    : 'text-gray-500 hover:text-red-500'
                }`}
                title="Delete File"
              >
                <Trash2 size={12} />
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default FileTree;
