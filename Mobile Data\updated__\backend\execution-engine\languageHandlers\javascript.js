import path from "path";
import fs from "fs";
import os from "os";
import crypto from "crypto";
import containerPool from "../execution/containerPool.js";

// Write a temporary file on host
const writeTempFile = (content, extension = ".js", fixedName = null) => {
  const filename = fixedName || `code-${crypto.randomUUID()}${extension}`;
  const fullPath = path.join(os.tmpdir(), filename);
  fs.writeFileSync(fullPath, content, "utf-8");
  return fullPath;
};


async function streamFileIntoContainer(
  container,
  hostFilePath,
  destFilename,
  maxRetries = 3
) {
  const content = fs.readFileSync(hostFilePath);

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const exec = await container.exec({
        Cmd: ["sh", "-c", `cat > /app/${destFilename}`],
        AttachStdin: true,
        AttachStdout: false,
        AttachStderr: true,
      });

      const stream = await exec.start({ hijack: true, stdin: true });

      await new Promise((resolve, reject) => {
        let resolved = false;

        stream.on("close", () => {
          resolved = true;
          resolve();
        });

        stream.on("error", (err) => {
          if (!resolved) reject(err);
        });

        stream.write(content);
        stream.end();

        setTimeout(() => {
          if (!resolved)
            reject(new Error(`Stream timeout after 5s (attempt ${attempt})`));
        }, 5000);
      });

      await new Promise((res) => setTimeout(res, 50));

      const inspect = await exec.inspect();
      if (inspect.ExitCode === null || inspect.ExitCode !== 0) {
        throw new Error(
          `File streaming failed at /app/${destFilename}, exit code: ${inspect.ExitCode}`
        );
      }

      return;
    } catch (err) {
      if (attempt === maxRetries) throw err;
      await new Promise((r) => setTimeout(r, 100 * attempt));
    }
  }
}

// Inject fs require and read logic if needed
const injectInputReader = (code) => {
  let finalCode = code;

  finalCode = finalCode.replace(
    /([^a-zA-Z0-9_])(?<!fs\.)readFileSync/g,
    "$1fs.readFileSync"
  );

  const hasFSImport = /require\s*\(\s*['"]fs['"]\s*\)/.test(finalCode);
  if (!hasFSImport) {
    finalCode = `const fs = require('fs');\n${finalCode}`;
  }

  return finalCode;
};

// Main JS Executor
export default {
  execute: async (codeContent, input = "", { timeout = 3000 } = {}) => {
    const finalCode = input ? injectInputReader(codeContent) : codeContent;

    const codePath = writeTempFile(finalCode, ".js");
    const inputPath = input ? writeTempFile(input, ".txt", "input.txt") : null;
    const codeFile = path.basename(codePath);

    let containerObj;

    try {
      containerObj = await containerPool.getContainer("javascript");
      const c = containerObj.container;

      await streamFileIntoContainer(c, codePath, codeFile);
      if (inputPath) await streamFileIntoContainer(c, inputPath, "input.txt");

      const timeoutSec = Math.floor(timeout / 1000);
      const cmd = `timeout ${timeoutSec}s node /app/${codeFile}`;

      const exec = await c.exec({
        Cmd: ["sh", "-c", cmd],
        AttachStdout: true,
        AttachStderr: true,
      });

      const stream = await exec.start({ hijack: true, stdin: false });

      let stdout = "",
        stderr = "";

      await new Promise((resolve, reject) => {
        c.modem.demuxStream(
          stream,
          {
            write: (chunk) => {
              stdout += chunk.toString();
            },
          },
          {
            write: (chunk) => {
              stderr += chunk.toString();
            },
          }
        );
        stream.on("end", resolve);
        stream.on("error", reject);
      });

      const inspect = await exec.inspect();
      const exitCode = inspect.ExitCode;

      if (exitCode === 124) {
        return {
          success: false,
          output: "",
          error: "Execution timed out.",
        };
      }

      return {
        success: true,
        output: stdout.trim(),
        error: stderr.trim() || null,
      };
    } catch (err) {
      return {
        success: false,
        output: "",
        error: err.message,
      };
    } finally {
      if (fs.existsSync(codePath)) fs.unlinkSync(codePath);
      if (inputPath && fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
      if (containerObj) containerPool.releaseContainer(containerObj);
    }
  },
};
