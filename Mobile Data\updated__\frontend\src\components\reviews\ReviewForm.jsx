import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaStar, FaPaperPlane, FaTimes } from 'react-icons/fa';
import { useSelector } from 'react-redux';
import reviewService from '../../services/reviewService';

const ReviewForm = ({ courseId, courseName, onReviewSubmitted, onClose }) => {
  // Try to get user from Redux, but allow fallback for anonymous reviews
  let user;
  try {
    const authState = useSelector((state) => state.auth);
    user = authState?.user;
  } catch (error) {
    // If Redux is not properly configured, allow anonymous reviews
    user = null;
  }

  const [formData, setFormData] = useState({
    rating: 0,
    title: '',
    comment: '',
    userName: '',
    userEmail: ''
  });
  const [hoveredRating, setHoveredRating] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const handleRatingClick = (rating) => {
    setFormData({ ...formData, rating });
    setErrors({ ...errors, rating: '' });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: '' });
  };

  const validateForm = () => {
    const newErrors = {};

    if (formData.rating === 0) {
      newErrors.rating = 'Please select a rating';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Please enter a review title';
    }

    if (!formData.comment.trim()) {
      newErrors.comment = 'Please enter your review comment';
    } else if (formData.comment.trim().length < 10) {
      newErrors.comment = 'Review comment must be at least 10 characters long';
    }

    // If user is not logged in, require name
    if (!user && !formData.userName.trim()) {
      newErrors.userName = 'Please enter your name';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const reviewData = {
        courseId,
        courseName,
        userId: user?.id || `anonymous_${Date.now()}`,
        userName: user?.name || formData.userName || 'Anonymous User',
        userEmail: user?.email || formData.userEmail || '',
        rating: formData.rating,
        title: formData.title.trim(),
        comment: formData.comment.trim()
      };

      const newReview = reviewService.addReview(reviewData);
      
      // Reset form
      setFormData({ rating: 0, title: '', comment: '', userName: '', userEmail: '' });

      if (onReviewSubmitted) {
        onReviewSubmitted(newReview);
      }

      // Show success message
      alert('Thank you for your review! It has been published successfully.');

      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      alert('Failed to submit review. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = () => {
    return Array(5).fill(0).map((_, index) => {
      const starValue = index + 1;
      const isActive = starValue <= (hoveredRating || formData.rating);
      
      return (
        <motion.button
          key={index}
          type="button"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => handleRatingClick(starValue)}
          onMouseEnter={() => setHoveredRating(starValue)}
          onMouseLeave={() => setHoveredRating(0)}
          className={`text-2xl transition-colors ${
            isActive ? 'text-yellow-400' : 'text-gray-300'
          } hover:text-yellow-400`}
        >
          <FaStar />
        </motion.button>
      );
    });
  };

  // Remove the authentication requirement - allow all users to submit reviews

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-600 p-6"
    >
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-xl font-bold text-white">
            Write a Review for {courseName}
          </h3>
          {!user && (
            <p className="text-sm text-gray-400 mt-1">
              Share your experience with other students
            </p>
          )}
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <FaTimes />
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* User Info (if not logged in) */}
        {!user && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="userName" className="block text-sm font-medium text-gray-300 mb-2">
                Your Name *
              </label>
              <input
                type="text"
                id="userName"
                name="userName"
                value={formData.userName}
                onChange={handleInputChange}
                placeholder="Enter your name"
                className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.userName ? 'border-red-500' : 'border-gray-600'
                }`}
                required
              />
              {errors.userName && (
                <p className="mt-1 text-sm text-red-400">{errors.userName}</p>
              )}
            </div>
            <div>
              <label htmlFor="userEmail" className="block text-sm font-medium text-gray-300 mb-2">
                Your Email
              </label>
              <input
                type="email"
                id="userEmail"
                name="userEmail"
                value={formData.userEmail}
                onChange={handleInputChange}
                placeholder="Enter your email (optional)"
                className="w-full px-3 py-2 bg-gray-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 border-gray-600"
              />
            </div>
          </div>
        )}

        {/* Rating */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Rating *
          </label>
          <div className="flex items-center space-x-1">
            {renderStars()}
            <span className="ml-2 text-sm text-gray-400">
              {formData.rating > 0 && `${formData.rating} out of 5 stars`}
            </span>
          </div>
          {errors.rating && (
            <p className="mt-1 text-sm text-red-400">{errors.rating}</p>
          )}
        </div>

        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-2">
            Review Title *
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            placeholder="Summarize your experience..."
            className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.title ? 'border-red-500' : 'border-gray-600'
            }`}
            maxLength={100}
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-400">{errors.title}</p>
          )}
        </div>

        {/* Comment */}
        <div>
          <label htmlFor="comment" className="block text-sm font-medium text-gray-300 mb-2">
            Your Review *
          </label>
          <textarea
            id="comment"
            name="comment"
            value={formData.comment}
            onChange={handleInputChange}
            placeholder="Share your detailed experience with this course..."
            rows={4}
            className={`w-full px-3 py-2 bg-gray-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.comment ? 'border-red-500' : 'border-gray-600'
            }`}
            maxLength={500}
          />
          <div className="flex justify-between items-center mt-1">
            {errors.comment ? (
              <p className="text-sm text-red-400">{errors.comment}</p>
            ) : (
              <p className="text-sm text-gray-400">
                {formData.comment.length}/500 characters
              </p>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <motion.button
            type="submit"
            disabled={isSubmitting}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className={`px-6 py-2 bg-blue-600 text-white rounded-md font-medium flex items-center space-x-2 transition-colors ${
              isSubmitting 
                ? 'opacity-50 cursor-not-allowed' 
                : 'hover:bg-blue-700'
            }`}
          >
            <FaPaperPlane />
            <span>{isSubmitting ? 'Submitting...' : 'Submit Review'}</span>
          </motion.button>
        </div>
      </form>
    </motion.div>
  );
};

export default ReviewForm;
