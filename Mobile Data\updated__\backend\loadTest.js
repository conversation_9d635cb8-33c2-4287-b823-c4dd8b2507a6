import http from "k6/http";
import { sleep, check } from "k6";

export let options = {
  vus: 10,
  duration: "30s",
};

export default function () {
  let loginRes = http.post(
    "http://localhost:8000/api/v1/auth/login",
    JSON.stringify({
      email: "r<PERSON><PERSON><PERSON><PERSON><EMAIL>",
      password: "Anonymous@123",
    }),
    {
      headers: { "Content-Type": "application/json" },
    }
  );

  check(loginRes, {
    "login status 200": (r) => r.status === 200,
    "login cookie set": (r) => r.cookies["accessToken"] !== undefined,
  });
  let accessToken = loginRes.cookies["accessToken"][0].value;

  let payload = JSON.stringify({
    problemId: "686eff7cf316799e2a87f1b6",
    language: "javascript",
    code: `const fs = require('fs');

const inputData = fs.readFileSync('/app/input.txt', 'utf-8').trim();
const [line1, line2] = inputData.split('\\n');

const nums = line1.trim().split(' ').map(Number);
const target = parseInt(line2.trim());

const map = new Map();

for (let i = 0; i < nums.length; i++) {
  const complement = target - nums[i];
  if (map.has(complement)) {
    console.log(\`\${map.get(complement)} \${i}\`);
    break;
  }
  map.set(nums[i], i);
}`,
  });

  let res = http.post(
    "http://localhost:8000/api/v1/problem/execute/run",
    payload,
    {
      headers: {
        "Content-Type": "application/json",
        Cookie: `accessToken=${accessToken}`,
      },
    }
  );

  check(res, {
    "execution status 200": (r) => r.status === 200,
  });

  sleep(1);
}
