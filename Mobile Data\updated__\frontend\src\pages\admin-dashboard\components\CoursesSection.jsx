import React, { useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaEye, FaUsers, FaChartBar, FaStar, FaToggleOn, FaToggleOff, FaTimes } from 'react-icons/fa';

// Mock instructors data
const instructors = [
  { id: 1, name: '<PERSON>' },
  { id: 2, name: '<PERSON>' },
  { id: 3, name: '<PERSON>' },
  { id: 4, name: '<PERSON>' },
  { id: 5, name: '<PERSON>' }
];

// Mock data for courses with enhanced properties
const initialCourses = [
  {
    id: 1,
    title: 'Python Master Course',
    instructors: [1, 2], // IDs of assigned instructors
    description: 'A comprehensive course covering Python fundamentals to advanced concepts.',
    published: true,
    enrollments: 245,
    views: 1820,
    completionRate: 68,
    rating: 4.7
  },
  {
    id: 2,
    title: 'Full Stack Development',
    instructors: [2],
    description: 'Learn to build complete web applications with modern technologies.',
    published: true,
    enrollments: 189,
    views: 1540,
    completionRate: 72,
    rating: 4.5
  },
  {
    id: 3,
    title: 'Data Science Fundamentals',
    instructors: [3, 5],
    description: 'Introduction to data analysis, visualization, and machine learning.',
    published: false,
    enrollments: 120,
    views: 980,
    completionRate: 65,
    rating: 4.3
  }
];

const CoursesSection = () => {
  const [courses, setCourses] = useState(initialCourses);
  const [showModal, setShowModal] = useState(false);
  const [editingCourse, setEditingCourse] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    instructors: [],
    description: '',
    published: true
  });
  const [activeTab, setActiveTab] = useState('all');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleInstructorChange = (instructorId) => {
    const newInstructors = [...formData.instructors];
    if (newInstructors.includes(instructorId)) {
      // Remove instructor if already selected
      setFormData({
        ...formData,
        instructors: newInstructors.filter(id => id !== instructorId)
      });
    } else {
      // Add instructor if not selected
      setFormData({
        ...formData,
        instructors: [...newInstructors, instructorId]
      });
    }
  };

  const openModal = (course = null) => {
    if (course) {
      // Edit mode
      setFormData({
        title: course.title,
        instructors: course.instructors || [],
        description: course.description,
        published: course.published
      });
      setEditingCourse(course);
    } else {
      // Add mode
      setFormData({
        title: '',
        instructors: [],
        description: '',
        published: true
      });
      setEditingCourse(null);
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingCourse(null);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (editingCourse) {
      // Update existing course
      setCourses(courses.map(course => 
        course.id === editingCourse.id 
          ? { 
              ...course, 
              ...formData 
            } 
          : course
      ));
    } else {
      // Add new course
      const newCourse = {
        id: courses.length + 1,
        ...formData,
        enrollments: 0,
        views: 0,
        completionRate: 0,
        rating: 0
      };
      setCourses([...courses, newCourse]);
    }
    
    closeModal();
  };

  const togglePublishStatus = (courseId) => {
    setCourses(courses.map(course => 
      course.id === courseId 
        ? { ...course, published: !course.published } 
        : course
    ));
  };

  const deleteCourse = (courseId) => {
    setCourses(courses.filter(course => course.id !== courseId));
    setShowDeleteConfirm(null);
  };

  const getInstructorNames = (instructorIds) => {
    return instructorIds.map(id => 
      instructors.find(instructor => instructor.id === id)?.name
    ).join(', ');
  };

  const filteredCourses = activeTab === 'all' 
    ? courses 
    : courses.filter(course => 
        activeTab === 'published' ? course.published : !course.published
      );

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Courses Management</h2>
        <button
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center"
          onClick={() => openModal()}
        >
          <FaPlus className="mr-2" />
          Add New Course
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-6">
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'all' 
              ? 'text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('all')}
        >
          All Courses ({courses.length})
        </button>
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'published' 
              ? 'text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('published')}
        >
          Published ({courses.filter(c => c.published).length})
        </button>
        <button
          className={`py-2 px-4 font-medium ${
            activeTab === 'unpublished' 
              ? 'text-blue-600 border-b-2 border-blue-600' 
              : 'text-gray-500 hover:text-gray-700'
          }`}
          onClick={() => setActiveTab('unpublished')}
        >
          Unpublished ({courses.filter(c => !c.published).length})
        </button>
      </div>

      {/* Courses Table */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden mb-6">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Instructors</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enrollments</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Analytics</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredCourses.map((course) => (
              <tr key={course.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{course.title}</div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">{course.description}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{getInstructorNames(course.instructors)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <button 
                    onClick={() => togglePublishStatus(course.id)}
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      course.published 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {course.published ? <FaToggleOn className="mr-1" /> : <FaToggleOff className="mr-1" />}
                    {course.published ? 'Published' : 'Draft'}
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-900">
                    <FaUsers className="mr-1 text-blue-500" />
                    {course.enrollments}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center text-sm text-gray-500" title="Views">
                      <FaEye className="mr-1 text-blue-500" />
                      {course.views}
                    </div>
                    <div className="flex items-center text-sm text-gray-500" title="Completion Rate">
                      <FaChartBar className="mr-1 text-green-500" />
                      {course.completionRate}%
                    </div>
                    <div className="flex items-center text-sm text-gray-500" title="Rating">
                      <FaStar className="mr-1 text-yellow-500" />
                      {course.rating}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => openModal(course)} 
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    <FaEdit />
                  </button>
                  <button 
                    onClick={() => setShowDeleteConfirm(course.id)} 
                    className="text-red-600 hover:text-red-900"
                  >
                    <FaTrash />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Course Form Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800">
                {editingCourse ? 'Edit Course' : 'Add New Course'}
              </h3>
              <button 
                onClick={closeModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6">
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="title">
                  Course Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2">
                  Assign Instructors
                </label>
                <div className="grid grid-cols-2 gap-2">
                  {instructors.map(instructor => (
                    <div key={instructor.id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`instructor-${instructor.id}`}
                        checked={formData.instructors.includes(instructor.id)}
                        onChange={() => handleInstructorChange(instructor.id)}
                        className="mr-2"
                      />
                      <label htmlFor={`instructor-${instructor.id}`} className="text-sm text-gray-700">
                        {instructor.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="description">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  rows="4"
                  required
                ></textarea>
              </div>
              
              <div className="mb-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="published"
                    name="published"
                    checked={formData.published}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <label htmlFor="published" className="text-sm font-medium text-gray-700">
                    Publish this course
                  </label>
                </div>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="button"
                  className="bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2"
                  onClick={closeModal}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-4 py-2 rounded-md"
                >
                  {editingCourse ? 'Update Course' : 'Create Course'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this course? This action cannot be undone.
            </p>
            <div className="flex justify-end">
              <button
                className="bg-gray-300 text-gray-800 px-4 py-2 rounded-md mr-2"
                onClick={() => setShowDeleteConfirm(null)}
              >
                Cancel
              </button>
              <button
                className="bg-red-600 text-white px-4 py-2 rounded-md"
                onClick={() => deleteCourse(showDeleteConfirm)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CoursesSection;