import React from "react";
import { motion } from "framer-motion";

const TopicsGrid = ({ 
  topics, 
  activeSection, 
  allCaseStudies, 
  scrollToSection, 
  itemVariants 
}) => {
  return (
    <motion.div variants={itemVariants} className="mb-16">
      <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
        Select a Topic to Explore
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
        {topics.map((topic, index) => (
          <motion.div
            key={index}
            variants={itemVariants}
            whileHover={{ scale: 1.05, y: -5 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => scrollToSection(topic.sectionId)}
            className={`bg-white p-6 rounded-2xl shadow-lg border cursor-pointer hover:shadow-xl transition-all duration-300 text-center ${
              activeSection === topic.sectionId 
                ? 'border-orange-500 bg-orange-50' 
                : 'border-gray-100'
            }`}
          >
            <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${topic.color} flex items-center justify-center text-white text-2xl`}>
              <i className={topic.icon}></i>
            </div>
            <h4 className="text-lg font-semibold text-gray-900 mb-2">{topic.title}</h4>
            <div className="text-sm text-gray-600">
              {allCaseStudies[topic.sectionId]?.length || 0} Case Studies
            </div>
            {activeSection === topic.sectionId && (
              <div className="mt-2 text-xs text-orange-600 font-medium">
                ✓ Active
              </div>
            )}
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default TopicsGrid;
