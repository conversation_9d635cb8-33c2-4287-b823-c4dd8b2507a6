import express from "express";
import {
  activateUser,
  deleteUser,
  generateResetPasswordToken,
  generateTwoFactorToken,
  getAllUsers,
  getUserDetails,
  googleLogin,
  loginUser,
  logoutUser,
  refreshAccessToken,
  registrationUser,
  resetPassword,
  updatePassword,
  updateSocialMedia,
  updateUserDetails,
  updateUserRole,
  verifyTwoFactorToken,
  verifyTwoFAOtp,
} from "../controllers/user.js";
import { isAuthenticated } from "../middleware/auth.js";
import { upload } from "../utils/multerConfig.js";

const userRouter = express.Router();

userRouter.get("/google", googleLogin);

userRouter.get("/me", isAuthenticated, getUserDetails);

userRouter.post("/register", upload.single("image"), registrationUser);

userRouter.post("/activate-user", activateUser);

userRouter.post("/login", loginUser);

userRouter.post("/refresh-token", refreshAccessToken);

userRouter.post("/generate-reset-token", generateResetPasswordToken);

userRouter.post("/new-password", resetPassword);

userRouter.post("/logout", isAuthenticated, logoutUser);

userRouter.patch(
  "/user/edit-profile",
  upload.single("image"),
  isAuthenticated,
  updateUserDetails
);

userRouter.patch("/user/social-accounts", isAuthenticated, updateSocialMedia);

userRouter.put("/user/update-password", isAuthenticated, updatePassword);

userRouter.post("/two-fa-token", isAuthenticated, generateTwoFactorToken);

userRouter.post("/verify-two-fa",verifyTwoFactorToken);

userRouter.post("/verify-2fa",verifyTwoFAOtp);

// admin routes

userRouter.get("/all-users",isAuthenticated,getAllUsers);

userRouter.delete("/delete-user/:id",isAuthenticated,deleteUser);

userRouter.put("/update-role/:id",isAuthenticated,updateUserRole);

export default userRouter;
