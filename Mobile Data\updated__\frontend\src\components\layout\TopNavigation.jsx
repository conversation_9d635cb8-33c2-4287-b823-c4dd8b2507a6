import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';

const TopNavigation = ({ theme, onToggleTheme }) => {
  const location = useLocation();

  const navItems = [
    { path: '/code-editor/1/solve', label: 'Code Editor', icon: '💻' },
    { path: '/data_strut', label: 'DSA Lab', icon: '📊' },
    { path: '/api-tester', label: 'API Tester', icon: '🔧' },
    { path: '/process-manager', label: 'Process Manager', icon: '⚙️' }
  ];

  const isActive = (path) => {
    if (path === '/code-editor/1/solve') {
      return location.pathname.startsWith('/code-editor');
    }
    return location.pathname === path;
  };

  return (
    <nav className={`border-b ${
      theme === 'dark' 
        ? 'bg-gray-800 border-gray-700' 
        : 'bg-white border-gray-200'
    } px-4 py-3`}>
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        {/* Logo/Brand */}
        <div className="flex items-center space-x-4">
          <Link 
            to="/" 
            className={`text-xl font-bold ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}
          >
            DevTools
          </Link>
        </div>

        {/* Navigation Items */}
        <div className="flex items-center space-x-1">
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                isActive(item.path)
                  ? theme === 'dark'
                    ? 'bg-blue-600 text-white'
                    : 'bg-blue-600 text-white'
                  : theme === 'dark'
                    ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              }`}
            >
              {isActive(item.path) && (
                <motion.div
                  layoutId="activeTab"
                  className="absolute inset-0 bg-blue-600 rounded-lg"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
              <span className="relative flex items-center gap-2">
                <span>{item.icon}</span>
                <span className="hidden sm:inline">{item.label}</span>
              </span>
            </Link>
          ))}
        </div>

        {/* Theme Toggle */}
        <div className="flex items-center space-x-4">
          <button
            onClick={onToggleTheme}
            className={`p-2 rounded-lg transition-colors ${
              theme === 'dark' 
                ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-600'
            }`}
            title="Toggle Theme"
          >
            {theme === 'dark' ? '☀️' : '🌙'}
          </button>
        </div>
      </div>
    </nav>
  );
};

export default TopNavigation;
