import React, { useState } from 'react';
import { Plus, Edit, Trash2, X, Save, Search, Tag, Calendar, User, Eye, EyeOff, ThumbsUp, ThumbsDown } from 'lucide-react';

const FAQManager = ({ theme }) => {
  // Enhanced FAQ data based on faqModal.js
  const [faqs, setFaqs] = useState([
    {
      _id: '507f1f77bcf86cd799439011',
      question: 'How do I reset my password?',
      answer: 'You can reset your password by clicking on the "Forgot Password" link on the login page. You\'ll receive an email with instructions to create a new password. Make sure to check your spam folder if you don\'t see the email within a few minutes.',
      category: 'Account Management',
      tags: ['password', 'reset', 'login', 'security'],
      isActive: true,
      priority: 'high',
      author: 'Admin Team',
      views: 1247,
      helpful: 89,
      notHelpful: 12,
      lastUpdated: '2024-01-15T10:30:00Z',
      createdAt: '2024-01-01T09:00:00Z',
      relatedQuestions: ['507f1f77bcf86cd799439012'],
      searchKeywords: ['password reset', 'forgot password', 'login issues']
    },
    {
      _id: '507f1f77bcf86cd799439012',
      question: 'What programming languages are supported on the platform?',
      answer: 'Our platform supports a wide range of programming languages including JavaScript, Python, Java, C++, C#, Go, Rust, TypeScript, PHP, Ruby, Swift, and Kotlin. Each language comes with syntax highlighting, auto-completion, and debugging tools.',
      category: 'Technical Features',
      tags: ['programming', 'languages', 'coding', 'support'],
      isActive: true,
      priority: 'medium',
      author: 'Technical Team',
      views: 2156,
      helpful: 145,
      notHelpful: 8,
      lastUpdated: '2024-01-14T15:20:00Z',
      createdAt: '2024-01-02T11:30:00Z',
      relatedQuestions: ['507f1f77bcf86cd799439013'],
      searchKeywords: ['programming languages', 'coding support', 'syntax highlighting']
    },
    {
      _id: '507f1f77bcf86cd799439013',
      question: 'How can I contact customer support?',
      answer: 'You can reach our support team through multiple channels: 1) Use the live chat feature (bottom right corner), 2) Email <NAME_EMAIL>, 3) Submit a ticket through the Help Center, or 4) Call us at +1-800-UPCODING during business hours (9 AM - 6 PM EST).',
      category: 'Customer Support',
      tags: ['support', 'contact', 'help', 'assistance'],
      isActive: true,
      priority: 'high',
      author: 'Support Team',
      views: 892,
      helpful: 67,
      notHelpful: 5,
      lastUpdated: '2024-01-13T14:45:00Z',
      createdAt: '2024-01-03T08:15:00Z',
      relatedQuestions: [],
      searchKeywords: ['contact support', 'help desk', 'customer service']
    },
    {
      _id: '507f1f77bcf86cd799439014',
      question: 'What are the system requirements for the coding environment?',
      answer: 'Our web-based coding environment works on any modern browser (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+). For optimal performance, we recommend: 8GB RAM, stable internet connection (5+ Mbps), and a screen resolution of 1366x768 or higher.',
      category: 'Technical Requirements',
      tags: ['system', 'requirements', 'browser', 'performance'],
      isActive: false,
      priority: 'low',
      author: 'Technical Team',
      views: 456,
      helpful: 34,
      notHelpful: 3,
      lastUpdated: '2024-01-12T16:00:00Z',
      createdAt: '2024-01-04T12:00:00Z',
      relatedQuestions: ['507f1f77bcf86cd799439012'],
      searchKeywords: ['system requirements', 'browser compatibility', 'performance']
    }
  ]);

  const [showModal, setShowModal] = useState(false);
  const [editingFaq, setEditingFaq] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [formData, setFormData] = useState({
    question: '',
    answer: '',
    category: 'General',
    tags: [],
    isActive: true,
    priority: 'medium',
    author: 'Admin',
    searchKeywords: []
  });

  const categories = ['Account Management', 'Technical Features', 'Customer Support', 'Technical Requirements', 'General'];

  // Filter FAQs based on search and filters
  const filteredFaqs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         faq.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = filterCategory === '' || faq.category === filterCategory;
    const matchesStatus = filterStatus === '' ||
      (filterStatus === 'active' && faq.isActive) ||
      (filterStatus === 'inactive' && !faq.isActive);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const handleAdd = () => {
    setEditingFaq(null);
    setFormData({
      question: '',
      answer: '',
      category: 'General',
      tags: [],
      isActive: true,
      priority: 'medium',
      author: 'Admin',
      searchKeywords: []
    });
    setShowModal(true);
  };

  const handleEdit = (faq) => {
    setEditingFaq(faq);
    setFormData({
      question: faq.question,
      answer: faq.answer,
      category: faq.category,
      tags: faq.tags || [],
      isActive: faq.isActive,
      priority: faq.priority || 'medium',
      author: faq.author || 'Admin',
      searchKeywords: faq.searchKeywords || []
    });
    setShowModal(true);
  };

  const handleSave = () => {
    if (editingFaq) {
      setFaqs(prev => prev.map(faq =>
        faq._id === editingFaq._id
          ? { ...faq, ...formData, lastUpdated: new Date().toISOString() }
          : faq
      ));
    } else {
      const newFaq = {
        _id: Date.now().toString(),
        ...formData,
        views: 0,
        helpful: 0,
        notHelpful: 0,
        relatedQuestions: [],
        createdAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString()
      };
      setFaqs(prev => [...prev, newFaq]);
    }
    setShowModal(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this FAQ?')) {
      setFaqs(prev => prev.filter(faq => faq._id !== id));
    }
  };

  const toggleStatus = (id) => {
    setFaqs(prev => prev.map(faq =>
      faq._id === id
        ? { ...faq, isActive: !faq.isActive, lastUpdated: new Date().toISOString() }
        : faq
    ));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            FAQ Manager
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Comprehensive FAQ management based on faqModal.js - Total: {faqs.length} FAQs
          </p>
        </div>

        <button
          onClick={handleAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add FAQ</span>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Search FAQs by question, answer, or tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
              theme === 'dark'
                ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
            } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
          />
        </div>

        <select
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          className={`px-4 py-2 rounded-lg border ${
            theme === 'dark'
              ? 'bg-gray-800 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
        >
          <option value="">All Categories</option>
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>

        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className={`px-4 py-2 rounded-lg border ${
            theme === 'dark'
              ? 'bg-gray-800 border-gray-600 text-white'
              : 'bg-white border-gray-300 text-gray-900'
          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
        >
          <option value="">All Status</option>
          <option value="active">Active</option>
          <option value="inactive">Inactive</option>
        </select>
      </div>

      {/* FAQ List */}
      <div className="space-y-4">
        {filteredFaqs.length === 0 ? (
          <div className={`text-center py-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
            No FAQs found matching your criteria.
          </div>
        ) : (
          filteredFaqs.map((faq) => (
          <div
            key={faq._id}
            className={`rounded-lg border p-6 ${
              theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-3">
                  <h3 className={`text-lg font-semibold ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {faq.question}
                  </h3>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    faq.category === 'Account Management' ? 'bg-blue-100 text-blue-800' :
                    faq.category === 'Technical Features' ? 'bg-green-100 text-green-800' :
                    faq.category === 'Customer Support' ? 'bg-purple-100 text-purple-800' :
                    faq.category === 'Technical Requirements' ? 'bg-red-100 text-red-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {faq.category}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    faq.priority === 'high' ? 'bg-red-100 text-red-800' :
                    faq.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-green-100 text-green-800'
                  }`}>
                    {faq.priority} priority
                  </span>
                  <button
                    onClick={() => toggleStatus(faq._id)}
                    className={`px-2 py-1 text-xs rounded-full ${
                      faq.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {faq.isActive ? 'Active' : 'Inactive'}
                  </button>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {faq.tags.map((tag, index) => (
                    <span key={index} className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                      theme === 'dark' ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'
                    }`}>
                      <Tag size={10} className="mr-1" />
                      {tag}
                    </span>
                  ))}
                </div>

                <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'} mb-3`}>
                  {faq.answer}
                </p>

                {/* Stats and Info */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                  <div className="flex items-center gap-1">
                    <Eye size={14} className="text-gray-400" />
                    <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      {faq.views} views
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ThumbsUp size={14} className="text-green-500" />
                    <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      {faq.helpful} helpful
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <ThumbsDown size={14} className="text-red-500" />
                    <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      {faq.notHelpful} not helpful
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <User size={14} className="text-gray-400" />
                    <span className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      {faq.author}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Calendar size={14} className="text-gray-400" />
                      <span className={`text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                        Created: {new Date(faq.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <span className={`text-sm ${theme === 'dark' ? 'text-gray-500' : 'text-gray-400'}`}>
                      Updated: {new Date(faq.lastUpdated).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => handleEdit(faq)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  }`}
                >
                  <Edit size={16} />
                </button>
                <button
                  onClick={() => handleDelete(faq._id)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                      : 'text-red-500 hover:bg-red-50 hover:text-red-700'
                  }`}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          </div>
          ))
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className={`rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-lg font-semibold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {editingFaq ? 'Edit FAQ' : 'Add New FAQ'}
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className={`p-2 rounded-lg transition-colors ${
                  theme === 'dark'
                    ? 'text-gray-400 hover:bg-gray-700 hover:text-white'
                    : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                }`}
              >
                <X size={20} />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Question
                </label>
                <input
                  type="text"
                  value={formData.question}
                  onChange={(e) => setFormData(prev => ({ ...prev, question: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  placeholder="Enter the question..."
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  Answer
                </label>
                <textarea
                  value={formData.answer}
                  onChange={(e) => setFormData(prev => ({ ...prev, answer: e.target.value }))}
                  rows={4}
                  className={`w-full px-3 py-2 rounded-lg border ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  placeholder="Enter the answer..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Category
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  >
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    Status
                  </label>
                  <select
                    value={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.value === 'true' }))}
                    className={`w-full px-3 py-2 rounded-lg border ${
                      theme === 'dark'
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  >
                    <option value="true">Active</option>
                    <option value="false">Inactive</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowModal(false)}
                className={`px-4 py-2 rounded-lg border transition-colors ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <Save size={16} />
                <span>{editingFaq ? 'Update' : 'Create'}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FAQManager;
