import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCreditCard, FaPaypal, FaGooglePay, FaApplePay, FaUniversity, FaBitcoin, FaCheck, FaLock, FaStar, FaUsers, FaClock, FaTimes } from 'react-icons/fa';
import { SiPhonepe, SiGooglepay, SiPaytm } from 'react-icons/si';

const PaymentModal = ({ isOpen, onClose, courseData }) => {
  const [selectedTier, setSelectedTier] = useState('premium');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');

  if (!courseData) return null;

  // Course-specific themes
  const courseThemes = {
    python: {
      gradient: 'from-blue-500 to-blue-700',
      accent: 'blue-500',
      border: 'border-blue-500/30',
      bg: 'bg-blue-500/10',
      text: 'text-blue-300'
    },
    fullstack: {
      gradient: 'from-purple-500 to-indigo-600',
      accent: 'purple-500',
      border: 'border-purple-500/30',
      bg: 'bg-purple-500/10',
      text: 'text-purple-300'
    },
    datascience: {
      gradient: 'from-teal-500 to-cyan-600',
      accent: 'teal-500',
      border: 'border-teal-500/30',
      bg: 'bg-teal-500/10',
      text: 'text-teal-300'
    },
    default: {
      gradient: 'from-blue-500 to-blue-700',
      accent: 'blue-500',
      border: 'border-blue-500/30',
      bg: 'bg-blue-500/10',
      text: 'text-blue-300'
    }
  };

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: <FaCreditCard />, popular: true, fee: '2.9%' },
    { id: 'paypal', name: 'PayPal', icon: <FaPaypal />, popular: true, fee: '3.4%' },
    { id: 'upi', name: 'UPI', icon: <FaUniversity />, popular: true, fee: '0%' },
    { id: 'googlepay', name: 'Google Pay', icon: <SiGooglepay />, popular: false, fee: '0%' },
    { id: 'phonepe', name: 'PhonePe', icon: <SiPhonepe />, popular: false, fee: '0%' },
    { id: 'paytm', name: 'Paytm', icon: <SiPaytm />, popular: false, fee: '1.5%' }
  ];

  // Pricing tiers (in Indian Rupees)
  const pricingTiers = {
    basic: { price: 199, features: ['Course videos', 'Basic exercises', 'Community access', 'Mobile app access'] },
    premium: { price: 499, features: ['Everything in Basic', 'Live sessions', '1-on-1 mentoring', 'Certificate', 'Priority support'] },
    enterprise: { price: 999, features: ['Everything in Premium', 'Team collaboration', 'Custom projects', 'Dedicated support', 'Advanced analytics'] }
  };

  const getTheme = () => {
    const courseType = courseData.labType?.toLowerCase() || courseData.name?.toLowerCase() || 'default';
    return courseThemes[courseType] || courseThemes.default;
  };

  const theme = getTheme();

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 10 }}
            transition={{ duration: 0.2 }}
            className="bg-gradient-to-br from-[#1a3c50] to-[#010509] rounded-2xl max-w-2xl w-full max-h-[85vh] overflow-y-auto border border-white/10"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 bg-gradient-to-br from-[#1a3c50] to-[#010509] border-b border-white/10 p-4 flex items-center justify-between z-10">
              <div>
                <h2 className="text-xl font-bold text-white">Enroll in {courseData.name}</h2>
                <p className="text-gray-300 text-sm">Choose your plan and payment method</p>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors p-2"
              >
                <FaTimes className="text-lg" />
              </button>
            </div>

            <div className="p-4">
              {/* Course Stats */}
              <div className="grid grid-cols-3 gap-3 mb-6">
                <div className={`bg-transparent border-2 ${theme.border} rounded-lg p-3 text-center`}>
                  <FaStar className={`text-lg ${theme.text} mx-auto mb-1`} />
                  <div className="text-lg font-bold text-white">4.8/5</div>
                  <div className="text-gray-300 text-xs">Rating</div>
                </div>
                <div className={`bg-transparent border-2 ${theme.border} rounded-lg p-3 text-center`}>
                  <FaUsers className={`text-lg ${theme.text} mx-auto mb-1`} />
                  <div className="text-lg font-bold text-white">15k+</div>
                  <div className="text-gray-300 text-xs">Students</div>
                </div>
                <div className={`bg-transparent border-2 ${theme.border} rounded-lg p-3 text-center`}>
                  <FaClock className={`text-lg ${theme.text} mx-auto mb-1`} />
                  <div className="text-lg font-bold text-white">12w</div>
                  <div className="text-gray-300 text-xs">Duration</div>
                </div>
              </div>

              {/* Pricing Tiers */}
              <div className="mb-6">
                <h3 className="text-xl font-bold text-white text-center mb-4">Choose Your Plan</h3>
                <div className="grid grid-cols-1 gap-3">
                  {Object.entries(pricingTiers).map(([tier, details]) => {
                    const isSelected = selectedTier === tier;
                    const isPopular = tier === 'premium';
                    
                    return (
                      <div
                        key={tier}
                        onClick={() => setSelectedTier(tier)}
                        className={`
                          relative cursor-pointer rounded-lg p-4 transition-all duration-200 border-2
                          ${isSelected 
                            ? `bg-gradient-to-r ${theme.gradient} border-white/30` 
                            : 'bg-transparent border-gray-600 hover:border-gray-500'
                          }
                        `}
                      >
                        {isPopular && (
                          <div className={`absolute -top-2 left-1/2 transform -translate-x-1/2 px-2 py-1 bg-gradient-to-r ${theme.gradient} text-white text-xs rounded-full font-semibold`}>
                            Most Popular
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className={`text-lg font-bold mb-1 capitalize ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                              {tier}
                            </h4>
                            <div className={`text-2xl font-bold ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                              ₹{details.price.toLocaleString('en-IN')}
                            </div>
                          </div>
                          <div className={`text-right text-sm ${isSelected ? 'text-white/90' : 'text-gray-400'}`}>
                            {details.features.slice(0, 2).map((feature, index) => (
                              <div key={index} className="flex items-center space-x-2 mb-1">
                                <FaCheck className={`text-xs ${isSelected ? 'text-white' : theme.text}`} />
                                <span>{feature}</span>
                              </div>
                            ))}
                            {details.features.length > 2 && (
                              <div className="text-xs opacity-75">+{details.features.length - 2} more</div>
                            )}
                          </div>
                        </div>
                        
                        {isSelected && (
                          <div className="absolute top-2 right-2">
                            <FaCheck className="text-white text-sm" />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Payment Methods */}
              <div className="mb-6">
                <h3 className="text-xl font-bold text-white text-center mb-4">Payment Methods</h3>
                <div className="grid grid-cols-2 gap-3">
                  {paymentMethods.map((method) => {
                    const isSelected = selectedPaymentMethod === method.id;
                    
                    return (
                      <div
                        key={method.id}
                        onClick={() => setSelectedPaymentMethod(method.id)}
                        className={`
                          relative cursor-pointer rounded-lg p-3 transition-all duration-200 border-2
                          ${isSelected 
                            ? `bg-gradient-to-r ${theme.gradient} border-white/30` 
                            : 'bg-transparent border-gray-600 hover:border-gray-500'
                          }
                        `}
                      >
                        {method.popular && (
                          <div className={`absolute -top-1 -right-1 px-1 py-0.5 bg-gradient-to-r ${theme.gradient} text-white text-xs rounded-full font-semibold`}>
                            Popular
                          </div>
                        )}
                        
                        <div className="text-center">
                          <div className={`text-xl mb-1 ${isSelected ? 'text-white' : 'text-gray-300'}`}>
                            {method.icon}
                          </div>
                          <h4 className={`font-semibold text-xs mb-1 ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                            {method.name}
                          </h4>
                          <p className={`text-xs ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>
                            Fee: {method.fee}
                          </p>
                        </div>
                        
                        {isSelected && (
                          <div className="absolute top-1 right-1">
                            <FaCheck className="text-white text-xs" />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Payment Summary */}
              {selectedPaymentMethod && (
                <div className={`bg-transparent border-2 ${theme.border} rounded-lg p-4`}>
                  <div className="flex items-center justify-center mb-3">
                    <FaLock className={`text-lg ${theme.text} mr-2`} />
                    <h3 className="text-lg font-bold text-white">Secure Checkout</h3>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Course:</span>
                      <span className="text-white font-semibold text-sm">{courseData.name}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Plan:</span>
                      <span className="text-white capitalize text-sm">{selectedTier}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300 text-sm">Payment:</span>
                      <span className="text-white text-sm">{paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}</span>
                    </div>
                    <div className="border-t border-gray-600 pt-2">
                      <div className="flex justify-between items-center">
                        <span className="text-base font-semibold text-gray-300">Total:</span>
                        <span className="text-xl font-bold text-white">
                          ₹{pricingTiers[selectedTier].price.toLocaleString('en-IN')}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <button className={`w-full py-3 px-4 bg-gradient-to-r ${theme.gradient} text-white font-semibold rounded-lg hover:opacity-90 transition-all duration-200`}>
                    Complete Payment
                  </button>
                  
                  <div className="mt-3 flex items-center justify-center space-x-3 text-xs text-gray-400">
                    <div className="flex items-center space-x-1">
                      <FaLock className="text-green-400" />
                      <span>SSL Secured</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <FaCheck className="text-green-400" />
                      <span>256-bit</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PaymentModal;
