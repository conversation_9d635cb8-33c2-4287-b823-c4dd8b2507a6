import React, { useState } from 'react';
import { FaFileAlt, FaDownload, FaCalendarAlt, FaChartLine } from 'react-icons/fa';

const ReportsSection = () => {
  const [downloading, setDownloading] = useState(null);
  
  const downloadReport = (report) => {
    setDownloading(report.id);
    
    // Simulate download process
    setTimeout(() => {
      // Create a blob object representing the file
      const content = `This is a simulated ${report.title} file content.\n\nGenerated on: ${new Date().toLocaleString()}\nReport type: ${report.type}\nDescription: ${report.description}`;
      
      const blob = new Blob([content], { type: report.type === 'PDF' ? 'application/pdf' : 'application/vnd.ms-excel' });
      const url = URL.createObjectURL(blob);
      
      // Create a temporary anchor element and trigger download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${report.title.replace(/ /g, '_')}.${report.type.toLowerCase()}`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      setDownloading(null);
    }, 1000); // Simulate 1 second download time
  };
  // Mock reports data
  const reports = [
    { 
      id: 1, 
      title: 'Monthly Enrollment Report', 
      description: 'Summary of student enrollments for the current month',
      date: 'November 2023',
      type: 'PDF',
      size: '1.2 MB'
    },
    { 
      id: 2, 
      title: 'Course Completion Analysis', 
      description: 'Analysis of course completion rates across all courses',
      date: 'October 2023',
      type: 'Excel',
      size: '3.5 MB'
    },
    { 
      id: 3, 
      title: 'Revenue Report', 
      description: 'Financial summary of course sales and revenue',
      date: 'Q3 2023',
      type: 'PDF',
      size: '2.1 MB'
    },
    { 
      id: 4, 
      title: 'Student Engagement Metrics', 
      description: 'Analysis of student activity and engagement',
      date: 'October 2023',
      type: 'PDF',
      size: '1.8 MB'
    },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Reports</h2>
        <div className="flex space-x-2">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center">
            <FaChartLine className="mr-2" />
            Generate Report
          </button>
        </div>
      </div>

      {/* Report Filters */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
            <select className="border rounded-md px-3 py-2 w-full">
              <option>All Reports</option>
              <option>Enrollment</option>
              <option>Completion</option>
              <option>Revenue</option>
              <option>Engagement</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
            <div className="flex items-center border rounded-md px-3 py-2">
              <FaCalendarAlt className="text-gray-400 mr-2" />
              <span className="text-sm">Last 30 days</span>
            </div>
          </div>
          <div className="ml-auto">
            <button className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded-md">
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      {/* Reports List */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {reports.map((report) => (
              <tr key={report.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <FaFileAlt className="text-blue-500" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{report.title}</div>
                      <div className="text-sm text-gray-500">{report.description}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{report.date}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    report.type === 'PDF' ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {report.type}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {report.size}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button 
                    onClick={() => downloadReport(report)}
                    className={`${downloading === report.id ? 'text-gray-400' : 'text-blue-600 hover:text-blue-900'} flex items-center justify-end`}
                    disabled={downloading === report.id}
                  >
                    <FaDownload className="mr-1" />
                    {downloading === report.id ? 'Downloading...' : 'Download'}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ReportsSection;