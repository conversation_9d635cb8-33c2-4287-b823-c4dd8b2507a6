import React, { useState } from 'react';
import { FaUser<PERSON>ircle, FaBell, FaCog, FaSignOutAlt } from 'react-icons/fa';

const Navbar = () => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      message: 'New student registration',
      time: '5 minutes ago',
      read: false
    },
    {
      id: 2,
      message: 'New course review submitted',
      time: '1 hour ago',
      read: false
    },
    {
      id: 3,
      message: 'System update completed',
      time: '3 hours ago',
      read: false
    }
  ]);
  
  const unreadCount = notifications.filter(n => !n.read).length;
  
  const markAsRead = (id) => {
    setNotifications(notifications.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    ));
  };
  
  const markAllAsRead = () => {
    setNotifications(notifications.map(notification => ({ ...notification, read: true })));
  };
  return (
    <header className="bg-white shadow-sm">
      <div className="flex items-center justify-between px-6 py-3">
        <h1 className="text-xl font-semibold text-gray-800">Admin Dashboard</h1>
        
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <div className="relative">
            <button 
              className="text-gray-500 hover:text-gray-700"
              onClick={() => setShowNotifications(!showNotifications)}
            >
              <FaBell className="w-5 h-5" />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
                  {unreadCount}
                </span>
              )}
            </button>
            
            {/* Notification Dropdown */}
            {showNotifications && (
              <div className="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-10">
                <div className="p-3 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-semibold text-gray-700">Notifications</h3>
                    {unreadCount > 0 && (
                      <button 
                        onClick={markAllAsRead}
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        Mark all as read
                      </button>
                    )}
                  </div>
                </div>
                
                <div className="max-h-64 overflow-y-auto">
                  {notifications.length === 0 ? (
                    <div className="py-4 px-3 text-sm text-gray-500 text-center">
                      No notifications
                    </div>
                  ) : (
                    <ul>
                      {notifications.map(notification => (
                        <li 
                          key={notification.id} 
                          className={`px-4 py-3 border-b border-gray-100 hover:bg-gray-50 ${!notification.read ? 'bg-blue-50' : ''}`}
                          onClick={() => markAsRead(notification.id)}
                        >
                          <div className="flex justify-between">
                            <p className={`text-sm ${!notification.read ? 'font-semibold' : 'text-gray-700'}`}>
                              {notification.message}
                            </p>
                            <span className="text-xs text-gray-500">{notification.time}</span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                
                <div className="p-2 border-t border-gray-200 text-center">
                  <button className="text-xs text-blue-600 hover:text-blue-800">
                    View all notifications
                  </button>
                </div>
              </div>
            )}
          </div>
          
          {/* Settings */}
          <button className="text-gray-500 hover:text-gray-700">
            <FaCog className="w-5 h-5" />
          </button>
          
          {/* User Menu */}
          <div className="relative">
            <div className="flex items-center space-x-3 bg-gray-100 rounded-full py-1 px-3 cursor-pointer hover:bg-gray-200">
              <FaUserCircle className="text-gray-600 text-xl" />
              <div className="flex flex-col">
                <span className="text-sm font-medium text-gray-700">Admin User</span>
                <span className="text-xs text-gray-500">Administrator</span>
              </div>
              <FaSignOutAlt className="text-gray-500 ml-2" />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Navbar;