config:
  target: "http://localhost:8000"
  phases:
    - duration: 20
      arrivalRate: 5
      rampTo: 10
  defaults:
    headers:
      Content-Type: "application/json"
      Cookie: "accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.UvRVp0bnOqTTAyciiajSNwNe6r8rfsQ5A_C28UNFfmY; refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2ODZiZmI5MTRlNDY0MGVmNGQ0MmIzNzEiLCJpYXQiOjE3NTI3NDI0NTgsImV4cCI6MTc1Mjc0MzM1OH0.MXeZQ22HQXhaIEgqVOrLAp08k7OGTwRYooVz1XgQGbA"

scenarios:
  - name: "Two Sum JS Execution"
    flow:
      - post:
          url: "/api/v1/problem/execute/run"
          json:
            problemId: "686eff7cf316799e2a87f1b6"
            language: "javascript"
            code: |
              const fs = require('fs');

              const inputData = fs.readFileSync('/app/input.txt', 'utf-8').trim();
              const [line1, line2] = inputData.split('\n');

              const nums = line1.trim().split(' ').map(Number);
              const target = parseInt(line2.trim());

              const map = new Map();

              for (let i = 0; i < nums.length; i++) {
                const complement = target - nums[i];
                if (map.has(complement)) {
                  console.log(`${map.get(complement)} ${i}`);
                  break;
                }
                map.set(nums[i], i);
              }
