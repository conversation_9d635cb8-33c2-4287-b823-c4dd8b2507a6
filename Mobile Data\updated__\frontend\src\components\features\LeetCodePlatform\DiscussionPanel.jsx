import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MessageCircle, 
  ThumbsUp, 
  ThumbsDown, 
  Reply, 
  Send,
  Filter,
  Clock,
  User,
  Award,
  Pin,
  Flag
} from 'lucide-react';

const DiscussionPanel = ({ isDarkMode }) => {
  const [newComment, setNewComment] = useState('');
  const [sortBy, setSortBy] = useState('popular');
  const [replyingTo, setReplyingTo] = useState(null);
  const [replyText, setReplyText] = useState('');

  // Sample discussion data
  const discussions = [
    {
      id: 1,
      author: {
        name: 'CodeMaster2024',
        avatar: '👨‍💻',
        reputation: 15420,
        badge: 'Expert'
      },
      content: 'Great problem! Here\'s my O(n) solution using HashMap. The key insight is to store the complement while iterating through the array.',
      code: `var twoSum = function(nums, target) {
    const map = new Map();
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
};`,
      timestamp: '2 hours ago',
      upvotes: 45,
      downvotes: 2,
      replies: [
        {
          id: 11,
          author: {
            name: 'NewbieCoder',
            avatar: '🧑‍🎓',
            reputation: 250,
            badge: 'Beginner'
          },
          content: 'Thanks for the explanation! Can you explain why we check for the complement first before adding to the map?',
          timestamp: '1 hour ago',
          upvotes: 8,
          downvotes: 0
        },
        {
          id: 12,
          author: {
            name: 'CodeMaster2024',
            avatar: '👨‍💻',
            reputation: 15420,
            badge: 'Expert'
          },
          content: 'Good question! We check first to avoid using the same element twice. If we added to the map first, we might return [i, i] for cases where target = 2 * nums[i].',
          timestamp: '45 minutes ago',
          upvotes: 12,
          downvotes: 0
        }
      ],
      isPinned: true,
      tags: ['HashMap', 'Optimal', 'JavaScript']
    },
    {
      id: 2,
      author: {
        name: 'PythonGuru',
        avatar: '🐍',
        reputation: 8750,
        badge: 'Advanced'
      },
      content: 'Here\'s a clean Python solution with detailed comments. Time complexity: O(n), Space complexity: O(n)',
      code: `def twoSum(nums, target):
    """
    Find two numbers that add up to target
    """
    seen = {}  # value -> index mapping
    
    for i, num in enumerate(nums):
        complement = target - num
        if complement in seen:
            return [seen[complement], i]
        seen[num] = i
    
    return []  # No solution found`,
      timestamp: '4 hours ago',
      upvotes: 32,
      downvotes: 1,
      replies: [],
      isPinned: false,
      tags: ['Python', 'Clean Code', 'Comments']
    },
    {
      id: 3,
      author: {
        name: 'AlgoExplorer',
        avatar: '🔍',
        reputation: 5200,
        badge: 'Intermediate'
      },
      content: 'For beginners: here\'s the brute force approach first, then the optimized version. Understanding both helps grasp the improvement!',
      code: `// Brute Force - O(n²)
var twoSumBrute = function(nums, target) {
    for (let i = 0; i < nums.length; i++) {
        for (let j = i + 1; j < nums.length; j++) {
            if (nums[i] + nums[j] === target) {
                return [i, j];
            }
        }
    }
};

// Optimized - O(n)
var twoSumOptimal = function(nums, target) {
    const map = new Map();
    for (let i = 0; i < nums.length; i++) {
        const complement = target - nums[i];
        if (map.has(complement)) {
            return [map.get(complement), i];
        }
        map.set(nums[i], i);
    }
};`,
      timestamp: '6 hours ago',
      upvotes: 28,
      downvotes: 3,
      replies: [
        {
          id: 31,
          author: {
            name: 'StudentDev',
            avatar: '📚',
            reputation: 120,
            badge: 'Beginner'
          },
          content: 'This comparison is really helpful! I can see why the HashMap approach is much better.',
          timestamp: '3 hours ago',
          upvotes: 5,
          downvotes: 0
        }
      ],
      isPinned: false,
      tags: ['Educational', 'Comparison', 'Beginner-Friendly']
    }
  ];

  const handleSubmitComment = () => {
    if (newComment.trim()) {
      // Handle comment submission
      console.log('Submitting comment:', newComment);
      setNewComment('');
    }
  };

  const handleSubmitReply = (discussionId) => {
    if (replyText.trim()) {
      // Handle reply submission
      console.log('Submitting reply to:', discussionId, replyText);
      setReplyText('');
      setReplyingTo(null);
    }
  };

  const getBadgeColor = (badge) => {
    switch (badge) {
      case 'Expert':
        return 'bg-purple-500 text-white';
      case 'Advanced':
        return 'bg-blue-500 text-white';
      case 'Intermediate':
        return 'bg-green-500 text-white';
      case 'Beginner':
        return 'bg-gray-500 text-white';
      default:
        return 'bg-gray-400 text-white';
    }
  };

  const formatReputation = (rep) => {
    if (rep >= 1000) {
      return `${(rep / 1000).toFixed(1)}k`;
    }
    return rep.toString();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="p-6 h-full overflow-auto"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <MessageCircle className="text-blue-500" size={24} />
          <h2 className="text-xl font-bold">Discussion</h2>
          <span className={`text-sm px-2 py-1 rounded-full ${
            isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-600'
          }`}>
            {discussions.length} posts
          </span>
        </div>
        
        {/* Sort Options */}
        <div className="flex items-center gap-2">
          <Filter size={16} className={isDarkMode ? 'text-gray-400' : 'text-gray-600'} />
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className={`px-3 py-1 text-sm rounded border ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="popular">Most Popular</option>
            <option value="recent">Most Recent</option>
            <option value="oldest">Oldest First</option>
          </select>
        </div>
      </div>

      {/* Add Comment */}
      <div className={`p-4 rounded-lg border mb-6 ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        <h3 className="font-semibold mb-3">Add to Discussion</h3>
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Share your solution, ask a question, or help others..."
          className={`w-full h-24 px-3 py-2 rounded-lg border text-sm resize-none ${
            isDarkMode
              ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
              : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
          } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
        />
        <div className="flex justify-between items-center mt-3">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <span>💡</span>
            <span>Tip: Include code snippets and explain your approach</span>
          </div>
          <button
            onClick={handleSubmitComment}
            disabled={!newComment.trim()}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
              newComment.trim()
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-400 text-gray-200 cursor-not-allowed'
            }`}
          >
            <Send size={16} />
            Post
          </button>
        </div>
      </div>

      {/* Discussion Posts */}
      <div className="space-y-6">
        {discussions.map((discussion) => (
          <motion.div
            key={discussion.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`border rounded-lg overflow-hidden ${
              isDarkMode ? 'border-gray-700' : 'border-gray-200'
            } ${discussion.isPinned ? 'ring-2 ring-yellow-500/20' : ''}`}
          >
            {/* Post Header */}
            <div className={`p-4 border-b ${
              isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
            }`}>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  {discussion.isPinned && (
                    <Pin size={16} className="text-yellow-500" />
                  )}
                  <div className="text-2xl">{discussion.author.avatar}</div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">{discussion.author.name}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${getBadgeColor(discussion.author.badge)}`}>
                        {discussion.author.badge}
                      </span>
                      <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {formatReputation(discussion.author.reputation)} rep
                      </span>
                    </div>
                    <div className={`text-xs flex items-center gap-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      <Clock size={12} />
                      {discussion.timestamp}
                    </div>
                  </div>
                </div>
                
                <button className={`p-1 rounded transition-colors ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300'
                    : 'text-gray-600 hover:text-gray-700'
                }`}>
                  <Flag size={16} />
                </button>
              </div>
            </div>

            {/* Post Content */}
            <div className="p-4">
              <p className={`mb-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {discussion.content}
              </p>

              {/* Code Block */}
              {discussion.code && (
                <div className={`rounded-lg border overflow-hidden mb-4 ${
                  isDarkMode ? 'border-gray-700' : 'border-gray-200'
                }`}>
                  <pre className={`p-4 text-sm font-mono overflow-auto ${
                    isDarkMode ? 'bg-gray-900 text-gray-300' : 'bg-gray-50 text-gray-800'
                  }`}>
                    {discussion.code}
                  </pre>
                </div>
              )}

              {/* Tags */}
              {discussion.tags && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {discussion.tags.map((tag) => (
                    <span
                      key={tag}
                      className={`px-2 py-1 text-xs rounded-full ${
                        isDarkMode
                          ? 'bg-blue-900/30 text-blue-400 border border-blue-700/30'
                          : 'bg-blue-100 text-blue-700 border border-blue-200'
                      }`}
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <button className="flex items-center gap-2 text-sm text-green-500 hover:text-green-600 transition-colors">
                    <ThumbsUp size={16} />
                    {discussion.upvotes}
                  </button>
                  <button className="flex items-center gap-2 text-sm text-red-500 hover:text-red-600 transition-colors">
                    <ThumbsDown size={16} />
                    {discussion.downvotes}
                  </button>
                  <button
                    onClick={() => setReplyingTo(replyingTo === discussion.id ? null : discussion.id)}
                    className={`flex items-center gap-2 text-sm transition-colors ${
                      isDarkMode
                        ? 'text-gray-400 hover:text-gray-300'
                        : 'text-gray-600 hover:text-gray-700'
                    }`}
                  >
                    <Reply size={16} />
                    Reply
                  </button>
                </div>
                
                {discussion.replies.length > 0 && (
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {discussion.replies.length} {discussion.replies.length === 1 ? 'reply' : 'replies'}
                  </span>
                )}
              </div>

              {/* Reply Form */}
              {replyingTo === discussion.id && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
                >
                  <textarea
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                    placeholder="Write your reply..."
                    className={`w-full h-20 px-3 py-2 rounded-lg border text-sm resize-none ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
                  />
                  <div className="flex justify-end gap-2 mt-2">
                    <button
                      onClick={() => setReplyingTo(null)}
                      className={`px-3 py-1 text-sm rounded transition-colors ${
                        isDarkMode
                          ? 'text-gray-400 hover:text-gray-300'
                          : 'text-gray-600 hover:text-gray-700'
                      }`}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={() => handleSubmitReply(discussion.id)}
                      disabled={!replyText.trim()}
                      className={`px-3 py-1 text-sm rounded transition-colors ${
                        replyText.trim()
                          ? 'bg-blue-600 text-white hover:bg-blue-700'
                          : 'bg-gray-400 text-gray-200 cursor-not-allowed'
                      }`}
                    >
                      Reply
                    </button>
                  </div>
                </motion.div>
              )}

              {/* Replies */}
              {discussion.replies.length > 0 && (
                <div className="mt-4 space-y-3">
                  {discussion.replies.map((reply) => (
                    <div
                      key={reply.id}
                      className={`pl-4 border-l-2 ${
                        isDarkMode ? 'border-gray-700' : 'border-gray-200'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className="text-lg">{reply.author.avatar}</div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">{reply.author.name}</span>
                            <span className={`text-xs px-2 py-0.5 rounded-full ${getBadgeColor(reply.author.badge)}`}>
                              {reply.author.badge}
                            </span>
                            <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              {reply.timestamp}
                            </span>
                          </div>
                          <p className={`text-sm mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                            {reply.content}
                          </p>
                          <div className="flex items-center gap-3">
                            <button className="flex items-center gap-1 text-xs text-green-500 hover:text-green-600 transition-colors">
                              <ThumbsUp size={12} />
                              {reply.upvotes}
                            </button>
                            <button className="flex items-center gap-1 text-xs text-red-500 hover:text-red-600 transition-colors">
                              <ThumbsDown size={12} />
                              {reply.downvotes}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default DiscussionPanel;
