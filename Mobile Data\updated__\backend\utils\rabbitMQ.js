// // utils/rabbitMQ.js
// import amqp from "amqplib";
// import { Buffer } from "node:buffer";
// import fs from "fs";
// import path from "path";

// const QUEUE_NAME = "jobQueue";
// let channel = null;
// const JOB_STORE_FILE = path.resolve("jobStore.json");

// let jobStore = new Map();

// // RabbitMQ for stroing jobs

// function loadJobsFromDisk() {
//   if (fs.existsSync(JOB_STORE_FILE)) {
//     const data = fs.readFileSync(JOB_STORE_FILE, "utf-8");
//     const parsed = JSON.parse(data);
//     jobStore = new Map(Object.entries(parsed));
//     console.log(`Loaded ${jobStore.size} jobs from disk`);
//   }
// }

// function saveJobsToDisk() {
//   const obj = Object.fromEntries(jobStore);
//   fs.writeFileSync(JOB_STORE_FILE, JSON.stringify(obj, null, 2));
// }

// // rabbitmq connection
// export async function connectRabbitMQ() {
//   const conn = await amqp.connect("amqp://guest:guest@localhost");
//   channel = await conn.createChannel();
//   await channel.assertQueue(QUEUE_NAME, { durable: true });
//   loadJobsFromDisk(); 
//   console.log(`Connected to RabbitMQ & asserted queue`);
// }

// // save and send jobs 
// export async function publishToQueue(job) {
//   if (!channel) throw new Error("RabbitMQ not initialized");
//   jobStore.set(job.id, job);
//   saveJobsToDisk();
//   return channel.sendToQueue(QUEUE_NAME, Buffer.from(JSON.stringify(job)), {
//     persistent: true,
//   });
// }

// export async function consumeFromQueue(handler) {
//   if (!channel) throw new Error("RabbitMQ not initialized");

//   channel.consume(QUEUE_NAME, async (msg) => {
//     if (msg !== null) {
//       const content = msg.content.toString();

//       const { id } = JSON.parse(content);
//       const job = jobStore.get(id);

//       if (!job) {
//         console.warn(`⚠️ Job ${id} not found in store`);
//         channel.ack(msg);
//         return;
//       }

//       try {
//         await handler(job);
//       } finally {
//         channel.ack(msg);
//       }
//     }
//   });
// }


// export async function deleteJobData(id) {
//   jobStore.delete(id);
//   saveJobsToDisk();
// }

// export async function getJobData(id) {
//   return jobStore.get(id);
// }

// export async function updateJobData(job) {
//   jobStore.set(job.id, job);
//   saveJobsToDisk();
// }
