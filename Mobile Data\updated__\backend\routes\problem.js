import express from "express";
import { isAuthenticated } from "../middleware/auth.js";
import {
  createProblem,
  getAllProblems,
  getProblemById,
  runCodeHandler,
  submitCode,
} from "../controllers/problem.js";

const problemRouter = express.Router();

problemRouter.post("/create", isAuthenticated, createProblem);

problemRouter.get("/all-problems", isAuthenticated, getAllProblems);

problemRouter.get("/:problemId", isAuthenticated, getProblemById);


problemRouter.post("/execute/run", isAuthenticated, runCodeHandler);

problemRouter.post("/execute/submit", isAuthenticated, submitCode);




export default problemRouter;
