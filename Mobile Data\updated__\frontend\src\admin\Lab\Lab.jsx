import { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import axios from "axios";
import TestCases from "./TestCases";
import { DeleteModal } from "../../components/ui";

const Lab = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editQuestion, setEditQuestion] = useState(null);

  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [difficulty, setDifficulty] = useState("Easy");

  const [questions, setQuestions] = useState([]);
  const [tags, setTags] = useState([]);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6;

  useEffect(() => {
    fetchQuestions();
  }, []);

  const fetchQuestions = async () => {
    try {
      const res = await axios.get("http://localhost:8000/api/questions");
      setQuestions(res.data);
    } catch (error) {
      console.error("Error fetching questions:", error);
    }
  };

  const handleAddQuestion = async () => {
    if (!title || !description || !tags) {
      return;
    }

    const newQuestion = {
      title,
      description,
      difficulty,
      tags: tags, // Send as a string, backend will split it
    };

    try {
      const response = await axios.post(
        "http://localhost:8000/api/questions",
        newQuestion,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      // console.log("Server response:", response);

      if (response.status === 201) {
        // console.log("✅ Question added successfully");
        // alert("Question added successfully!");
        fetchQuestions();
        setTitle("");
        setDescription("");
        setDifficulty("Easy");
        setTags("");
      }
    } catch (error) {
      console.error("Error adding question:", error);
      //   alert(error.response?.data?.error || "Something went wrong!");
    }
  };

  const openEditModal = (question) => {
    console.log(question);
    setEditQuestion(question);
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setEditQuestion(null);
  };

  const handleDelete = async (id) => {
    if (id) {
      try {
        console.log(id);
        let res = await axios.delete(
          `http://localhost:8000/api/questions/${id}`
        );
        fetchQuestions();
      } catch (error) {
        console.error("Error deleting question:", error);
      }
    }
  };

  const handleUpdateQuestion = async () => {
    if (
      !editQuestion.title ||
      !editQuestion.description ||
      !editQuestion.tags
    ) {
      alert("All fields are required!");
      return;
    }

    try {
      await axios.put(
        `http://localhost:8000/api/questions/${editQuestion._id}`,
        editQuestion,
        {
          headers: { "Content-Type": "application/json" },
        }
      );
      // alert("Question updated successfully!");
      fetchQuestions();
      closeEditModal();
    } catch (error) {
      console.error("Error updating question:", error);
    }
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = questions.slice(indexOfFirstItem, indexOfLastItem);

  const nextPage = () => {
    if (indexOfLastItem < questions.length) setCurrentPage(currentPage + 1);
  };

  const prevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const openDeleteModal = () => {
    setIsOpen(true);
  };

  const closeDeleteModal = () => {
    setIsOpen(false);
  };

  const deleteConfirmation = () => {
    alert("Question List Deleted!");
    setIsOpen(false);
  };

  return (
    <>
      <div className="bg-gray-100 min-h-screen py-10">
        <div className="max-w-5xl mx-auto bg-white text-black p-6 rounded-lg shadow-md border border-gray-300">
          <h2 className="text-center text-2xl font-bold mb-6 text-blue-500">
            Add New Question
          </h2>

          {/* Form Fields */}
          <div className="mb-4">
            <label className="block text-sm font-semibold mb-1">Title</label>
            <input
              type="text"
              className="w-full p-2 text-lg border border-gray-300 rounded-md outline-none"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter question title"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-semibold mb-1">
              Description
            </label>
            <textarea
              className="w-full p-2 text-lg border border-gray-300 rounded-md outline-none min-h-[100px]"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter question description"
            ></textarea>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-semibold mb-1">
              Difficulty
            </label>
            <select
              className="w-full p-2 text-lg border border-gray-300 rounded-md outline-none"
              value={difficulty}
              onChange={(e) => setDifficulty(e.target.value)}
            >
              <option>Easy</option>
              <option>Medium</option>
              <option>Hard</option>
            </select>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-semibold mb-1">
              Tags (comma separated)
            </label>
            <input
              type="text"
              className="w-full p-2 text-lg border border-gray-300 rounded-md outline-none"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              placeholder="e.g. array, graph, dp"
            />
          </div>

          <button
            onClick={handleAddQuestion}
            className="w-full bg-blue-500 text-white p-3 text-lg font-bold rounded-md hover:bg-blue-600 transition"
          >
            Save Question
          </button>
        </div>

        <br />
        <br />
        <div className="w-11/12 mx-auto p-5 mt-8 bg-white text-gray-800 rounded-lg shadow-lg border border-gray-300">
          <h2 className="text-center text-2xl mb-4 text-blue-500 font-semibold">
            Questions List
          </h2>

          <div className="overflow-x-auto">
            <table className="w-full border-collapse text-sm sm:text-base">
              <thead className="bg-blue-900 text-white">
                <tr>
                  <th className="p-3 text-left">Id</th>
                  <th className="p-3 text-left">Title</th>
                  <th className="p-3 text-left">Description</th>
                  <th className="p-3 text-center">Difficulty</th>
                  <th className="p-3 text-left">Tags</th>
                  <th className="p-3 text-left">Action</th>
                </tr>
              </thead>
              <tbody>
                {currentItems.map((q) => (
                  <tr
                    key={q._id}
                    className="odd:bg-gray-50 even:bg-white border-b border-gray-200"
                  >
                    <td className="p-3">{q._id}</td>
                    <td className="p-3">{q.title}</td>
                    <td className="p-3 break-words max-w-[200px]">
                      {q.description}
                    </td>
                    <td className="p-3 text-center font-bold">
                      {q.difficulty}
                    </td>
                    <td className="p-3">{q.tags.join(", ")}</td>
                    <td className="p-3">
                      <div className="flex gap-2">
                        <button
                          onClick={() => openEditModal(q)}
                          className="bg-yellow-500 px-3 py-1 font-bold text-white rounded hover:bg-yellow-600 transition"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(q._id)}
                          className="bg-red-500 px-3 py-1 font-bold text-white rounded hover:bg-red-600 transition"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex flex-wrap justify-center gap-4 items-center mt-4">
            <button
              onClick={prevPage}
              disabled={currentPage === 1}
              className="px-4 py-2 rounded bg-blue-500 text-white font-bold hover:bg-blue-600 disabled:bg-gray-400"
            >
              ⬅ Prev
            </button>
            <span className="text-lg font-bold text-blue-500 bg-blue-100 px-4 py-2 rounded">
              Page {currentPage}
            </span>
            <button
              onClick={nextPage}
              disabled={indexOfLastItem >= questions.length}
              className="px-4 py-2 rounded bg-blue-500 text-white font-bold hover:bg-blue-600 disabled:bg-gray-400"
            >
              Next ➡
            </button>
          </div>
        </div>

        <DeleteModal
          title={"Delete Question List"}
          message={"Are You Sure Want to Delete?"}
          onClose={closeDeleteModal}
          isOpen={isOpen}
          onConfirm={deleteConfirmation}
        />

        <br />
        {/* Test cases form */}
        <TestCases />
      </div>
      {editModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center">
          <div className="bg-white p-6 rounded-lg w-96 shadow-lg">
            <h2 className="text-lg font-bold mb-4">Edit Question</h2>
            <input
              type="text"
              value={editQuestion.title}
              onChange={(e) =>
                setEditQuestion({ ...editQuestion, title: e.target.value })
              }
              className="w-full p-2 border border-gray-300 rounded mb-3"
            />
            <textarea
              value={editQuestion.description}
              onChange={(e) =>
                setEditQuestion({
                  ...editQuestion,
                  description: e.target.value,
                })
              }
              className="w-full p-2 border border-gray-300 rounded mb-3"
            />
            <select
              value={editQuestion.difficulty}
              onChange={(e) =>
                setEditQuestion({ ...editQuestion, difficulty: e.target.value })
              }
              className="w-full p-2 border border-gray-300 rounded mb-3"
            >
              <option>Easy</option>
              <option>Medium</option>
              <option>Hard</option>
            </select>
            <input
              type="text"
              value={editQuestion.tags.join(", ")}
              onChange={(e) =>
                setEditQuestion({
                  ...editQuestion,
                  tags: e.target.value.split(", "),
                })
              }
              className="w-full p-2 border border-gray-300 rounded mb-3"
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={closeEditModal}
                className="px-4 py-2 bg-gray-300 rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleUpdateQuestion}
                className="px-4 py-2 bg-blue-500 text-white rounded"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Lab;
