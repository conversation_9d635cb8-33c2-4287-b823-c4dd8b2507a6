import React, { useState } from "react";

const ProjectsComponent = () => {
  const [activeProject, setActiveProject] = useState(null);

  const projects = [
    {
      id: "url-shortener",
      title: "URL Shortener Service",
      description: "Design and implement a URL shortening service like TinyURL or bit.ly.",
      image: "/images/interviewlogo.png",
      difficulty: "Beginner",
      technologies: ["Node.js", "Express", "MongoDB", "Redis"],
      requirements: [
        "Create a URL shortening algorithm",
        "Design API endpoints for creating and redirecting short URLs",
        "Implement database schema for storing URL mappings",
        "Add analytics features (clicks, geographic data)"
      ],
      implementationDetails: `
        <h4>Key Components:</h4>
        <ul>
          <li>Hash function to generate short URLs</li>
          <li>Database for storing URL mappings</li>
          <li>Cache for frequently accessed URLs</li>
          <li>API endpoints for URL creation and redirection</li>
        </ul>
        <h4>System Architecture:</h4>
        <p>The URL shortener service consists of web servers behind a load balancer, a database cluster for storing URL mappings, and a cache layer for fast lookups.</p>
      `
    },
    {
      id: "chat-application",
      title: "Real-time Chat System",
      description: "Design a scalable real-time chat application similar to Discord or Slack.",
      image: "/images/images.jpeg",
      difficulty: "Intermediate",
      technologies: ["WebSockets", "Node.js", "React", "MongoDB"],
      requirements: [
        "Support for direct messaging and group chats",
        "Real-time message delivery using WebSockets",
        "Message persistence in database",
        "User presence indicators (online/offline status)"
      ],
      implementationDetails: `
        <h4>Key Components:</h4>
        <ul>
          <li>WebSocket server for real-time communication</li>
          <li>Authentication system for users</li>
          <li>Message queue for reliability</li>
          <li>Database for message history</li>
        </ul>
        <h4>System Architecture:</h4>
        <p>The chat system uses WebSockets for real-time communication, with a message queue to ensure reliability. Messages are stored in a database for history and offline delivery.</p>
      `
    },
    {
      id: "video-streaming",
      title: "Video Streaming Platform",
      description: "Design a video streaming service similar to YouTube or Netflix.",
      image: "/images/images.jpeg",
      difficulty: "Advanced",
      technologies: ["AWS S3", "CDN", "Encoding Service", "Node.js"],
      requirements: [
        "Video upload and processing pipeline",
        "Adaptive bitrate streaming",
        "Content delivery network integration",
        "Recommendation system"
      ],
      implementationDetails: `
        <h4>Key Components:</h4>
        <ul>
          <li>Video encoding service</li>
          <li>Storage system for video files</li>
          <li>CDN for global content delivery</li>
          <li>Metadata database for video information</li>
        </ul>
        <h4>System Architecture:</h4>
        <p>The video platform includes upload servers, encoding workers, a distributed storage system, and a CDN for content delivery. Metadata is stored separately for quick access.</p>
      `
    },
    {
      id: "distributed-cache",
      title: "Distributed Cache System",
      description: "Design a distributed caching system similar to Redis or Memcached.",
      image: "/images/images.jpeg",
      difficulty: "Advanced",
      technologies: ["C++", "Go", "Consistent Hashing"],
      requirements: [
        "Distributed key-value store",
        "Consistent hashing for data partitioning",
        "Replication for fault tolerance",
        "Eviction policies (LRU, LFU)"
      ],
      implementationDetails: `
        <h4>Key Components:</h4>
        <ul>
          <li>Key-value storage mechanism</li>
          <li>Consistent hashing ring</li>
          <li>Replication manager</li>
          <li>Cache eviction algorithms</li>
        </ul>
        <h4>System Architecture:</h4>
        <p>The distributed cache uses a cluster of nodes that communicate via a gossip protocol. Data is partitioned using consistent hashing and replicated for reliability.</p>
      `
    }
  ];

  // CSS Animation Styles
  const animationStyles = `
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    .project-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .project-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    }
    .project-details {
      animation: fadeIn 0.5s ease forwards;
    }
  `;

  return (
    <div className="py-8">
      <style dangerouslySetInnerHTML={{ __html: animationStyles }} />
      
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">System Design Projects</h2>
        <p className="text-gray-600 mb-6">
          Practice your system design skills by working on these real-world inspired projects.
          Each project comes with requirements and implementation details to guide your learning.
        </p>
      </div>

      {activeProject ? (
        <div className="project-details bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-2xl font-bold text-gray-800">{projects.find(p => p.id === activeProject).title}</h3>
            <button 
              className="px-4 py-2 bg-gray-200 rounded-lg hover:bg-gray-300 text-gray-700 transition"
              onClick={() => setActiveProject(null)}
            >
              Back to Projects
            </button>
          </div>

          <div className="mb-6">
            <span className="inline-block bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full mr-2">
              {projects.find(p => p.id === activeProject).difficulty}
            </span>
            {projects.find(p => p.id === activeProject).technologies.map(tech => (
              <span key={tech} className="inline-block bg-gray-100 text-gray-800 text-sm font-medium px-3 py-1 rounded-full mr-2">
                {tech}
              </span>
            ))}
          </div>

          <p className="text-gray-700 mb-6">
            {projects.find(p => p.id === activeProject).description}
          </p>

          <div className="mb-6">
            <h4 className="text-lg font-semibold mb-3">Requirements</h4>
            <ul className="list-disc pl-6 space-y-2">
              {projects.find(p => p.id === activeProject).requirements.map((req, i) => (
                <li key={i} className="text-gray-700">{req}</li>
              ))}
            </ul>
          </div>

          <div className="prose max-w-none" dangerouslySetInnerHTML={{ 
            __html: projects.find(p => p.id === activeProject).implementationDetails 
          }} />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          {projects.map((project, index) => (
            <div 
              key={project.id} 
              className="project-card bg-white rounded-xl overflow-hidden shadow-md border border-gray-100"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => setActiveProject(project.id)}
            >
              <div className="h-40 overflow-hidden">
                <img 
                  src={project.image || "/images/image.png"} 
                  alt={project.title}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-5">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-bold text-lg text-gray-800">{project.title}</h3>
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                    project.difficulty === 'Beginner' ? 'bg-green-100 text-green-800' :
                    project.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {project.difficulty}
                  </span>
                </div>
                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{project.description}</p>
                <button 
                  className="w-full py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    setActiveProject(project.id);
                  }}
                >
                  View Project Details
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectsComponent;
