import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { FloatingParticles, GradientOrbs } from "./common/BackgroundEffects";
import { HeroTerminal, HeroTrustIndicators } from "./common/HeroComponents";
import HeroStats from "./common/components/HeroStats";

const HeroSection = ({ itemVariants }) => {
  return (
    <div
      variants={itemVariants}
      className="relative overflow-hidden bg-first text-white min-h-screen flex items-center pt-4 px-4 sm:px-6 lg:px-8"
    >
      <div className="absolute inset-0 overflow-hidden">
        {/* Subtle background effects can be added here */}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto pt-24 pb-16">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Column - Text Content */}
          <HeroContent itemVariants={itemVariants} />

          {/* Right Column - Interactive Elements */}
          <HeroTerminal itemVariants={itemVariants} />
        </div>

        {/* Stats Section */}
        <HeroStats itemVariants={itemVariants} />
      </div>
    </div>
  );
};

const HeroContent = ({ itemVariants }) => (
  <div className="text-center lg:text-left">
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="mb-6"
    >
      <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight tracking-tight">
        Master the Art of{" "}
        <span className="bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 bg-clip-text text-transparent">
          Coding
        </span>
      </h1>
    </motion.div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="mb-8"
    >
      <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-gray-300 leading-relaxed">
        Practice, Learn, and Excel
      </h2>
    </motion.div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="mb-12"
    >
      <p className="text-lg sm:text-xl text-gray-400 max-w-2xl mx-auto lg:mx-0 leading-relaxed">
        Join millions of developers worldwide. Solve real-world problems, 
        master data structures & algorithms, and build your coding career 
        with our comprehensive learning platform.
      </p>
    </motion.div>

    <HeroCTA />
    <HeroTrustIndicators />
  </div>
);

const HeroCTA = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: 0.6 }}
    className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start items-center mb-16"
  >
    <Link
      to="/courses"
      className="group relative inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-blue-900 to-blue-700 text-white font-semibold rounded-lg text-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
    >
      <span className="relative z-10">Start Learning</span>
      <svg 
        className="ml-2 w-5 h-5 transition-transform group-hover:translate-x-1" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
      </svg>
    </Link>

    <Link
      to="/contests"
      className="inline-flex items-center justify-center px-8 py-4 border-2 border-gray-600 text-gray-300 font-semibold rounded-lg text-lg hover:bg-gray-700 hover:border-gray-500 hover:text-white transition-all duration-300"
    >
      Join Contests
    </Link>

    <div className="flex items-center gap-3 text-center">
      <div className="text-3xl font-bold text-white">1M+</div>
      <div className="text-sm text-gray-400">
        <div>Active</div>
        <div>Learners</div>
      </div>
    </div>
  </motion.div>
);

export default HeroSection;
