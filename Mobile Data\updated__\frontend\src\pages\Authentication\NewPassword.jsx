import { useState } from "react";
import toast from "react-hot-toast";
import axios from "axios";
import { Spinner } from "../../components/ui";

const NewPassword = () => {
  const [newPassword, setNewPassword] = useState("");
  const [loading, setLoading] = useState(false);

  const validateFields = () => {
    if (!newPassword) {
      toast.error("Please Enter Password");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;
    setLoading(true);
    try {
      const { data } = await axios.post(
        `http://localhost:8000/api/v1/auth/new-password`,
        { newPassword },
        { withCredentials: true }
      );

      if (data.success) {
        toast.success(data.message);
        setNewPassword("");
      } else {
        toast.error(data.error);
      }
    } catch (err) {
      toast.error(err.response?.data?.message || "Something went wrong!");
    } finally {
      setLoading(false);
    }
  };
  return (
    <div>
      <div className="forgot-password-container">
        <div className="form-container-forgot">
          <h2>Enter New Password</h2>
          {/* <p>
            Enter your email address to receive a link to reset your password.
          </p> */}

          <form onSubmit={handleSubmit}>
            <input
              type="password"
              className="email-input-forgot"
              placeholder="Enter New Password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              required
            />
            <button
              type="submit"
              className="submit-btn-forgot"
              disabled={loading}
            >
              {loading ? <Spinner /> : "New Password"}
            </button>
          </form>

          {/* <Link to="/log-In" style={{textDecoration:"none"}} className="back-link-forgot">
            Back to Login
          </Link> */}
        </div>
      </div>
    </div>
  );
};

export default NewPassword;
