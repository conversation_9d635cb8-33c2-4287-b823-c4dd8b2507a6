import { useState } from "react";
import {
  FaLinkedin,
  FaGithubSquare,
  FaExternalLinkAlt,
  FaCheck,
} from "react-icons/fa";
import { FaSquareTwitter } from "react-icons/fa6";
import toast from "react-hot-toast";
import axiosInstance from "../../utils/axiosInstance";
import { userLoggedIn } from "../../features/authSlice";
import { useDispatch } from "react-redux";

const SocialAccount = () => {
  const [socialLinks, setSocialLinks] = useState({
    linkedin: "",
    github: "",
    twitter: "",
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [savedLinks, setSavedLinks] = useState({
    linkedin: "",
    github: "",
    twitter: "",
  });

  const dispatch = useDispatch()

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSocialLinks((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateFields = () => {
    let newErrors = {};

    const urlRegex =
      /^(https?:\/\/)?(www\.)?([a-zA-Z0-9]+)([-._]?[a-zA-Z0-9])*\.[a-zA-Z]{2,}(:\d+)?(\/.*)?$/;

    if (!socialLinks.linkedin) {
      newErrors.linkedin = "LinkedIn URL is required";
    } else if (!urlRegex.test(socialLinks.linkedin)) {
      newErrors.linkedin = "Invalid LinkedIn URL";
    }

    if (!socialLinks.github) {
      newErrors.github = "GitHub URL is required";
    } else if (!urlRegex.test(socialLinks.github)) {
      newErrors.github = "Invalid GitHub URL";
    }

    if (!socialLinks.twitter) {
      newErrors.twitter = "Twitter URL is required";
    } else if (!urlRegex.test(socialLinks.twitter)) {
      newErrors.twitter = "Invalid Twitter URL";
    }

    setErrors(newErrors);
    setTimeout(() => setErrors({}), 3000);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;
    setLoading(true);

    try {
      const { data } = await axiosInstance.patch(
        "/auth/user/social-accounts",
        socialLinks,
        { withCredentials: true }
      );

      if (data.success) {
        toast.success(data.message || "Social media links updated!");
        setSavedLinks(socialLinks);
        setSocialLinks({
          linkedin: "",
          github: "",
          twitter: "",
        });
        dispatch(
          userLoggedIn({
            accessToken: data.accessToken,
            user: data.user,
          })
        );
      } else {
        toast.error(data.message || "Failed to update links.");
      }
    } catch (error) {
      const message = error.response?.data?.message;
      toast.error(message);
    } finally {
      setLoading(false);
    }
  };

  const socialPlatforms = [
    {
      name: "linkedin",
      label: "LinkedIn",
      icon: FaLinkedin,
      placeholder: "https://linkedin.com/in/your-profile",
      color: "from-blue-600 to-blue-800",
      bgColor: "from-blue-900/60 via-blue-800/70 to-slate-800/80",
      borderColor: "border-blue-500/30 hover:border-blue-400/50",
      focusColor: "focus:border-blue-400/50 focus:ring-blue-400/20",
    },
    {
      name: "github",
      label: "GitHub",
      icon: FaGithubSquare,
      placeholder: "https://github.com/your-username",
      color: "from-gray-600 to-gray-800",
      bgColor: "from-gray-900/60 via-slate-800/70 to-gray-800/80",
      borderColor: "border-gray-500/30 hover:border-gray-400/50",
      focusColor: "focus:border-gray-400/50 focus:ring-gray-400/20",
    },
    {
      name: "twitter",
      label: "Twitter/X",
      icon: FaSquareTwitter,
      placeholder: "https://twitter.com/your-handle",
      color: "from-sky-600 to-sky-800",
      bgColor: "from-sky-900/60 via-cyan-800/70 to-slate-800/80",
      borderColor: "border-sky-500/30 hover:border-sky-400/50",
      focusColor: "focus:border-sky-400/50 focus:ring-sky-400/20",
    },
  ];

  return (
    <div className="min-h-screen w-full py-6 px-4 md:px-6 lg:px-8 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-200 via-purple-200 to-cyan-200 bg-clip-text text-transparent mb-2">
            🔗 Social Accounts
          </h1>
          <p className="text-slate-400 text-lg">
            Connect your professional social media profiles
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Social Platform Cards */}
          {socialPlatforms.map((platform) => {
            const IconComponent = platform.icon;
            const hasValue = socialLinks[platform.name];
            const isSaved = savedLinks[platform.name];

            return (
              <div
                key={platform.name}
                className={`relative bg-gradient-to-br ${platform.bgColor} backdrop-blur-xl rounded-2xl shadow-xl p-6 border ${platform.borderColor} transition-all duration-500 group overflow-hidden`}
              >
                {/* Animated background */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-white/10 to-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-0"></div>

                <div className="relative z-10 flex flex-col md:flex-row items-start md:items-center gap-4">
                  {/* Platform Icon & Info */}
                  <div className="flex items-center gap-4 min-w-0 flex-1">
                    <div
                      className={`w-16 h-16 bg-gradient-to-r ${platform.color} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                    >
                      <IconComponent className="text-white text-2xl" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <h3 className="text-xl font-bold text-white mb-1 flex items-center gap-2">
                        {platform.label}
                        {isSaved && (
                          <span className="inline-flex items-center gap-1 px-2 py-1 bg-green-500/20 text-green-300 text-xs font-semibold rounded-full border border-green-500/30">
                            <FaCheck className="text-xs" />
                            Connected
                          </span>
                        )}
                      </h3>

                      <div className="space-y-3">
                        <input
                          type="url"
                          name={platform.name}
                          placeholder={platform.placeholder}
                          value={socialLinks[platform.name]}
                          onChange={handleChange}
                          className={`w-full px-4 py-3 bg-slate-900/50 backdrop-blur-sm border border-slate-600/50 rounded-xl text-white placeholder-slate-400 ${platform.focusColor} focus:ring-2 focus:outline-none transition-all duration-300`}
                        />

                        {errors[platform.name] && (
                          <p className="text-red-400 text-sm flex items-center gap-1">
                            ⚠️ {errors[platform.name]}
                          </p>
                        )}

                        {hasValue && !errors[platform.name] && (
                          <div className="flex items-center gap-2 text-sm text-slate-300">
                            <FaExternalLinkAlt className="text-xs" />
                            <span>
                              Preview:{" "}
                              {socialLinks[platform.name].substring(0, 50)}...
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}

          {/* Submit Button */}
          <div className="pt-6">
            <button
              type="submit"
              disabled={loading}
              className="w-full relative px-8 py-4 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-600 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-300 font-bold text-lg tracking-wide disabled:opacity-50 disabled:cursor-not-allowed group/btn overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-700 via-blue-700 to-cyan-700 opacity-0 group-hover/btn:opacity-100 transition-opacity duration-300"></div>
              <span className="relative z-10 flex items-center justify-center gap-2">
                {loading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    Saving Links...
                  </>
                ) : (
                  <>🔗 Save Social Links</>
                )}
              </span>
            </button>
          </div>
        </form>

        {/* Tips Section */}
        <div className="mt-8 bg-gradient-to-br from-emerald-900/60 via-slate-800/80 to-teal-900/60 backdrop-blur-xl rounded-2xl shadow-xl p-6 border border-emerald-500/30 hover:border-emerald-400/50 transition-all duration-500">
          <h3 className="text-xl font-bold text-emerald-200 mb-4 flex items-center gap-2">
            💡 Social Media Tips
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-300">
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Use your professional profile URLs</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Keep your profiles updated and active</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Make sure your profiles are public</span>
            </div>
            <div className="flex items-start gap-2">
              <span className="text-emerald-400">✓</span>
              <span>Use consistent usernames across platforms</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialAccount;
