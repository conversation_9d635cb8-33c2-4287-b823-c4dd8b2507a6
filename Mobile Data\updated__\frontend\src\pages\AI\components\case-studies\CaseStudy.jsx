import React, { useState } from 'react';
import CaseStudyCard from './CaseStudyCard';
import CategorySelector from './CategorySelector';
import { categories } from './caseStudiesData';

const CaseStudy = () => {
  const [selectedCategory, setSelectedCategory] = useState(categories[0].id);

  const selectedStudies = categories.find(
    (category) => category.id === selectedCategory
  ).studies;

  return (
    <div className="container mx-auto px-4 py-8 bg-transparent text-gray-900 dark:text-gray-100">
      <h2 className="text-2xl font-bold mb-6">AI Case Studies</h2>
      <CategorySelector
        categories={categories}
        selectedCategory={selectedCategory}
        onSelectCategory={setSelectedCategory}
      />
      <div className="space-y-8">
        {selectedStudies.map((study, index) => (
          <CaseStudyCard 
            key={index} 
            study={study} 
            index={index}
          />
        ))}
      </div>
    </div>
  );
};

export default CaseStudy;