import { useEffect, useState } from "react";
import axiosInstance from "../../../utils/axiosInstance";

import { useDispatch, useSelector } from "react-redux";

const LAB_TYPES = ["DSA", "Web Dev", "AI", "ML"];
const LABEL_OPTIONS = [
  "Hot",
  "Popular",
  "Career",
  "Advanced",
  "In Demand",
  "New",
];

const CreateLab = ({ theme }) => {
  const [formData, setFormData] = useState({
    name: "",
    title: "",
    description: "",
    labType: "",
    learningPoints: "",
    labels: [],
    isActive: true,
    iconFile: null,
    iconPreview: "",
  });
  const disPatch = useDispatch();
  // const labs = useSelector((state) => state.lab.labs);
  // console.log("redux", labs);

  const [labs, setLabs] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editLabId, setEditLabId] = useState(null);

  const fetchLabs = async () => {
    try {
      const res = await axiosInstance.get(
        "http://localhost:8000/api/v1/lab/all-labs"
      );
      // console.log(res);
      disPatch(setLabs(res.data.labs));

      setLabs(res.data.labs);
    } catch (err) {
      console.error("Failed to fetch labs", err);
    }
  };

  useEffect(() => {
    fetchLabs();
  }, [disPatch]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (type === "checkbox") {
      setFormData((prev) => ({
        ...prev,
        labels: checked
          ? [...prev.labels, value]
          : prev.labels.filter((label) => label !== value),
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleIconChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({
        ...prev,
        iconFile: file,
        iconPreview: URL.createObjectURL(file),
      }));
    }
  };

  const handleEdit = (lab) => {
    setFormData({
      name: lab.name,
      title: lab.title || "",
      description: lab.description || "",
      labType: lab.labType || "",
      learningPoints: lab.learningPoints?.join(", ") || "",
      labels: lab.labels || [],
      isActive: true,
      iconFile: null,
      iconPreview: lab.icon?.secure_url || "",
    });
    setEditLabId(lab._id);
    setIsEditing(true);
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Are you sure you want to delete this lab?")) {
      return;
    }
    try {
      await axiosInstance.delete(
        `http://localhost:8000/api/v1/lab/delete/${id}`
      );
      alert("Lab deleted successfully!");
      fetchLabs();
      if (isEditing && editLabId === id) {
        setIsEditing(false);
        setEditLabId(null);
        setFormData({
          name: "",
          title: "",
          description: "",
          labType: "",
          learningPoints: "",
          labels: [],
          isActive: true,
          iconFile: null,
          iconPreview: "",
        });
      }
    } catch (error) {
      console.error("Error deleting lab:", error);
      alert(error.response?.data?.message || "Failed to delete lab.");
    }
  };

  const handleSubmit = async () => {
    try {
      if (!formData.name || !formData.labType) {
        alert("Please fill all required fields.");
        return;
      }

      const formPayload = new FormData();
      formPayload.append("name", formData.name.trim());
      formPayload.append("title", formData.title.trim());
      formPayload.append("description", formData.description.trim());
      formPayload.append("labType", formData.labType);
      formPayload.append("isActive", formData.isActive);

      if (formData.iconFile) {
        formPayload.append("icon", formData.iconFile);
      }

      formData.learningPoints
        .split(",")
        .map((lp) => lp.trim())
        .filter(Boolean)
        .forEach((lp) => formPayload.append("learningPoints[]", lp));

      formData.labels.forEach((label) => formPayload.append("labels[]", label));

      if (isEditing && editLabId) {
        await axiosInstance.put(
          `http://localhost:8000/api/v1/lab/update/${editLabId}`,
          formPayload,
          {
            headers: { "Content-Type": "multipart/form-data" },
          }
        );
        alert("Lab updated successfully!");
      } else {
        await axiosInstance.post(
          "http://localhost:8000/api/v1/lab/create",
          formPayload,
          {
            headers: { "Content-Type": "multipart/form-data" },
          }
        );
        alert("Lab created successfully!");
      }

      fetchLabs();
      setFormData({
        name: "",
        title: "",
        description: "",
        labType: "",
        learningPoints: "",
        labels: [],
        isActive: true,
        iconFile: null,
        iconPreview: "",
      });
      setIsEditing(false);
      setEditLabId(null);
    } catch (error) {
      console.error("Error submitting lab:", error);
      alert(error.response?.data?.message || "Submission failed");
    }
  };

  const inputBaseClass =
    "p-2 border rounded placeholder-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500";
  const darkInputClass =
    "bg-gray-700 text-white border-gray-600 placeholder-gray-400";
  const lightInputClass =
    "bg-white text-gray-900 border-gray-300 placeholder-gray-600";

  return (
    <div className="max-w-6xl mx-auto py-10 px-4">
      {/* Lab Form */}
      <div
        className={`p-6 rounded shadow-md mb-8 ${
          theme === "dark" ? "bg-gray-800 text-white" : "bg-white text-gray-900"
        }`}
      >
        <h2 className="text-2xl font-bold mb-4 text-blue-600">
          {isEditing ? "Edit Lab" : "Create New Lab"}
        </h2>
        <div className="grid gap-4 md:grid-cols-2">
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Lab Name"
            className={`${inputBaseClass} ${
              theme === "dark" ? darkInputClass : lightInputClass
            }`}
          />
          <input
            type="text"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="Lab Title"
            className={`${inputBaseClass} ${
              theme === "dark" ? darkInputClass : lightInputClass
            }`}
          />
          <select
            name="labType"
            value={formData.labType}
            onChange={handleChange}
            className={`${inputBaseClass} ${
              theme === "dark" ? darkInputClass : lightInputClass
            }`}
          >
            <option value="">Select Lab Type</option>
            {LAB_TYPES.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
          <input
            type="text"
            name="learningPoints"
            value={formData.learningPoints}
            onChange={handleChange}
            placeholder="Learning Points (comma-separated)"
            className={`${inputBaseClass} ${
              theme === "dark" ? darkInputClass : lightInputClass
            }`}
          />
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Description"
            className={`p-2 border rounded col-span-2 resize-none ${
              theme === "dark" ? darkInputClass : lightInputClass
            }`}
          />

          <div className="col-span-2 mt-2">
            <label className="block font-semibold mb-1">Upload Icon:</label>
            <input
              type="file"
              accept="image/*"
              onChange={handleIconChange}
              className={`p-1 border rounded ${
                theme === "dark" ? darkInputClass : lightInputClass
              }`}
            />
            {formData.iconPreview && (
              <img
                src={formData.iconPreview}
                alt="Preview"
                className="mt-2 h-20 w-20 object-contain border rounded"
              />
            )}
          </div>
        </div>
        <div className="mt-4">
          <label className="block font-semibold mb-1">Labels:</label>
          <div className="flex flex-wrap gap-3">
            {LABEL_OPTIONS.map((label) => (
              <label key={label} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  value={label}
                  checked={formData.labels.includes(label)}
                  onChange={handleChange}
                  className={`${
                    theme === "dark" ? "accent-blue-400" : "accent-blue-600"
                  }`}
                />
                {label}
              </label>
            ))}
          </div>
        </div>
        <div className="flex items-center gap-4 mt-6">
          <button
            onClick={handleSubmit}
            className="px-6 py-2 bg-blue-500 text-white font-bold rounded hover:bg-blue-600"
          >
            {isEditing ? "Update Lab" : "Create Lab"}
          </button>

          {isEditing && (
            <button
              onClick={() => {
                setIsEditing(false);
                setEditLabId(null);
                setFormData({
                  name: "",
                  title: "",
                  description: "",
                  labType: "",
                  learningPoints: "",
                  labels: [],
                  isActive: true,
                  iconFile: null,
                  iconPreview: "",
                });
              }}
              className="px-4 py-2 bg-gray-400 text-white rounded hover:bg-gray-500"
            >
              Cancel Edit
            </button>
          )}
        </div>
      </div>

      {/* Lab List */}
      <div
        className={`p-6 rounded shadow-md ${
          theme === "dark" ? "bg-gray-800 text-white" : "bg-white text-gray-900"
        }`}
      >
        <h2 className="text-xl font-bold mb-4 text-blue-600">All Labs</h2>

        {labs.length === 0 ? (
          <p className="text-gray-500">No labs available.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm border border-gray-200">
              <thead
                className={`border-b ${
                  theme === "dark" ? "bg-gray-700 text-white" : "bg-gray-100"
                }`}
              >
                <tr>
                  <th className="p-2 text-left">Icon</th>
                  <th className="p-2 text-left">Name</th>
                  <th className="p-2 text-left">Type</th>
                  <th className="p-2 text-left">Labels</th>
                  <th className="p-2 text-left">Learning Points</th>
                  <th className="p-2 text-left">Slug</th>
                  <th className="p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {labs.map((lab) => (
                  <tr
                    key={lab._id}
                    className={`border-b hover:${
                      theme === "dark" ? "bg-gray-700" : "bg-gray-50"
                    }`}
                  >
                    <td className="p-2">
                      {lab.icon?.secure_url ? (
                        <img
                          src={lab.icon.secure_url}
                          alt="Icon"
                          className="h-10 w-10 object-contain rounded"
                        />
                      ) : (
                        "No Icon"
                      )}
                    </td>
                    <td className="p-2">{lab.name}</td>
                    <td className="p-2">{lab.labType}</td>
                    <td className="p-2">{lab.labels?.join(", ")}</td>
                    <td className="p-2">{lab.learningPoints?.join(", ")}</td>
                    <td className="p-2">{lab.slug}</td>
                    <td className="p-2 flex gap-4">
                      <button
                        className="text-blue-500 hover:underline"
                        onClick={() => handleEdit(lab)}
                      >
                        Edit
                      </button>
                      <button
                        className="text-red-500 hover:underline"
                        onClick={() => handleDelete(lab._id)}
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default CreateLab;
