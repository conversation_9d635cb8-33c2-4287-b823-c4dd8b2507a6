import { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import toast from "react-hot-toast";
import { useSelector, useDispatch } from "react-redux";
import { userLoggedIn, userLoggedOut } from "../../features/authSlice";
import axiosInstance from "../../utils/axiosInstance";
const logo = "/upcoding_logo.png";

const ReusableNavbar = ({
  toggleSidebar,
  showSidebarToggle = true,
  isSidebarOpen = false,
  title = "",
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isRegisterOpen, setIsRegisterOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Check if current page should show the company logo when sidebar is open
  const shouldShowCompanyLogo = () => {
    if (!isSidebarOpen || !showSidebarToggle) return false;

    // Check route patterns
    const courseRoutes = [
      "/pythoncourse",
      "/fullstack-course",
      "/datascience-course",
      "/ml-course",
      "/ai-course",
    ];

    // Check title patterns
    const courseTitles = [
      "Python Master",
      "Full Stack Development",
      "Data Science Master",
      "Machine Learning",
      "Artificial Intelligence",
    ];

    return (
      courseRoutes.some((route) => location.pathname.includes(route)) ||
      (title && courseTitles.some((courseTitle) => title.includes(courseTitle)))
    );
  };

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const { data } = await axiosInstance.get(
          "/auth/me",
          {
            withCredentials: true,
          }
        );
        dispatch(
          userLoggedIn({ accessToken: data.accessToken, user: data.user })
        );
      } catch (error) {
        console.error(
          "Error fetching user:",
          error.response?.data || error.message
        );
      }
    };

    // Scroll detection for navbar appearance
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setScrolled(scrollTop > 50);
    };

    window.addEventListener("scroll", handleScroll);
    fetchUser();

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [dispatch]);

  const handleLogin = () => {
    navigate("/login");
  };

  const handleLogout = async () => {
    try {
      await axiosInstance.post(
        "auth/logout",
        {},
        {
          withCredentials: true,
        }
      );
      dispatch(userLoggedOut());
      toast.success("Logged out successfully!");
      navigate("/");
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Failed to logout!");
    }
  };

  const handleRegister = () => {
    setIsRegisterOpen(!isRegisterOpen);
  };

  return (
    <>
      <motion.header
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
       

        className={`fixed top-0 left-0 right-0 z-50 border-b border-gray-400 rounded-b-2xl ${
          scrolled
            ? "bg-top backdrop-blur-md shadow-lg"
            : "bg-transparent "

       
        }`}
        style={{ height: "80px" }} // Fixed navbar height
      >

        <div className="w-full max-w-screen-xl mx-auto px-2 py-2 h-full"> {/* Increased padding and added h-full */}
          <div className="flex items-center justify-between h-full">
            {/* Left side - Company Logo (conditional), Hamburger and Main Logo */}
            <div className="flex items-center space-x-2 h-full"> {/* Restored space-x-2 and added h-full */}

        
              {/* Company Logo - Only show when sidebar is open and on course pages */}
              {shouldShowCompanyLogo() && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center"
                >
                  <Link to="/" className="flex items-center">
                    <motion.img
                      whileHover={{ scale: 1.05 }}
                      src={logo}
                      alt="Upcoding"
                      className="h-[48px] w-[48px]"
                    />
                  </Link>
                </motion.div>
              )}
              {/* Hamburger Menu - Only show if showSidebarToggle is true */}
              {showSidebarToggle && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={toggleSidebar}
                  className={`p-1.5 rounded-lg transition-colors ${
                    scrolled
                      ? "text-white hover:bg-white/10"
                      : "text-white hover:bg-white/10"
                  }`}
                >
                  <svg
                    className="w-6 h-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </motion.button>
              )}
              {/* Main Logo and Title */}
              <Link to="/" className="flex items-center space-x-2">
                {!shouldShowCompanyLogo() && (
                  <motion.img
                    whileHover={{ scale: 1.05 }}
                    src={logo}
                    alt="Upcoding"
                    className="h-[60px] w-[120px]"
                  />
                )}
                {/* Removed the title from navbar */}
              </Link>
            </div>

            {/* Right side - User controls */}
            <div className="flex items-center space-x-2">
              {" "}
              {/* Reduced space-x-4 to space-x-2 */}
              {user ? (
                <div className="relative">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsProfileOpen(!isProfileOpen)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-full transition-colors ${
                      scrolled
                        ? "text-white hover:bg-white/10"
                        : "text-white hover:bg-white/10"
                    }`}
                  >
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                      
                      <img src={user?.image} alt="" className="w-full h-full rounded-full"/>
                    </div>
                    <span className="hidden md:block font-medium">
                      {user.name}
                    </span>
                    <svg
                      className={`w-4 h-4 transition-transform ${
                        isProfileOpen ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </motion.button>

                  <AnimatePresence>
                    {isProfileOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: 0.2 }}
                        className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
                      >
                        <Link
                          to="/profile"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          onClick={() => setIsProfileOpen(false)}
                        >
                          <i className="fas fa-user mr-2"></i>
                          Profile
                        </Link>
                        <Link
                          to="/dashboard"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          onClick={() => setIsProfileOpen(false)}
                        >
                          <i className="fas fa-tachometer-alt mr-2"></i>
                          Dashboard
                        </Link>
                        <hr className="my-1" />
                        <button
                          onClick={() => {
                            handleLogout();
                            setIsProfileOpen(false);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                        >
                          <i className="fas fa-sign-out-alt mr-2"></i>
                          Logout
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleLogin}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      scrolled
                        ? "text-white hover:bg-white/10"
                        : "text-white hover:bg-white/10"
                    }`}
                  >
                    Login
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleRegister}
                    className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-colors"
                  >
                    Register
                  </motion.button>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.header>

      {/* Registration Modal */}
      <AnimatePresence>
        {isRegisterOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
            onClick={() => setIsRegisterOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-xl p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                Choose Registration Type
              </h3>
              <div className="space-y-3">
                <button
                  onClick={() => {
                    navigate("/internship");
                    setIsRegisterOpen(false);
                  }}
                  className="w-full p-3 text-left rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium text-gray-900">
                    Internship Registration
                  </div>
                  <div className="text-sm text-gray-600">
                    Join our internship program
                  </div>
                </button>
                <button
                  onClick={() => {
                    navigate("/course-registration");
                    setIsRegisterOpen(false);
                  }}
                  className="w-full p-3 text-left rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <div className="font-medium text-gray-900">
                    Course Registration
                  </div>
                  <div className="text-sm text-gray-600">
                    Register for our courses
                  </div>
                </button>
              </div>
              <button
                onClick={() => setIsRegisterOpen(false)}
                className="mt-4 w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default ReusableNavbar;
