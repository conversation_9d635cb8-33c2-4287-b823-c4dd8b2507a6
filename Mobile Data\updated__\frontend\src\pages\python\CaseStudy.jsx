import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

const CaseStudy = () => {
  const [activeSection, setActiveSection] = useState("data-expression");
  const [activeCaseStudy, setActiveCaseStudy] = useState(null);

  const topics = [
    {
      title: "Data and Expression",
      icon: "fas fa-database",
      sectionId: "data-expression",
      color: "from-blue-500 to-cyan-500",
    },
    {
      title: "Control Structure",
      icon: "fas fa-project-diagram",
      sectionId: "control-structure",
      color: "from-purple-500 to-pink-500",
    },
    { 
      title: "List", 
      icon: "fas fa-stream", 
      sectionId: "list",
      color: "from-green-500 to-teal-500",
    },
    { 
      title: "Functions", 
      icon: "fas fa-code", 
      sectionId: "functions",
      color: "from-orange-500 to-red-500",
    },
    { 
      title: "Objects", 
      icon: "fas fa-cube", 
      sectionId: "objects",
      color: "from-indigo-500 to-purple-500",
    },
    {
      title: "Modular Design",
      icon: "fas fa-th-large",
      sectionId: "modular-design",
      color: "from-pink-500 to-rose-500",
    },
    { 
      title: "Text Files", 
      icon: "fas fa-file-alt", 
      sectionId: "text-files",
      color: "from-yellow-500 to-orange-500",
    },
    { 
      title: "Dictionaries", 
      icon: "fas fa-key", 
      sectionId: "dictionaries",
      color: "from-cyan-500 to-blue-500",
    },
    { 
      title: "OOP", 
      icon: "fas fa-object-group", 
      sectionId: "oop",
      color: "from-emerald-500 to-green-500",
    },
    { 
      title: "Recursion", 
      icon: "fas fa-sync-alt", 
      sectionId: "recursion",
      color: "from-violet-500 to-purple-500",
    },
  ];

  // Sample case studies data
  const caseStudies = [
    {
      title: "Case Study 1: Basic Data Types and Variables",
      objective: "Understand and differentiate between basic data types in Python.",
      scenario: "Write a Python script to define variables of different types (integer, float, string, boolean). Print the type of each variable using the type() function.",
      keyConcepts: "Integers, Floats, Strings, Booleans, type() function.",
      solution: `# Defining variables
integer_var = 10
float_var = 10.5
string_var = "Hello, Python!"
boolean_var = True

# Printing types
print(type(integer_var))    # <class 'int'>
print(type(float_var))      # <class 'float'>
print(type(string_var))     # <class 'str'>
print(type(boolean_var))    # <class 'bool'>`,
    },
    {
      title: "Case Study 2: Arithmetic Operations",
      objective: "Perform and understand basic arithmetic operations in Python.",
      scenario: "Create a Python script that takes two numbers as input from the user and performs addition, subtraction, multiplication, division, and modulus operations. Display the results.",
      keyConcepts: "Arithmetic Operators (+, -, *, /, %), input() function.",
      solution: `# Taking input from user
num1 = float(input("Enter first number: "))
num2 = float(input("Enter second number: "))

# Performing operations
addition = num1 + num2
subtraction = num1 - num2
multiplication = num1 * num2
division = num1 / num2 if num2 != 0 else "Cannot divide by zero"
modulus = num1 % num2 if num2 != 0 else "Cannot divide by zero"

# Displaying results
print(f"Addition: {addition}")
print(f"Subtraction: {subtraction}")
print(f"Multiplication: {multiplication}")
print(f"Division: {division}")
print(f"Modulus: {modulus}")`,
    },
  ];

  const [showSolutions, setShowSolutions] = useState(
    Array(caseStudies.length).fill(false)
  );
  const [activeIndex, setActiveIndex] = useState(null);

  const scrollToSection = (section) => {
    const element = document.getElementById(section);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const toggleSolution = (index) => {
    const updatedSolutions = [...showSolutions];
    updatedSolutions[index] = !updatedSolutions[index];
    setShowSolutions(updatedSolutions);
  };

  const toggleChapterContent = (index) => {
    setActiveIndex(activeIndex === index ? null : index);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <motion.div
      className="py-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-7xl mx-auto px-4 md:px-6">
        {/* Header Section */}
        <motion.div variants={itemVariants} className="text-center mb-16">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-block px-6 py-3 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg"
          >
            📚 Python Case Studies
          </motion.div>
          
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
            Practical{" "}
            <span className="bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
              Case Studies
            </span>
          </h2>
          
          <p className="text-xl text-blue-100/80 max-w-3xl mx-auto leading-relaxed mb-4">
            Learn Python through real-world scenarios and hands-on practice
          </p>
          
          <motion.div
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md p-6 max-w-2xl mx-auto"
          >
            <h3 className="text-lg font-semibold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
              🎯 Ready to dive in?
            </h3>
            <p className="text-blue-100/80">
              Click on any topic below to explore comprehensive case studies and start your learning journey!
            </p>
          </motion.div>
        </motion.div>

        {/* Topics Grid */}
        <motion.div variants={itemVariants} className="mb-16">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {topics.map((topic, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                whileHover={{ scale: 1.05, y: -8 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => scrollToSection(topic.sectionId)}
                className="group bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/10 cursor-pointer hover:shadow-2xl transition-all duration-300 text-center relative overflow-hidden"
              >
                {/* Gradient background on hover */}
                <div className={`absolute inset-0 bg-gradient-to-r ${topic.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300`}></div>
                
                <div className={`relative w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${topic.color} flex items-center justify-center text-white text-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                  <i className={topic.icon}></i>
                </div>
                
                <h4 className="relative text-lg font-semibold text-white group-hover:text-blue-100 transition-colors duration-300" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                  {topic.title}
                </h4>
                
                {/* Subtle animation indicator */}
                <motion.div
                  className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={{ scaleX: 0 }}
                  whileHover={{ scaleX: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Case Studies Section */}
        <motion.div variants={itemVariants} className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl shadow-md p-8 md:p-12">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 id="data-expression" className="text-3xl font-bold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                Data and Expression
              </h2>
              <p className="text-blue-100/80">Fundamental concepts and practical applications</p>
            </div>
            <div className="bg-gradient-to-r from-orange-500/20 to-red-500/20 backdrop-blur-sm p-4 rounded-2xl border border-white/10">
              <span className="text-3xl">🐍</span>
            </div>
          </div>
          
          <div className="space-y-6">
            {caseStudies.map((study, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="group bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl overflow-hidden hover:border-white/20 hover:shadow-xl transition-all duration-300"
              >
                <motion.div
                  onClick={() => toggleChapterContent(index)}
                  className="p-6 cursor-pointer hover:bg-gradient-to-r hover:from-white/5 hover:to-orange-500/10 transition-all duration-200 flex items-center justify-between"
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                >
                  <div className="flex items-center space-x-4">
                    <motion.div 
                      className="w-14 h-14 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                      whileHover={{ rotate: 5 }}
                    >
                      {index + 1}
                    </motion.div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-1 group-hover:text-orange-300 transition-colors duration-200" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                        {study.title}
                      </h3>
                      <p className="text-blue-100/60 text-sm">
                        Click to explore this case study
                      </p>
                    </div>
                  </div>
                  
                  <motion.div
                    animate={{ rotate: activeIndex === index ? 180 : 0 }}
                    transition={{ duration: 0.3 }}
                    className="text-2xl text-orange-400 group-hover:text-red-400 transition-colors duration-200"
                  >
                    ↓
                  </motion.div>
                </motion.div>

                <AnimatePresence>
                  {activeIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.4 }}
                      className="overflow-hidden"
                    >
                      <div className="p-6 bg-gradient-to-b from-[#1e293b]/20 to-transparent backdrop-blur-sm border-t border-white/10">
                        <div className="grid lg:grid-cols-2 gap-8">
                          {/* Left Column - Details */}
                          <div className="space-y-6">
                            <motion.div
                              initial={{ x: -20, opacity: 0 }}
                              animate={{ x: 0, opacity: 1 }}
                              transition={{ delay: 0.1 }}
                              className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-5 rounded-xl shadow-sm"
                            >
                              <div className="flex items-center space-x-2 mb-3">
                                <span className="text-2xl">🎯</span>
                                <h4 className="font-semibold text-white text-lg" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Objective</h4>
                              </div>
                              <p className="text-blue-100/80 leading-relaxed">{study.objective}</p>
                            </motion.div>
                            
                            <motion.div
                              initial={{ x: -20, opacity: 0 }}
                              animate={{ x: 0, opacity: 1 }}
                              transition={{ delay: 0.2 }}
                              className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-5 rounded-xl shadow-sm"
                            >
                              <div className="flex items-center space-x-2 mb-3">
                                <span className="text-2xl">📝</span>
                                <h4 className="font-semibold text-white text-lg" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Scenario</h4>
                              </div>
                              <p className="text-blue-100/80 leading-relaxed">{study.scenario}</p>
                            </motion.div>
                            
                            <motion.div
                              initial={{ x: -20, opacity: 0 }}
                              animate={{ x: 0, opacity: 1 }}
                              transition={{ delay: 0.3 }}
                              className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-5 rounded-xl shadow-sm"
                            >
                              <div className="flex items-center space-x-2 mb-3">
                                <span className="text-2xl">🔑</span>
                                <h4 className="font-semibold text-white text-lg" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Key Concepts</h4>
                              </div>
                              <p className="text-blue-100/80 leading-relaxed">{study.keyConcepts}</p>
                            </motion.div>
                          </div>
                          
                          {/* Right Column - Solution */}
                          <div>
                            <motion.div
                              initial={{ x: 20, opacity: 0 }}
                              animate={{ x: 0, opacity: 1 }}
                              transition={{ delay: 0.2 }}
                              className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-5 rounded-xl shadow-sm h-full"
                            >
                              <div className="flex items-center justify-between mb-4">
                                <div className="flex items-center space-x-2">
                                  <span className="text-2xl">💻</span>
                                  <h4 className="font-semibold text-white text-lg" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>Solution</h4>
                                </div>
                                
                                <motion.button
                                  onClick={() => toggleSolution(index)}
                                  whileHover={{ scale: 1.05 }}
                                  whileTap={{ scale: 0.95 }}
                                  className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 text-sm"
                                >
                                  <span className="mr-2">
                                    {showSolutions[index] ? "🙈" : "👁️"}
                                  </span>
                                  {showSolutions[index] ? "Hide Code" : "Show Code"}
                                </motion.button>
                              </div>
                              
                              <AnimatePresence>
                                {showSolutions[index] ? (
                                  <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    exit={{ opacity: 0, y: -20 }}
                                    transition={{ duration: 0.3 }}
                                    className="bg-gray-900 rounded-xl p-4 overflow-x-auto max-h-96 overflow-y-auto"
                                  >
                                    <pre className="text-green-400 text-sm font-mono leading-relaxed">
                                      {study.solution}
                                    </pre>
                                  </motion.div>
                                ) : (
                                  <motion.div
                                    initial={{ opacity: 0 }}
                                    animate={{ opacity: 1 }}
                                    className="bg-gradient-to-br from-[#1e293b]/20 to-orange-500/10 backdrop-blur-sm rounded-xl p-8 text-center border-2 border-dashed border-orange-400/50"
                                  >
                                    <span className="text-6xl mb-4 block">🔒</span>
                                    <p className="text-blue-100/80 font-medium">
                                      Click "Show Code" to reveal the solution
                                    </p>
                                  </motion.div>
                                )}
                              </AnimatePresence>
                            </motion.div>
                          </div>
                        </div>
                        
                        {/* Progress indicator */}
                        <motion.div
                          initial={{ scaleX: 0 }}
                          animate={{ scaleX: 1 }}
                          transition={{ delay: 0.4, duration: 0.6 }}
                          className="mt-6 h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full"
                        />
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>

          {/* Stats Section */}
          <motion.div
            variants={itemVariants}
            className="mt-16 grid md:grid-cols-3 gap-6"
          >
            {[
              {
                icon: "📚",
                number: caseStudies.length,
                label: "Case Studies",
                color: "from-orange-500 to-red-500",
              },
              {
                icon: "🎯",
                number: "100%",
                label: "Practical Focus",
                color: "from-blue-500 to-cyan-500",
              },
              {
                icon: "⭐",
                number: "Expert",
                label: "Level Content",
                color: "from-green-500 to-teal-500",
              },
            ].map((stat, index) => (
              <motion.div
                key={index}
                whileHover={{ y: -5 }}
                className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 p-6 rounded-xl shadow-lg text-center hover:shadow-xl transition-all duration-300"
              >
                <div className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r ${stat.color} flex items-center justify-center text-white text-2xl`}>
                  {stat.icon}
                </div>
                <div className="text-3xl font-bold text-white mb-2" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                  {stat.number}
                </div>
                <div className="text-blue-100/80">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default CaseStudy;
