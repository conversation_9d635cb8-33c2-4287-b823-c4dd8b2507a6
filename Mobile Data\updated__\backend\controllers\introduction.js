import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import Introduction from "../models/introductionModal.js";
import cloudinary from "../utils/cloudinaryConfig.js";
import ErrorHandler from "../utils/ErrorHandler.js";
import { sanitizeObject } from "../utils/sanitizeInput.js";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { redis } from "../utils/redisClient.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const createIntroduction = CatchAsyncError(async (req, res, next) => {
  const {
    lab,
    section,
    courseOverview,
    whyLearning,
    whatYouWillLearn,
    keyTopics,
    toolsAndTechnologies,
    careerImpact,
    conceptAndSkills,
  } = sanitizeObject(req.body, {
    lab: "string",
    section: "string",
    courseOverview: "string",
    whyLearning: "string",
    whatYouWillLearn: "string",
    keyTopics: "array",
    toolsAndTechnologies: "array",
    careerImpact: "string",
    conceptAndSkills: "object",
  });

  console.log("✅ Sanitized Object:", {
    lab,
    section,
    courseOverview,
    whyLearning,
    whatYouWillLearn,
    keyTopics,
    toolsAndTechnologies,
    careerImpact,
    conceptAndSkills,
  });

  if (
    !lab ||
    !section ||
    !courseOverview ||
    !whyLearning ||
    !whatYouWillLearn ||
    !Array.isArray(keyTopics) ||
    keyTopics.length === 0 ||
    !careerImpact ||
    !conceptAndSkills
  ) {
    return next(new ErrorHandler("Missing required fields", 400));
  }

  let toolsArray = toolsAndTechnologies;
  if (!Array.isArray(toolsArray)) {
    toolsArray = [toolsArray];
  }

  const uploadedIcons = [];
  const localFilesToDelete = [];

  try {
    const uploadedTools = await Promise.all(
      toolsArray.map(async (tool, index) => {
        let parsedTool;

        if (typeof tool === "string") {
          try {
            parsedTool = JSON.parse(tool);
          } catch (err) {
            parsedTool = { name: tool };
          }
        } else {
          parsedTool = tool;
        }

        const toolData = {
          name: parsedTool.name || parsedTool,
        };

        const file = req.files?.[index];
        if (file && file.path) {
          localFilesToDelete.push(file.path);
          const result = await cloudinary.uploader.upload(file.path, {
            folder: "lab/icons",
          });

          uploadedIcons.push(result.public_id);

          toolData.icon = {
            public_id: result.public_id,
            secure_url: result.secure_url,
          };
        }

        return toolData;
      })
    );

    const introduction = await Introduction.create({
      lab,
      section,
      courseOverview,
      whyLearning,
      whatYouWillLearn,
      keyTopics,
      toolsAndTechnologies: uploadedTools,
      careerImpact,
      conceptAndSkills,
      createdBy: req.user._id,
    });

    await redis.del("all-introductions");

    return res.status(201).json({
      success: true,
      message: "Introduction created !!",
      introduction,
    });
  } catch (error) {
    await Promise.all(
      uploadedIcons.map((publicId) =>
        cloudinary.uploader.destroy(publicId).catch(() => null)
      )
    );
    return next(new ErrorHandler(error.message, 500));
  } finally {
    for (const filePath of localFilesToDelete) {
      const absolutePath = path.join(__dirname, `../${filePath}`);
      fs.unlink(absolutePath, (err) => {
        if (err) {
          console.error(`Unable to delete ${absolutePath}:`, err.message);
        } else {
          console.log(`Deleted local file ${absolutePath}`);
        }
      });
    }
  }
});

export const updateIntroduction = CatchAsyncError(async (req, res, next) => {
  const introductionId = req.params.id;
  if (!mongoose.Types.ObjectId.isValid(introductionId)) {
    return next(new ErrorHandler("Invalid ID", 400));
  }

  const {
    lab,
    section,
    courseOverview,
    whyMachineLearning,
    whatYouWillLearn,
    keyTopics,
    toolsAndTechnologies,
  } = sanitizeObject(req.body);

  if (
    !lab ||
    !section ||
    !courseOverview ||
    !whyMachineLearning ||
    !whatYouWillLearn ||
    !Array.isArray(keyTopics) ||
    !Array.isArray(toolsAndTechnologies)
  ) {
    return next(new ErrorHandler("Missing required fields", 400));
  }

  const existing = await Introduction.findById(introductionId);
  if (!existing) {
    return next(new ErrorHandler("Introduction not found", 404));
  }

  const uploadedIcons = [];
  const publicIdsToDelete = [];
  const localFilesToDelete = [];

  try {
    const updatedTools = await Promise.all(
      toolsAndTechnologies.map(async (tool) => {
        if (!tool.name || !tool.icon) {
          throw new Error("Each tool must have a name and icon");
        }

        if (tool.icon.startsWith("uploads/")) {
          localFilesToDelete.push(tool.icon);

          const result = await cloudinary.uploader.upload(tool.icon, {
            folder: "lab/icons",
          });

          uploadedIcons.push(result.public_id);
          return {
            name: tool.name,
            icon: {
              public_id: result.public_id,
              secure_url: result.secure_url,
            },
          };
        } else {
          return tool;
        }
      })
    );

    existing.toolsAndTechnologies.forEach((tool) => {
      if (
        !toolsAndTechnologies.some(
          (t) => t.icon.public_id === tool.icon.public_id
        )
      ) {
        publicIdsToDelete.push(tool.icon.public_id);
      }
    });

    const updatedIntro = await Introduction.findByIdAndUpdate(
      introductionId,
      {
        lab,
        section,
        courseOverview,
        whyMachineLearning,
        whatYouWillLearn,
        keyTopics,
        toolsAndTechnologies: updatedTools,
      },
      { new: true, runValidators: true }
    );

    await redis.del("all-introductions");

    return res.status(200).json({
      success: true,
      message: "Introduction updated!",
      introduction: updatedIntro,
    });
  } catch (error) {
    await Promise.all(
      uploadedIcons.map((publicId) =>
        cloudinary.uploader.destroy(publicId).catch(() => null)
      )
    );

    return next(new ErrorHandler(error.message, 500));
  } finally {
    for (const filePath of localFilesToDelete) {
      const absolutePath = path.join(__dirname, `../${filePath}`);
      fs.unlink(absolutePath, (err) => {
        if (err) console.error(`Failed to delete ${absolutePath}`, err.message);
      });
    }

    for (const publicId of publicIdsToDelete) {
      await cloudinary.uploader.destroy(publicId).catch(() => null);
    }
  }
});

export const getIntroduction = CatchAsyncError(async (req, res, next) => {
  const { labId, sectionId } = req.params;
  const cacheKey = `introduction:${labId}:${sectionId}`;

  const cachedData = await redis.get(cacheKey);
  if (cachedData) {
    return res.status(200).json({
      success: true,
      fromCache: true,
      data: cachedData,
    });
  }

  const introduction = await Introduction.findOne({
    lab: labId,
    section: sectionId,
  })
    .populate("createdBy", "name email")
    .populate("lastEditedBy", "name email");

  if (!introduction) {
    return next(new ErrorHandler("Introduction not found", 404));
  }

  await redis.set(cacheKey, introduction, {
    ex: 3600,
  });

  res.status(200).json({
    success: true,
    fromCache: false,
    data: introduction,
  });
});

export const getAllIntroductions = CatchAsyncError(async (req, res, next) => {
  const cacheKey = "introductions:all";

  const cachedData = await redis.get(cacheKey);
  if (cachedData) {
    return res.status(200).json({
      success: true,
      fromCache: true,
      data: cachedData,
    });
  }

  const introductions = await Introduction.find()
    .populate("createdBy", "name email")
    .populate("lastEditedBy", "name email");

  if (!introductions || introductions.length === 0) {
    return next(new ErrorHandler("No introductions found", 404));
  }

  await redisClient.set(cacheKey, introductions, {
    ex: 3600,
  });

  res.status(200).json({
    success: true,
    fromCache: false,
    data: introductions,
  });
});

export const deleteTool = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!id.match(/^[0-9a-fA-F]{24}$/)) {
    return next(new ErrorHandler("Invalid Tool ID", 400));
  }

  const cleanId = sanitizeObject(id);

  const tool = await Introduction.findById(cleanId);
  if (!tool) {
    return next(new ErrorHandler("Tool not found", 404));
  }

  const iconPublicId = tool.icon?.public_id;

  await tool.deleteOne();

  await redis.del("all-introductions");

  if (iconPublicId) {
    await cloudinary.uploader.destroy(iconPublicId);
  }

  res.status(200).json({
    success: true,
    message: "Tool deleted successfully",
  });
});
