// import { getExecutionTimeout } from "../../utils/constants.js";
// import logger from "../../utils/logger.js";
// import { sanitizeInput } from "../../utils/sanitizeExecutionEngine.js";

// import cpp from "../languageHandlers/cpp.js";
// import python from "../languageHandlers/python.js";
// import java from "../languageHandlers/java.js";
// import javascript from "../languageHandlers/javascript.js";

// export const executeCode = async ({ code, language, input = "" }) => {
//   try {
//     if (!code || typeof code !== "string") {
//       throw new Error("Code is required and must be a string.");
//     }

//     if (!["cpp", "python", "java", "javascript"].includes(language)) {
//       throw new Error("Unsupported or missing programming language.");
//     }

//     const cleanCode = sanitizeInput(code);

//     const cleanInput = input?.toString() || "";
//     const timeout = getExecutionTimeout(language);

//     let result = {};

//     switch (language) {
//       case "python":
//         result = await python.execute(cleanCode, cleanInput, { timeout });
//         break;
//       case "cpp":
//         result = await cpp.execute(cleanCode, cleanInput, { timeout });
//         break;
//       case "java":
//         result = await java.execute(cleanCode, cleanInput, { timeout });
//         break;
//       case "javascript":
//         result = await javascript.execute(cleanCode, cleanInput, { timeout });
//         break;
//     }

//     return {
//       success: true,
//       output: result.output || "",
//       error: result.error || null,
//       time: result.time || null,
//       memory: result.memory || null,
//     };
//   } catch (error) {
//     console.error(error);
//     logger.error(error.message);
//     return {
//       success: false,
//       error: error.message,
//       output: "",
//     };
//   }
// };

import engine from "../languageHandlers/engine.js";
import logger from "../../utils/logger.js";
import { sanitizeInput } from "../../utils/sanitizeExecutionEngine.js";
import { getExecutionTimeout } from "../../utils/constants.js";

export const executeCode = async ({ code, language, input = "", labType, timeout }) => {
  try {
    if (!code || typeof code !== "string") {
      throw new Error("Code is required and must be a string.");
    }

    const cleanCode = sanitizeInput(code);
    const cleanInput = input?.toString() || "";
    const finalTimeout = timeout || getExecutionTimeout(language);

    const result = await engine.execute(cleanCode, language, cleanInput, labType, {
      timeout: finalTimeout,
    });

    console.log("🔥 [Engine Result]:", result);


    return {
      success: true,
      output: result.output || "",
      error: result.error || null,
      time: result.time || null,
      memory: result.memory || null,
    };
  } catch (error) {
    console.error(error);
    logger.error(error.message);
    return {
      success: false,
      error: error.message,
      output: "",
    };
  }
};



