import express from "express";
import { createCategory, getAllCategories, updateCategory } from "../controllers/category.js";
import { isAuthenticated } from "../middleware/auth.js";
import { upload } from "../utils/multerConfig.js";

const categoryRouter = express.Router();

categoryRouter.post(
  "/create",
  isAuthenticated,
  upload.single("icon"),
  createCategory
);

categoryRouter.put(
  "/update/:id",
  isAuthenticated,
  upload.single("icon"),
  updateCategory
);

categoryRouter.get(
  "/all-categories",
  isAuthenticated,
  getAllCategories
);

export default categoryRouter;
