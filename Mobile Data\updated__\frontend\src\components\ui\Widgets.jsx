import { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";

const Widgets = () => {
  const [selectedPlan, setSelectedPlan] = useState("premium");
  const [enrollmentStats, setEnrollmentStats] = useState({
    studentsEnrolled: 10247,
    coursesCompleted: 8934,
    averageRating: 4.9,
    placementRate: 95,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0,
      },
    },
  };

  const cardVariants = {
    hidden: { y: 50, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.1, ease: "easeOut" },
    },
  };

  const courses = [
    {
      id: 1,
      title: "Full Stack Development Bootcamp",
      duration: "16 weeks",
      students: "2,543",
      rating: 4.9,
      price: "₹29,900",
      originalPrice: "₹59,900",
      level: "Beginner to Advanced",
      image: "/images/htmlcssjss.png",
      features: [
        "Live Projects",
        "1:1 Mentorship",
        "Job Assistance",
        "Certificate",
      ],
      technologies: ["React", "Node.js", "MongoDB", "Express"],
    },
    {
      id: 2,
      title: "Data Science & AI Mastery",
      duration: "20 weeks",
      students: "1,856",
      rating: 4.8,
      price: "₹39,900",
      originalPrice: "₹79,900",
      level: "Intermediate",
      image: "/images/python-icon-256x256-892onvae-removebg-preview.png",
      features: [
        "Real Data Projects",
        "ML Models",
        "Industry Mentors",
        "Portfolio",
      ],
      technologies: ["Python", "TensorFlow", "Pandas", "Scikit-learn"],
    },
    {
      id: 3,
      title: "System Design & Architecture",
      duration: "12 weeks",
      students: "987",
      rating: 4.9,
      price: "₹24,900",
      originalPrice: "₹49,900",
      level: "Advanced",
      image: "/images/systemdesignlogo-.png",
      features: [
        "Case Studies",
        "Architecture Reviews",
        "Mock Interviews",
        "Expert Feedback",
      ],
      technologies: ["Microservices", "AWS", "Kubernetes", "Redis"],
    },
  ];

  const plans = [];

  return (
    <div className="min-h-screen py-16">
      <div className="container mx-auto px-6">
        {/* Header Section */}
        <motion.div
          initial={{ y: -50, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          // transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="relative inline-block mb-6">
            <h1
              className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-400 via-indigo-400 to-purple-400 bg-clip-text text-transparent mb-4"
              // animate={{
              //   backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              // }}
              // transition={{
              //   duration: 3,
              //   repeat: Infinity,
              //   ease: "linear",
              // }}
            >
              Transform Your Career Today
            </h1>
          </div>

          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed italic">
            Enroll yourself with our best courses and unlock your potential with
            industry-leading programs designed by experts.
          </p>

          {/* Live Stats */}
          <div
            // initial={{ y: 30, opacity: 0 }}
            // animate={{ y: 0, opacity: 1 }}
            // transition={{ delay: 0 }}
            className="flex flex-wrap justify-center gap-8 mt-12"
          >
            {[
              {
                number: enrollmentStats.studentsEnrolled.toLocaleString() + "+",
                label: "Students Enrolled",
                icon: "👨‍🎓",
              },
              {
                number: enrollmentStats.coursesCompleted.toLocaleString() + "+",
                label: "Courses Completed",
                icon: "🎯",
              },
              {
                number: enrollmentStats.averageRating,
                label: "Average Rating",
                icon: "⭐",
              },
              {
                number: enrollmentStats.placementRate + "%",
                label: "Placement Rate",
                icon: "🚀",
              },
            ].map((stat, index) => (
              <motion.div
                key={index}
                // whileHover={{ scale: 1.05, y: -5 }}
                className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-lg border border-white/10 rounded-xl p-6 text-center shadow-lg"
              >
                <div className="text-3xl mb-2">{stat.icon}</div>
                <div
                  className="text-2xl font-bold text-white"
                  style={{ textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)" }}
                >
                  {stat.number}
                </div>
                <div className="text-sm text-blue-100/80">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Featured Courses Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-20"
        >
          <h2
            className="text-4xl font-bold text-center mb-12 bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent"
            style={{ textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)" }}
          >
            Our Most Popular Courses
          </h2>

          <div className="grid grid-cols-1 max-w-7xl mx-auto lg:grid-cols-3 gap-8">
            {courses.map((course, index) => (
              <motion.div
                key={course.id}
                variants={cardVariants}
                // whileHover={{
                //   scale: 1.02,
                //   y: -10,
                //   boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
                // }}
                className=" rounded-2xl px-3 py-2 hover:bg-[#1e293b]/15 transition-all duration-100 group shadow-xl border border-white/10"
              >
                <div className="relative mb-1 sm:mb-2 h-14 w-14 sm:h-16 sm:w-16 mx-auto">
                  <img
                    src={course.image}
                    alt={course.title}
                    className="w-full h-full object-cover rounded-md"
                  />
                  <div className="absolute top-1 right-1">
                    <div className="bg-green-500/30 backdrop-blur-lg text-white text-[10px] px-1.5 py-0.5 rounded-full font-bold leading-none border border-white/10">
                      HOT
                    </div>
                  </div>
                </div>

                <h3
                  className="text-xl font-bold text-white mb-2 group-hover:text-blue-300 transition-colors"
                  style={{ textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)" }}
                >
                  {course.title}
                </h3>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <span className="text-yellow-400">⭐</span>
                    <span className="text-sm font-medium text-white">
                      {course.rating}
                    </span>
                    <span className="text-sm text-blue-100/80">
                      ({course.students} students)
                    </span>
                  </div>
                  <span className="px-2 py-1 bg-blue-500/20 backdrop-blur-md text-blue-300 text-xs rounded-full font-medium border border-blue-500/20">
                    {course.level}
                  </span>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-blue-100 mb-2">Technologies:</p>
                  <div className="flex flex-wrap gap-1">
                    {course.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-2 py-1 bg-purple-500/10 backdrop-blur-md text-purple-300 text-xs rounded font-medium border border-purple-500/20"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-blue-100 mb-2">What's included:</p>
                  <div className="space-y-1">
                    {course.features
                      .slice(0, 3)
                      .map((feature, featureIndex) => (
                        <div
                          key={featureIndex}
                          className="flex items-center gap-2 text-sm"
                        >
                          <span className="text-green-400">✓</span>
                          <span
                            className="text-white"
                            style={{
                              textShadow: "0 1px 2px rgba(0, 0, 0, 0.3)",
                            }}
                          >
                            {feature}
                          </span>
                        </div>
                      ))}
                  </div>
                </div>

                <div className="flex items-center justify-between mb-6">
                  <div>
                    <span
                      className="text-2xl font-bold text-white"
                      style={{ textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)" }}
                    >
                      {course.price}
                    </span>
                    <span className="text-sm text-gray-300 line-through ml-2">
                      {course.originalPrice}
                    </span>
                  </div>
                  <span className="text-sm text-blue-100/80">
                    {course.duration}
                  </span>
                </div>

                <Link
                  to={`/course/${course.id}`}
                  className="w-full bg-gradient-to-r from-[#118c6e]/30 to-[#0f7a5f]/30 hover:from-[#118c6e]/40 hover:to-[#0f7a5f]/40 backdrop-blur-sm text-white py-3 px-4 rounded-xl font-semibold text-center block transition-all duration-300 border border-white/10 shadow-lg"
                  // style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}
                >
                  Enroll Now
                </Link>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Call to Action Section */}
        <motion.div
          // initial={{ y: 50, opacity: 0 }}
          // animate={{ y: 0, opacity: 1 }}
          // transition={{ delay: 0 }}
          className="relative bg-gradient-to-r from-[#149b77]/20 to-[#118c6e]/20 backdrop-blur-lg rounded-3xl p-12 text-white text-center overflow-hidden border border-white/10 max-w-7xl mx-auto"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10 ">
            {/* <motion.div
              animate={{
                backgroundPosition: ["0% 0%", "100% 100%"],
                opacity: [0.1, 0.2, 0.1],
              }}
              transition={{ duration: 8, repeat: Infinity }}
              className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(255,255,255,0.1)_50%,transparent_75%)] bg-[length:20px_20px]"
            /> */}
          </div>

          <div className="relative z-10 ">
            <h3 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Start Your Learning Journey?
            </h3>
            <p className="text-xl mb-10 opacity-90 max-w-3xl mx-auto italic">
              Join thousands of students who have transformed their careers with
              our expert-led courses. Start learning today with our 7-day free
              trial.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link
                to="/register"
                className="bg-[#303246]/60 backdrop-blur-lg text-white px-8 py-4 rounded-xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:scale-105 border border-white/10"
                // style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}
              >
                🎓 Start Free Trial
              </Link>
              <Link
                to="/courses"
                className="border-2 border-white/30 backdrop-blur-lg text-white px-8 py-4 rounded-xl font-bold hover:text-blue-300 transition-all duration-300 transform hover:scale-105"
                // style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}
              >
                📚 Browse All Courses
              </Link>
            </div>

            {/* Guarantees */}
            <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  icon: "💰",
                  title: "30-Day Money Back",
                  subtitle: "Guarantee",
                },
                {
                  icon: "🏆",
                  title: "Industry Certification",
                  subtitle: "Recognized Everywhere",
                },
                {
                  icon: "👨‍💼",
                  title: "Job Placement",
                  subtitle: "95% Success Rate",
                },
              ].map((item, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl mb-2">{item.icon}</div>
                  <div className="text-lg font-bold">{item.title}</div>
                  <div className="text-sm opacity-80">{item.subtitle}</div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
export default Widgets;
