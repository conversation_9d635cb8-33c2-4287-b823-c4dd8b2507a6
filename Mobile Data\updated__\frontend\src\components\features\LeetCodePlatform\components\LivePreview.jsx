import React from 'react';
import { RotateCcw } from 'lucide-react';

const LivePreview = ({
  files,
  previewKey,
  onRefresh,
  isDarkMode
}) => {
  const generatePreview = () => {
    const html = files['index.html']?.content || '';
    const css = files['style.css']?.content || '';
    const js = files['script.js']?.content || '';
    
    // Clean HTML by removing script and link tags
    const scriptRegex = /<script.*?src.*?<\/script>/gi;
    const linkRegex = /<link.*?rel="stylesheet".*?>/gi;
    const cleanHtml = html.replace(scriptRegex, '').replace(linkRegex, '');
    
    const previewHtml = [
      '<!DOCTYPE html>',
      '<html>',
      '<head>',
      '  <meta charset="UTF-8">',
      '  <meta name="viewport" content="width=device-width, initial-scale=1.0">',
      '  <style>' + css + '</style>',
      '</head>',
      '<body>',
      '  ' + cleanHtml,
      '  <script>',
      '    try {',
      '      ' + js,
      '    } catch (error) {',
      '      console.error("Preview Error:", error);',
      '    }',
      '  </script>',
      '</body>',
      '</html>'
    ].join('\n');
    
    return previewHtml;
  };

  return (
    <div className="w-1/2 flex flex-col">
      <div className={`p-2 border-b text-sm flex items-center justify-between ${
        isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-300' : 'border-gray-200 bg-gray-50 text-gray-700'
      }`}>
        <span>Live Preview</span>
        <button
          onClick={onRefresh}
          className={`p-1 rounded transition-colors ${
            isDarkMode
              ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
              : 'text-gray-600 hover:text-gray-700 hover:bg-gray-200'
          }`}
          title="Refresh Preview"
        >
          <RotateCcw size={14} />
        </button>
      </div>
      <iframe
        key={previewKey}
        srcDoc={generatePreview()}
        className="flex-1 bg-white"
        title="Live Preview"
        sandbox="allow-scripts allow-same-origin"
      />
    </div>
  );
};

export default LivePreview;
