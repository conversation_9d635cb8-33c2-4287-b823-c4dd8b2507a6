import dotenv from "dotenv";
import mongoose from "mongoose";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";

dotenv.config();

const emailRegexPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Please Enter Your Name"],
      trim: true,
    },

    email: {
      type: String,
      required: [true, "Please Enter Your Email"],
      unique: true,
      validate: {
        validator: function (value) {
          return emailRegexPattern.test(value);
        },
        message: "Please Enter a Valid Email",
      },
    },

    image: {
      type: String,
    },

    password: {
      type: String,
      required: function () {
        return !this.googleAuth;
      },
      minlength: [6, "Password must be at least 6 characters"],
    },

    googleAuth: {
      type: Boolean,
      default: false,
    },

    role: {
      type: String,
      enum: ["user", "admin"],
      default: "user",
    },

    refreshToken: String,

    resetPasswordToken: String,
    resetPasswordExpires: Date,

    isTwoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorVerificationToken: String,
    twoFactorVerificationExpires: Date,

    gender: String,
    phone: String,
    country: String,
    city: String,

    socialMedia: {
      linkedin: { type: String, default: null },
      github: { type: String, default: null },
      twitter: { type: String, default: null },
    },

    codingStats: {
      totalProblemsSolved: { type: Number, default: 0 },
      totalSubmissions: { type: Number, default: 0 },
      acceptedSubmissions: { type: Number, default: 0 },
      successRate: { type: Number, default: 0 },
    },

    favoriteProblems: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Problem",
      },
    ],

    badges: [
      {
        title: String,
        icon: String,
        achievedAt: Date,
      },
    ],

    preferences: {
      theme: { type: String, enum: ["light", "dark"], default: "light" },
      defaultLanguage: { type: String, default: "cpp" },
    },

    lastActivity: Date,

    projectsCreated: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ProjectRegistration",
      },
    ],

    hackathonParticipation: [
      {
        hackathonId: { type: mongoose.Schema.Types.ObjectId, ref: "Hackathon" },
        participatedAt: Date,
        score: Number,
      },
    ],

    certifications: [
      {
        title: String,
        issuedAt: Date,
        credentialUrl: String,
      },
    ],
  },
  { timestamps: true }
);

userSchema.pre("save", async function (next) {
  if (!this.isModified("password") || this.googleAuth) {
    return next();
  }
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

userSchema.methods.isPasswordCorrect = async function (password) {
  return await bcrypt.compare(password, this.password);
};

userSchema.methods.generateAccessToken = async function () {
  return jwt.sign(
    {
      _id: this._id,
      name: this.name,
      email: this.email,
    },
    process.env.ACCESS_TOKEN_SECRET,
    { expiresIn: process.env.ACCESS_TOKEN_EXPIRY || "15m" }
  );
};

userSchema.methods.generateRefreshToken = async function () {
  return jwt.sign({ _id: this._id }, process.env.REFRESH_TOKEN_SECRET, {
    expiresIn: process.env.REFRESH_TOKEN_EXPIRY || "3d",
  });
};

const User = mongoose.model("User", userSchema);
export default User;
