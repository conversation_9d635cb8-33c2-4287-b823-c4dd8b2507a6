import mongoose from "mongoose";
import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import Chapter from "../models/chapterModal.js";
import cloudinary from "../utils/cloudinaryConfig.js";
import ErrorHandler from "../utils/ErrorHandler.js";
import { redis } from "../utils/redisClient.js";
import { validateCreateChapter } from "../utils/validation.js";
import fs from "fs";

export const generateSlug = (text) => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)+/g, "");
};

export const createChapter = CatchAsyncError(async (req, res, next) => {
  try {
    const value = validateCreateChapter(req.body);
    const slug = generateSlug(req.body.title);
    let thumbnailUpload;

    if (req.file) {
      thumbnailUpload = await cloudinary.uploader.upload(req.file.path, {
        folder: "chapters",
      });
    }

    const chapter = await Chapter.create({
      ...value,
      slug,
      thumbnail: thumbnailUpload
        ? {
            url: thumbnailUpload.secure_url,
            public_id: thumbnailUpload.public_id,
          }
        : null,
      createdBy: req.user._id,
      createdBy: req.user._id,
    });

    await redis.del("chapters:all");

    res.status(201).json({
      success: true,
      message: "Chapter Created !!",
      chapter,
    });
  } catch (err) {
    return next(new ErrorHandler(err.message, 500));
  } finally {
    if (req.file && req.file.path) {
      fs.unlink(req.file.path, (unlinkErr) => {
        if (unlinkErr) {
          console.error("Unable to delete file", unlinkErr);
        }
      });
    }
  }
});

export const updateChapter = CatchAsyncError(async (req, res, next) => {
  let newThumbnailUpload;

  try {
    const { id } = req.params;
    const chapter = await Chapter.findById(id);

    if (!chapter) {
      return next(new ErrorHandler("Chapter not found", 404));
    }

    if (req.file) {
      newThumbnailUpload = await cloudinary.uploader.upload(req.file.path, {
        folder: "chapters",
      });

      if (chapter.thumbnail && chapter.thumbnail.public_id) {
        await cloudinary.uploader.destroy(chapter.thumbnail.public_id);
      }

      chapter.thumbnail = {
        url: newThumbnailUpload.secure_url,
        public_id: newThumbnailUpload.public_id,
      };
    }

    const updatableFields = [
      "title",
      "description",
      "labId",
      "labsectionId",
      "level",
      "isFree",
      "estimatedTime",
      "metaTitle",
      "metaDescription",
      "keywords",
      "visibility",
      "status",
      "resources",
      "rolesAllowed",
    ];

    updatableFields.forEach((field) => {
      if (req.body[field] !== undefined) {
        chapter[field] = req.body[field];
      }
    });

    chapter.updatedBy = req.user._id;
    await chapter.save();

    await redis.del("chapters:all");
    await redis.del(`chapter:${id}`);

    res.status(200).json({
      success: true,
      message: "Chapter updated !!",
      chapter,
    });
  } catch (err) {
    return next(new ErrorHandler(err.message, 500));
  } finally {
    if (req.file && req.file.path) {
      fs.unlink(req.file.path, (unlinkErr) => {
        if (unlinkErr) {
          console.error("Unable to delete local file", unlinkErr);
        }
      });
    }
  }
});

export const getAllChapters = CatchAsyncError(async (req, res, next) => {
  const {
    page = 1,
    limit = 10,
    labId,
    labsectionId,
    status,
    level,
    visibility,
  } = req.query;

  const pageNum = parseInt(page, 10);
  const limitNum = parseInt(limit, 10);

  if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
    return next(new ErrorHandler("Invalid pagination values", 400));
  }

  if (labId && !mongoose.Types.ObjectId.isValid(labId)) {
    return next(new ErrorHandler("Invalid labId", 400));
  }
  if (labsectionId && !mongoose.Types.ObjectId.isValid(labsectionId)) {
    return next(new ErrorHandler("Invalid labsectionId", 400));
  }

  const cacheKey = `chapters:${pageNum}:${limitNum}:${labId || ""}:${
    labsectionId || ""
  }:${status || ""}:${level || ""}:${visibility || ""}`;

  const cachedData = await redis.get(cacheKey);
  if (cachedData) {
    return res.status(200).json(cachedData);
  }

  const filters = {};
  if (labId) filters.labId = labId;
  if (labsectionId) filters.labsectionId = labsectionId;
  if (status) filters.status = status;
  if (level) filters.level = level;
  if (visibility) filters.visibility = visibility;

  const skip = (pageNum - 1) * limitNum;
  const [chapters, total] = await Promise.all([
    Chapter.find(filters).sort({ createdAt: -1 }).skip(skip).limit(limitNum),
    Chapter.countDocuments(filters),
  ]);

  const response = {
    success: true,
    message: "Chapters fetched successfully",
    page: pageNum,
    limit: limitNum,
    total,
    totalPages: Math.ceil(total / limitNum),
    chapters,
  };

  await redis.set(cacheKey, response, { ex: 3600 });

  res.status(200).json(response);
});

export const getChapterById = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Chapter ID", 400));
  }

  const cacheKey = `chapter:${id}`;

  const cachedChapter = await redis.get(cacheKey);
  if (cachedChapter) {
    return res.status(200).json({
      success: true,
      message: "Chapter fetched successfully from cache",
      chapter: cachedChapter,
    });
  }

  const chapter = await Chapter.findOne({ _id: id, isDeleted: { $ne: true } });

  if (!chapter) {
    return next(new ErrorHandler("Chapter not found", 404));
  }

  await redis.set(cacheKey, chapter, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: "Chapter fetched successfully",
    chapter,
  });
});

export const deleteChapter = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Chapter ID", 400));
  }

  const chapter = await Chapter.findById(id);

  if (!chapter || chapter.isDeleted) {
    return next(new ErrorHandler("Chapter not found or already deleted", 404));
  }

  chapter.isDeleted = true;
  chapter.updatedBy = req.user._id;
  await chapter.save();

  if (chapter.thumbnail && chapter.thumbnail.public_id) {
    await cloudinary.uploader.destroy(chapter.thumbnail.public_id);
  }

  await redis.del(`chapter:${id}`);

  res.status(200).json({
    success: true,
    message: "Chapter deleted !!",
  });
});

export const restoreChapter = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Chapter ID", 400));
  }

  const chapter = await Chapter.findById(id);

  if (!chapter || !chapter.isDeleted) {
    return next(new ErrorHandler("Chapter not found or already active", 404));
  }

  chapter.isDeleted = false;
  chapter.updatedBy = req.user._id;
  await chapter.save();

  await redis.set(`chapter:${id}`, chapter, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: "Chapter restored successfully",
    chapter,
  });
});

export const searchChapters = CatchAsyncError(async (req, res, next) => {
  const { query, page = 1, limit = 10 } = req.query;

  if (!query || query.trim() === "") {
    return next(new ErrorHandler("Search query is required", 400));
  }

  const cacheKey = `chapters:search:${query}:page:${page}:limit:${limit}`;
  const cachedData = await redis.get(cacheKey);
  if (cachedData) {
    return res.status(200).json(cachedData);
  }

  const skip = (page - 1) * limit;

  const [chapters, total] = await Promise.all([
    Chapter.find(
      { $text: { $search: query }, isDeleted: false, isActive: true },
      { score: { $meta: "textScore" } }
    )
      .sort({ score: { $meta: "textScore" } })
      .skip(skip)
      .limit(parseInt(limit)),
    Chapter.countDocuments({
      $text: { $search: query },
      isDeleted: false,
      isActive: true,
    }),
  ]);

  const response = {
    success: true,
    message: "Chapters fetched !!",
    page: parseInt(page),
    limit: parseInt(limit),
    total,
    totalPages: Math.ceil(total / limit),
    chapters,
  };

  await redis.set(cacheKey, response, { ex: 300 });

  res.status(200).json(response);
});

export const toggleChapterStatus = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  const { status } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Chapter ID", 400));
  }

  const validStatus = ["draft", "published", "archived"];
  if (!status || !validStatus.includes(status)) {
    return next(new ErrorHandler("Invalid status value", 400));
  }

  const chapter = await Chapter.findById(id);
  if (!chapter || chapter.isDeleted) {
    return next(new ErrorHandler("Chapter not found or deleted", 404));
  }

  chapter.status = status;
  chapter.updatedBy = req.user._id;
  await chapter.save();

  await redis.set(`chapter:${id}`, chapter, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: `Chapter status updated to ${status}`,
    chapter,
  });
});

export const updateVisibility = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  const { visibility } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Chapter ID", 400));
  }

  const validVisibilities = ["public", "private", "restricted"];
  if (!visibility || !validVisibilities.includes(visibility)) {
    return next(
      new ErrorHandler("Invalid visibility. Allowed: public, private, restricted", 400)
    );
  }

  const chapter = await Chapter.findById(id);
  if (!chapter || chapter.isDeleted) {
    return next(new ErrorHandler("Chapter not found or deleted", 404));
  }

  chapter.visibility = visibility;
  chapter.updatedBy = req.user._id;
  await chapter.save();

  await redis.set(`chapter:${id}`, chapter, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: `Visibility updated to ${visibility}`,
    chapter,
  });
});

export const incrementEngagement = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  const { field } = req.body; 

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Chapter ID", 400));
  }

  const allowedFields = ["views", "likes", "bookmarks"];
  if (!field || !allowedFields.includes(field)) {
    return next(new ErrorHandler("Invalid field. Allowed: views, likes, bookmarks", 400));
  }

  const chapter = await Chapter.findByIdAndUpdate(
    id,
    { $inc: { [field]: 1 }, updatedBy: req.user._id },
    { new: true }
  );

  if (!chapter || chapter.isDeleted) {
    return next(new ErrorHandler("Chapter not found or deleted", 404));
  }

  await redis.set(`chapter:${id}`, chapter, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: `${field} incremented successfully`,
    chapter,
  });
});

export const manageResources = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;
  const { action, resource } = req.body;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Chapter ID", 400));
  }

  const allowedActions = ["add", "remove"];
  if (!action || !allowedActions.includes(action)) {
    return next(new ErrorHandler("Invalid action. Allowed: add, remove", 400));
  }

  if (action === "add") {
    if (!resource || !resource.title || !resource.url || !resource.type) {
      return next(new ErrorHandler("Resource must have title, url, and type", 400));
    }
    const validTypes = ["pdf", "video", "link"];
    if (!validTypes.includes(resource.type)) {
      return next(new ErrorHandler("Invalid resource type. Allowed: pdf, video, link", 400));
    }
  }

  let updateQuery;
  if (action === "add") {
    updateQuery = { $push: { resources: resource }, updatedBy: req.user._id };
  } else if (action === "remove") {
    if (!resource || !resource._id) {
      return next(new ErrorHandler("Resource _id is required to remove", 400));
    }
    updateQuery = { $pull: { resources: { _id: resource._id } }, updatedBy: req.user._id };
  }

  const chapter = await Chapter.findByIdAndUpdate(id, updateQuery, { new: true });

  if (!chapter || chapter.isDeleted) {
    return next(new ErrorHandler("Chapter not found or deleted", 404));
  }

  await redis.set(`chapter:${id}`, chapter, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: `Resource ${action === "add" ? "added" : "removed"} successfully`,
    chapter,
  });
});




