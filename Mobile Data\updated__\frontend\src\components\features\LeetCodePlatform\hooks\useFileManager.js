import { useState } from 'react';

const defaultFiles = {
  'index.html': {
    name: 'index.html',
    content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UpCoding Project</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <h1>Welcome to UpCoding!</h1>
        <p>Start building your project here.</p>
        <button onclick="greet()">Click me!</button>
    </div>
    <script src="script.js"></script>
</body>
</html>`,
    type: 'html'
  },
  'style.css': {
    name: 'style.css',
    content: `body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

#app {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

button {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-top: 20px;
}

button:hover {
    background: #45a049;
}`,
    type: 'css'
  },
  'script.js': {
    name: 'script.js',
    content: `function greet() {
    alert('Hello from UpCoding!');
}

// Add your JavaScript code here
console.log('UpCoding project loaded!');

// Example API call
async function fetchData() {
    try {
        const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
        const data = await response.json();
        console.log('API Response:', data);
    } catch (error) {
        console.error('API Error:', error);
    }
}

// Uncomment to test API
// fetchData();`,
    type: 'javascript'
  },
  'package.json': {
    name: 'package.json',
    content: `{
  "name": "upcoding-project",
  "version": "1.0.0",
  "description": "A UpCoding project",
  "main": "index.html",
  "scripts": {
    "start": "live-server",
    "build": "webpack --mode production",
    "dev": "webpack serve --mode development"
  },
  "dependencies": {
    "axios": "^1.6.0",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "webpack": "^5.89.0",
    "webpack-cli": "^5.1.4",
    "webpack-dev-server": "^4.15.1"
  },
  "keywords": ["upcoding", "web", "development"],
  "author": "UpCoding User",
  "license": "MIT"
}`,
    type: 'json'
  }
};

export const useFileManager = (setCode) => {
  const [files, setFiles] = useState(defaultFiles);
  const [activeFile, setActiveFile] = useState('index.html');

  const createNewFile = () => {
    const fileName = prompt('Enter file name (e.g., newfile.js, styles.css):');
    if (fileName && !files[fileName]) {
      const extension = fileName.split('.').pop().toLowerCase();
      let fileType = 'text';
      let defaultContent = '';

      switch (extension) {
        case 'html':
          fileType = 'html';
          defaultContent = '<!DOCTYPE html>\n<html>\n<head>\n    <title>New Page</title>\n</head>\n<body>\n    \n</body>\n</html>';
          break;
        case 'css':
          fileType = 'css';
          defaultContent = '/* Add your styles here */\n';
          break;
        case 'js':
          fileType = 'javascript';
          defaultContent = '// Add your JavaScript code here\n';
          break;
        case 'json':
          fileType = 'json';
          defaultContent = '{\n  \n}';
          break;
        default:
          fileType = 'text';
      }

      setFiles(prev => ({
        ...prev,
        [fileName]: {
          name: fileName,
          content: defaultContent,
          type: fileType
        }
      }));
      setActiveFile(fileName);
      setCode(defaultContent);
    }
  };

  const deleteFile = (fileName) => {
    if (Object.keys(files).length <= 1) {
      alert('Cannot delete the last file!');
      return;
    }
    
    if (confirm(`Are you sure you want to delete ${fileName}?`)) {
      const newFiles = { ...files };
      delete newFiles[fileName];
      setFiles(newFiles);
      
      if (activeFile === fileName) {
        const firstFile = Object.keys(newFiles)[0];
        setActiveFile(firstFile);
        setCode(newFiles[firstFile].content);
      }
    }
  };

  const switchFile = (fileName) => {
    setActiveFile(fileName);
    setCode(files[fileName].content);
  };

  const updateFileContent = (fileName, content) => {
    setFiles(prev => ({
      ...prev,
      [fileName]: {
        ...prev[fileName],
        content
      }
    }));
  };

  const installPackage = () => {
    const packageName = prompt('Enter package name to install (e.g., axios, lodash):');
    if (packageName) {
      const packageJson = JSON.parse(files['package.json'].content);
      packageJson.dependencies[packageName] = '^1.0.0';
      
      setFiles(prev => ({
        ...prev,
        'package.json': {
          ...prev['package.json'],
          content: JSON.stringify(packageJson, null, 2)
        }
      }));
      
      // Simulate installation
      setTimeout(() => {
        alert(`Package "${packageName}" installed successfully!`);
      }, 1000);
    }
  };

  const resetProject = () => {
    setFiles(defaultFiles);
    setActiveFile('index.html');
    setCode(defaultFiles['index.html'].content);
  };

  return {
    files,
    activeFile,
    createNewFile,
    deleteFile,
    switchFile,
    updateFileContent,
    installPackage,
    resetProject
  };
};
