import express from "express";
import {
  createLab,
  deleteLab,
  getAdvancedLabs,
  getAllLabs,
  getLabById,
  getPopularLabs,
  updateLab,
} from "../controllers/lab.js";
import { isAuthenticated } from "../middleware/auth.js";
import { upload } from "../utils/multerConfig.js";

const labRouter = express.Router();

labRouter.post("/create", isAuthenticated, upload.single("icon"), createLab);

labRouter.get("/all-labs", getAllLabs);

labRouter.get("/hot-labs", getPopularLabs);

labRouter.get("/advanced-labs", getAdvancedLabs);

labRouter.get("/:id", isAuthenticated, getLabById);

labRouter.put("/update/:id", isAuthenticated, upload.single("icon"), updateLab);

labRouter.delete("/delete/:id", isAuthenticated, deleteLab);

export default labRouter;
