import { spawn } from "child_process";
import path from "path";

const engine = {
  execute: (code, language, input, labType, { timeout }) => {
    return new Promise((resolve) => {
      const executorPath = path.resolve(
        "../execution-engine/bin/Release/executor.exe"
      );
      const cwd = path.resolve("../execution-engine");

      const payload = JSON.stringify({
        language,
        code,
        input,
        labType,
        timeout,
      });

      console.log("🚀 [engine.js] Executor Path:", executorPath);
      console.log("📁 [engine.js] Working Directory:", cwd);
      console.log("📦 [engine.js] Payload:", payload);

      const proc = spawn(executorPath, { cwd });

      let stdout = "";
      let stderr = "";

      proc.stdin.write(payload);
      proc.stdin.end();

      proc.stdout.on("data", (data) => {
        const chunk = data.toString();
        stdout += chunk;
        console.log("📤 [engine.js] STDOUT chunk:\n" + chunk);
      });

      proc.stderr.on("data", (data) => {
        const errChunk = data.toString();
        stderr += errChunk;
        console.error("❗ [engine.js] STDERR chunk:\n" + errChunk);
      });

      proc.on("error", (err) => {
        console.error("❌ [engine.js] Spawn Error:", err);
        return resolve({
          output: "",
          error: `Spawn error: ${err.message}`,
          time: null,
          memory: null,
        });
      });

      proc.on("close", (code) => {
        console.log("🔚 [engine.js] Process closed with code:", code);
        console.log("📤 [engine.js] Final STDOUT:\n" + stdout);
        console.log("❗ [engine.js] Final STDERR:\n" + stderr);

        if (stderr.trim()) {
          return resolve({
            output: "",
            error: stderr.trim(),
            time: null,
            memory: null,
          });
        }

        try {
          // 🧠 Split output into JSON blocks (supports multi-line JSON)
          const jsonBlocks = stdout
            .trim()
            .split(/(?<=\})\s*\n/)
            .map((block) => block.trim())
            .filter((block) => block.startsWith("{") && block.endsWith("}"));

          let result = null;

          for (let i = jsonBlocks.length - 1; i >= 0; i--) {
            try {
              const parsed = JSON.parse(jsonBlocks[i]);
              if (parsed && typeof parsed === "object" && parsed.output !== undefined) {
                result = parsed;
                break;
              }
            } catch {
              continue;
            }
          }

          if (!result) {
            throw new Error("No valid JSON with 'output' found in stdout.");
          }

          console.log("✅ [engine.js] Parsed JSON Result:", result);

          // Fallback if output is empty
          if (!result.output) {
            const fallbackLine = stdout
              .split("\n")
              .reverse()
              .find((line) => line.includes("✅ Final Output (escaped):"));

            if (fallbackLine) {
              result.output = fallbackLine
                .split("✅ Final Output (escaped):")[1]
                .trim();
              console.log("📦 [engine.js] Fallback Output Extracted:", result.output);
            }
          }

          resolve({
            output: result.output || "",
            error: result.error || "",
            time: result.time || null,
            memory: result.memory || null,
          });
        } catch (err) {
          console.error("❌ [engine.js] JSON Parse Error:", err.message);
          resolve({
            output: "",
            error: `Invalid JSON output from executor: ${err.message}`,
            time: null,
            memory: null,
          });
        }
      });
    });
  },
};

export default engine;
