{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.8.0", "@upstash/redis": "^1.35.1", "amqplib": "^0.10.8", "axios": "^1.11.0", "bcrypt": "^5.1.1", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dockerode": "^4.0.7", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.10.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.10.0", "nodemon": "^3.1.9", "p-map": "^7.0.3", "tar-fs": "^3.1.0", "tar-stream": "^3.1.7", "uuid": "^11.1.0", "validator": "^13.12.0", "winston": "^3.17.0"}}