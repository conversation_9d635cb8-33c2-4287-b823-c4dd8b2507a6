import mongoose from "mongoose";

const liveClassSchema = new mongoose.Schema(
  {
    lab: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Lab",
      required: true,
    },
    labSection: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "LabSection",
      required: true,
    },
    title: {
      type: String,
      required: [true, "Class title is required"],
      trim: true,
      maxlength: 150,
    },
    description: {
      type: String,
      maxlength: 2000,
    },
    instructor: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },

    classType: {
      type: String,
      enum: ["live", "recorded"],
      required: true,
    },

    scheduledAt: {
      type: Date,
      validate: {
        validator: function (value) {
          return this.classType !== "live" || value > new Date();
        },
        message: "Scheduled time must be in the future.",
      },
    },
    durationMinutes: {
      type: Number,
      default: 60,
      min: [10, "Minimum duration is 10 minutes"],
    },
    meetingLink: {
      type: String,
      validate: {
        validator: function (v) {
          return !v || /^https?:\/\/\S+$/.test(v);
        },
        message: "Invalid meeting URL",
      },
    },

    // Only required if classType === 'recorded'
    videoUrl: {
      type: String,
      validate: {
        validator: function (v) {
          return !v || /^https?:\/\/\S+$/.test(v);
        },
        message: "Invalid video URL",
      },
    },

    isActive: {
      type: Boolean,
      default: true,
    },

    isRecordingAvailable: {
      type: Boolean,
      default: false,
    },
    recordingLink: {
      type: String,
      validate: {
        validator: function (v) {
          return !v || /^https?:\/\/\S+$/.test(v);
        },
        message: "Invalid recording URL",
      },
    },

    tags: [
      {
        type: String,
        trim: true,
        lowercase: true,
        maxlength: 30,
      },
    ],

    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
);

liveClassSchema.index({ lab: 1, labSection: 1, classType: 1 });

const LiveClass = mongoose.model("LiveClass", liveClassSchema);
export default LiveClass;
