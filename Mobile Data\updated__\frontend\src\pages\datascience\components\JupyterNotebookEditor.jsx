import React, { useState } from "react";
import { EmbeddedCodeEditor } from "../../../components/ui";

const JupyterNotebookEditor = ({ onBack }) => {
  const [cells, setCells] = useState([
    {
      id: "cell-1",
      type: "markdown",
      content: "# Data Science Notebook\n\nThis is a Jupyter-style notebook for data analysis and visualization. You can write markdown text like this cell, or code in the cells below.\n\n## Getting Started\n\nRun the code cells below to see how it works:",
      isEditing: false
    },
    {
      id: "cell-2",
      type: "code",
      content: "# Import essential libraries\nimport pandas as pd\nimport numpy as np\nimport matplotlib.pyplot as plt\n\n# Create sample data\ndata = pd.DataFrame({\n    'x': np.random.normal(0, 1, 100),\n    'y': np.random.normal(0, 1, 100),\n    'category': np.random.choice(['A', 'B', 'C'], 100)\n})\n\n# Display first 5 rows\nprint(data.head())",
      output: "",
      isRunning: false
    },
    {
      id: "cell-3",
      type: "code",
      content: "# Create a simple visualization\nplt.figure(figsize=(8, 6))\nplt.scatter(data['x'], data['y'], c=data['category'].astype('category').cat.codes, alpha=0.7)\nplt.colorbar(label='Category')\nplt.title('Sample Scatter Plot')\nplt.xlabel('X values')\nplt.ylabel('Y values')\nplt.grid(True, alpha=0.3)\nplt.show()",
      output: "",
      isRunning: false
    },
    {
      id: "cell-4",
      type: "markdown",
      content: "## Data Analysis\n\nYou can perform various data analysis tasks in this notebook environment. Try adding new cells and running different analyses.",
      isEditing: false
    }
  ]);

  const handleRunCode = (cellId) => {
    setCells(cells.map(cell => {
      if (cell.id === cellId && cell.type === 'code') {
        // Simulate code execution
        const output = simulateCodeExecution(cell.content);
        return { ...cell, isRunning: false, output };
      }
      return cell;
    }));
  };

  const simulateCodeExecution = (code) => {
    // Simple simulation of code execution with appropriate outputs
    if (code.includes('import')) {
      return "Libraries imported successfully!";
    } else if (code.includes('print')) {
      return `   x         y category\n0 -0.577     0.123       A\n1  1.052    -0.908       C\n2 -0.098     0.283       B\n3  0.541    -1.231       A\n4 -0.702     0.433       B`;
    } else if (code.includes('plt.scatter')) {
      return "[Scatter plot visualization displayed]";
    } else if (code.includes('data.describe()')) {
      return `              x             y\ncount  100.000000   100.000000\nmean     0.018651     0.059277\nstd      0.986744     1.029272\nmin     -2.226076    -2.620862\n25%     -0.695807    -0.581841\n50%      0.047102     0.021231\n75%      0.767338     0.704450\nmax      2.123491     2.427877`;
    } else {
      return "Code executed successfully!";
    }
  };

  const handleCodeChange = (cellId, newContent) => {
    setCells(cells.map(cell => 
      cell.id === cellId ? { ...cell, content: newContent } : cell
    ));
  };

  const handleMarkdownChange = (cellId, newContent) => {
    setCells(cells.map(cell => 
      cell.id === cellId ? { ...cell, content: newContent } : cell
    ));
  };

  const toggleMarkdownEdit = (cellId) => {
    setCells(cells.map(cell => 
      cell.id === cellId ? { ...cell, isEditing: !cell.isEditing } : cell
    ));
  };

  const addCell = (type, index) => {
    const newCell = {
      id: `cell-${Date.now()}`,
      type,
      content: type === 'code' ? '# Enter your code here' : '## New markdown cell',
      output: "",
      isRunning: false,
      isEditing: type === 'markdown'
    };
    
    const newCells = [...cells];
    newCells.splice(index + 1, 0, newCell);
    setCells(newCells);
  };

  const renderMarkdownContent = (content) => {
    // Very simple markdown rendering (just for demonstration)
    return (
      <div className="prose prose-invert max-w-none">
        {content.split('\n').map((line, i) => {
          if (line.startsWith('# ')) {
            return <h1 key={i} className="text-2xl font-bold mb-4">{line.substring(2)}</h1>;
          } else if (line.startsWith('## ')) {
            return <h2 key={i} className="text-xl font-bold mb-3">{line.substring(3)}</h2>;
          } else if (line.startsWith('### ')) {
            return <h3 key={i} className="text-lg font-bold mb-2">{line.substring(4)}</h3>;
          } else if (line === '') {
            return <br key={i} />;
          } else {
            return <p key={i} className="mb-2">{line}</p>;
          }
        })}
      </div>
    );
  };

  return (
    <div className="bg-[#111827] text-white rounded-xl overflow-hidden border border-gray-700">
      <div className="bg-gradient-to-r from-blue-900/30 to-purple-900/30 p-4 border-b border-gray-700 flex justify-between items-center">
        <div className="flex items-center">
          <button 
            onClick={onBack}
            className="mr-3 px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm flex items-center"
          >
            <span className="mr-1">←</span> Back
          </button>
          <span className="text-2xl mr-3">📓</span>
          <h2 className="text-xl font-bold">Jupyter Notebook</h2>
        </div>
        <div className="flex space-x-2">
          <button className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">Save</button>
          <button className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm">Share</button>
        </div>
      </div>

      <div className="p-4">
        {cells.map((cell, index) => (
          <div key={cell.id} className="mb-8">
            {/* Cell controls */}
            <div className="flex items-center justify-between mb-1 text-xs text-gray-400">
              <div className="flex items-center">
                <span className="bg-gray-700 px-2 py-1 rounded mr-2">{`[${index + 1}]`}</span>
                <span>{cell.type === 'code' ? 'Code' : 'Markdown'}</span>
              </div>
              <div className="flex space-x-2">
                {cell.type === 'code' && (
                  <button 
                    onClick={() => handleRunCode(cell.id)}
                    className="px-2 py-1 bg-green-700 hover:bg-green-600 rounded flex items-center"
                  >
                    <span className="mr-1">▶</span> Run
                  </button>
                )}
                {cell.type === 'markdown' && (
                  <button 
                    onClick={() => toggleMarkdownEdit(cell.id)}
                    className="px-2 py-1 bg-blue-700 hover:bg-blue-600 rounded"
                  >
                    {cell.isEditing ? 'Preview' : 'Edit'}
                  </button>
                )}
                <button 
                  onClick={() => addCell('code', index)}
                  className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded"
                >
                  + Code
                </button>
                <button 
                  onClick={() => addCell('markdown', index)}
                  className="px-2 py-1 bg-gray-700 hover:bg-gray-600 rounded"
                >
                  + Markdown
                </button>
              </div>
            </div>

            {/* Cell content */}
            <div className="border border-gray-700 rounded-md overflow-hidden">
              {cell.type === 'code' ? (
                <div>
                  <div className="border-b border-gray-700">
                    <EmbeddedCodeEditor
                      language="python"
                      value={cell.content}
                      onChange={(newCode) => handleCodeChange(cell.id, newCode)}
                      height="auto"
                    />
                  </div>
                  {cell.output && (
                    <div className="bg-gray-900 p-4 font-mono text-sm whitespace-pre-wrap text-gray-300">
                      {cell.output}
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-gray-800 p-4">
                  {cell.isEditing ? (
                    <textarea
                      className="w-full bg-gray-900 text-gray-300 p-3 rounded border border-gray-700 font-mono"
                      value={cell.content}
                      onChange={(e) => handleMarkdownChange(cell.id, e.target.value)}
                      rows={cell.content.split('\n').length + 1}
                    />
                  ) : (
                    <div className="text-gray-200" onClick={() => toggleMarkdownEdit(cell.id)}>
                      {renderMarkdownContent(cell.content)}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Add cell buttons at the end */}
        <div className="flex justify-center space-x-4 mt-6">
          <button 
            onClick={() => addCell('code', cells.length - 1)}
            className="px-4 py-2 bg-blue-700 hover:bg-blue-600 rounded-md"
          >
            + Add Code Cell
          </button>
          <button 
            onClick={() => addCell('markdown', cells.length - 1)}
            className="px-4 py-2 bg-purple-700 hover:bg-purple-600 rounded-md"
          >
            + Add Markdown Cell
          </button>
        </div>
      </div>
    </div>
  );
};

export default JupyterNotebookEditor;