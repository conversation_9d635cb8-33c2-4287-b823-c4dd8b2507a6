import React, { useState } from 'react';
import { Plus, Edit, Trash2, Save, X, Search, Filter, Eye, Code, Clock, Users, Star, Tag, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const QuestionManager = ({ theme }) => {
  // Enhanced question data based on Question.js and problemModal.js
  const [questions, setQuestions] = useState([
    {
      _id: '507f1f77bcf86cd799439011',
      title: 'Two Sum Problem',
      description: 'Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target. You may assume that each input would have exactly one solution, and you may not use the same element twice.',
      difficulty: 'Easy',
      tags: ['Array', 'Hash Table', 'Two Pointers'],
      lab: '507f1f77bcf86cd799439001',
      labSection: '507f1f77bcf86cd799439002',
      category: '507f1f77bcf86cd799439003',
      slug: 'two-sum-problem',
      inputDescription: 'nums: an array of integers, target: an integer',
      outputDescription: 'Return an array of two indices',
      constraints: '2 <= nums.length <= 10^4, -10^9 <= nums[i] <= 10^9, -10^9 <= target <= 10^9',
      examples: [
        {
          input: 'nums = [2,7,11,15], target = 9',
          output: '[0,1]',
          explanation: 'Because nums[0] + nums[1] == 9, we return [0, 1].'
        },
        {
          input: 'nums = [3,2,4], target = 6',
          output: '[1,2]',
          explanation: 'Because nums[1] + nums[2] == 6, we return [1, 2].'
        }
      ],
      starterCode: {
        javascript: 'function twoSum(nums, target) {\n    // Your code here\n}',
        python: 'def two_sum(nums, target):\n    # Your code here\n    pass',
        java: 'public int[] twoSum(int[] nums, int target) {\n    // Your code here\n}',
        cpp: 'vector<int> twoSum(vector<int>& nums, int target) {\n    // Your code here\n}'
      },
      solutionCode: {
        javascript: 'function twoSum(nums, target) {\n    const map = new Map();\n    for (let i = 0; i < nums.length; i++) {\n        const complement = target - nums[i];\n        if (map.has(complement)) {\n            return [map.get(complement), i];\n        }\n        map.set(nums[i], i);\n    }\n}',
        python: 'def two_sum(nums, target):\n    num_map = {}\n    for i, num in enumerate(nums):\n        complement = target - num\n        if complement in num_map:\n            return [num_map[complement], i]\n        num_map[num] = i'
      },
      testCases: [
        { input: '[2,7,11,15], 9', expectedOutput: '[0,1]', isHidden: false },
        { input: '[3,2,4], 6', expectedOutput: '[1,2]', isHidden: false },
        { input: '[3,3], 6', expectedOutput: '[0,1]', isHidden: true }
      ],
      hints: [
        'Try using a hash map to store numbers and their indices',
        'For each number, check if its complement exists in the hash map'
      ],
      timeComplexity: 'O(n)',
      spaceComplexity: 'O(n)',
      acceptanceRate: 52.3,
      totalSubmissions: 15420,
      acceptedSubmissions: 8065,
      likes: 1247,
      dislikes: 89,
      isActive: true,
      isPremium: false,
      estimatedTime: 30,
      createdBy: '507f1f77bcf86cd799439010',
      updatedBy: '507f1f77bcf86cd799439010',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T14:45:00Z'
    },
    {
      _id: '507f1f77bcf86cd799439012',
      title: 'Valid Parentheses',
      description: 'Given a string s containing just the characters \'(\', \')\', \'{\', \'}\', \'[\' and \']\', determine if the input string is valid. An input string is valid if: Open brackets must be closed by the same type of brackets, Open brackets must be closed in the correct order.',
      difficulty: 'Easy',
      tags: ['String', 'Stack'],
      lab: '507f1f77bcf86cd799439001',
      labSection: '507f1f77bcf86cd799439002',
      category: '507f1f77bcf86cd799439004',
      slug: 'valid-parentheses',
      inputDescription: 's: a string containing parentheses',
      outputDescription: 'Return true if valid, false otherwise',
      constraints: '1 <= s.length <= 10^4, s consists of parentheses only \'()[]{}\'.', 
      examples: [
        {
          input: 's = "()"',
          output: 'true',
          explanation: 'The string contains valid parentheses.'
        },
        {
          input: 's = "()[]{}"',
          output: 'true',
          explanation: 'All brackets are properly closed.'
        },
        {
          input: 's = "(]"',
          output: 'false',
          explanation: 'Brackets are not properly matched.'
        }
      ],
      starterCode: {
        javascript: 'function isValid(s) {\n    // Your code here\n}',
        python: 'def is_valid(s):\n    # Your code here\n    pass',
        java: 'public boolean isValid(String s) {\n    // Your code here\n}',
        cpp: 'bool isValid(string s) {\n    // Your code here\n}'
      },
      solutionCode: {
        javascript: 'function isValid(s) {\n    const stack = [];\n    const map = { ")": "(", "}": "{", "]": "[" };\n    \n    for (let char of s) {\n        if (char in map) {\n            if (stack.pop() !== map[char]) return false;\n        } else {\n            stack.push(char);\n        }\n    }\n    \n    return stack.length === 0;\n}'
      },
      testCases: [
        { input: '"()"', expectedOutput: 'true', isHidden: false },
        { input: '"()[]{}"', expectedOutput: 'true', isHidden: false },
        { input: '"(]"', expectedOutput: 'false', isHidden: false },
        { input: '"([)]"', expectedOutput: 'false', isHidden: true }
      ],
      hints: [
        'Use a stack data structure',
        'Push opening brackets onto the stack',
        'For closing brackets, check if they match the most recent opening bracket'
      ],
      timeComplexity: 'O(n)',
      spaceComplexity: 'O(n)',
      acceptanceRate: 40.8,
      totalSubmissions: 12890,
      acceptedSubmissions: 5259,
      likes: 892,
      dislikes: 45,
      isActive: true,
      isPremium: false,
      estimatedTime: 25,
      createdBy: '507f1f77bcf86cd799439010',
      updatedBy: '507f1f77bcf86cd799439010',
      createdAt: '2024-01-14T09:15:00Z',
      updatedAt: '2024-01-14T16:20:00Z'
    },
    {
      _id: '507f1f77bcf86cd799439013',
      title: 'Longest Substring Without Repeating Characters',
      description: 'Given a string s, find the length of the longest substring without repeating characters.',
      difficulty: 'Medium',
      tags: ['Hash Table', 'String', 'Sliding Window'],
      lab: '507f1f77bcf86cd799439001',
      labSection: '507f1f77bcf86cd799439002',
      category: '507f1f77bcf86cd799439005',
      slug: 'longest-substring-without-repeating',
      inputDescription: 's: a string',
      outputDescription: 'Return the length of the longest substring',
      constraints: '0 <= s.length <= 5 * 10^4, s consists of English letters, digits, symbols and spaces.',
      examples: [
        {
          input: 's = "abcabcbb"',
          output: '3',
          explanation: 'The answer is "abc", with the length of 3.'
        },
        {
          input: 's = "bbbbb"',
          output: '1',
          explanation: 'The answer is "b", with the length of 1.'
        }
      ],
      starterCode: {
        javascript: 'function lengthOfLongestSubstring(s) {\n    // Your code here\n}',
        python: 'def length_of_longest_substring(s):\n    # Your code here\n    pass'
      },
      solutionCode: {
        javascript: 'function lengthOfLongestSubstring(s) {\n    let maxLength = 0;\n    let start = 0;\n    const charMap = new Map();\n    \n    for (let end = 0; end < s.length; end++) {\n        if (charMap.has(s[end])) {\n            start = Math.max(charMap.get(s[end]) + 1, start);\n        }\n        charMap.set(s[end], end);\n        maxLength = Math.max(maxLength, end - start + 1);\n    }\n    \n    return maxLength;\n}'
      },
      testCases: [
        { input: '"abcabcbb"', expectedOutput: '3', isHidden: false },
        { input: '"bbbbb"', expectedOutput: '1', isHidden: false },
        { input: '"pwwkew"', expectedOutput: '3', isHidden: true }
      ],
      hints: [
        'Use the sliding window technique',
        'Keep track of character positions with a hash map',
        'Move the start pointer when you find a duplicate'
      ],
      timeComplexity: 'O(n)',
      spaceComplexity: 'O(min(m,n))',
      acceptanceRate: 33.2,
      totalSubmissions: 18750,
      acceptedSubmissions: 6225,
      likes: 1456,
      dislikes: 123,
      isActive: false,
      isPremium: true,
      estimatedTime: 45,
      createdBy: '507f1f77bcf86cd799439010',
      updatedBy: '507f1f77bcf86cd799439010',
      createdAt: '2024-01-13T11:00:00Z',
      updatedAt: '2024-01-13T15:30:00Z'
    }
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [filterDifficulty, setFilterDifficulty] = useState('');
  const [filterStatus, setFilterStatus] = useState('');
  const [filterTag, setFilterTag] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState(null);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    difficulty: 'Easy',
    tags: [],
    slug: '',
    inputDescription: '',
    outputDescription: '',
    constraints: '',
    examples: [],
    starterCode: {},
    solutionCode: {},
    testCases: [],
    hints: [],
    timeComplexity: '',
    spaceComplexity: '',
    isActive: true,
    isPremium: false,
    estimatedTime: 30
  });

  // Get all unique tags
  const allTags = [...new Set(questions.flatMap(q => q.tags))];

  // Filter questions
  const filteredQuestions = questions.filter(question => {
    const matchesSearch = question.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         question.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesDifficulty = filterDifficulty === '' || question.difficulty === filterDifficulty;
    const matchesStatus = filterStatus === '' || 
      (filterStatus === 'active' && question.isActive) ||
      (filterStatus === 'inactive' && !question.isActive);
    const matchesTag = filterTag === '' || question.tags.includes(filterTag);
    
    return matchesSearch && matchesDifficulty && matchesStatus && matchesTag;
  });

  const handleAdd = () => {
    setEditingQuestion(null);
    setFormData({
      title: '',
      description: '',
      difficulty: 'Easy',
      tags: [],
      slug: '',
      inputDescription: '',
      outputDescription: '',
      constraints: '',
      examples: [],
      starterCode: {},
      solutionCode: {},
      testCases: [],
      hints: [],
      timeComplexity: '',
      spaceComplexity: '',
      isActive: true,
      isPremium: false,
      estimatedTime: 30
    });
    setShowModal(true);
  };

  const handleEdit = (question) => {
    setEditingQuestion(question);
    setFormData({
      title: question.title,
      description: question.description,
      difficulty: question.difficulty,
      tags: question.tags,
      slug: question.slug,
      inputDescription: question.inputDescription,
      outputDescription: question.outputDescription,
      constraints: question.constraints,
      examples: question.examples,
      starterCode: question.starterCode,
      solutionCode: question.solutionCode,
      testCases: question.testCases,
      hints: question.hints,
      timeComplexity: question.timeComplexity,
      spaceComplexity: question.spaceComplexity,
      isActive: question.isActive,
      isPremium: question.isPremium,
      estimatedTime: question.estimatedTime
    });
    setShowModal(true);
  };

  const handleSave = () => {
    if (editingQuestion) {
      setQuestions(prev => prev.map(question => 
        question._id === editingQuestion._id 
          ? { ...question, ...formData, updatedAt: new Date().toISOString() }
          : question
      ));
    } else {
      const newQuestion = {
        _id: Date.now().toString(),
        ...formData,
        totalSubmissions: 0,
        acceptedSubmissions: 0,
        acceptanceRate: 0,
        likes: 0,
        dislikes: 0,
        createdBy: '507f1f77bcf86cd799439010',
        updatedBy: '507f1f77bcf86cd799439010',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setQuestions(prev => [...prev, newQuestion]);
    }
    setShowModal(false);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this question?')) {
      setQuestions(prev => prev.filter(question => question._id !== id));
    }
  };

  const toggleStatus = (id) => {
    setQuestions(prev => prev.map(question => 
      question._id === id 
        ? { ...question, isActive: !question.isActive, updatedAt: new Date().toISOString() }
        : question
    ));
  };

  const handleViewDetails = (question) => {
    setSelectedQuestion(question);
    setShowDetails(true);
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className={`text-2xl font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            Question Manager
          </h2>
          <p className={`mt-1 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`}>
            Comprehensive coding question management based on Question.js and problemModal.js - Total: {questions.length} questions
          </p>
        </div>
        
        <button
          onClick={handleAdd}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Add Question</span>
        </button>
      </div>
    </div>
  );
};

export default QuestionManager;
