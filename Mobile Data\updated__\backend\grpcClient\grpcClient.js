import grpc from "@grpc/grpc-js";
import protoLoader from "@grpc/proto-loader";
import path from "path";

const PROTO_PATH = path.join(process.cwd(), "protos", "execution.proto");

// Load .proto file
const packageDefinition = protoLoader.loadSync(PROTO_PATH, {
  keepCase: true,
  longs: String,
  enums: String,
  defaults: true,
  oneofs: true,
});

const protoDescriptor = grpc.loadPackageDefinition(packageDefinition);

const ExecutorService = protoDescriptor.executor.Executor;

const client = new ExecutorService(
  "localhost:50051",
  grpc.credentials.createInsecure()
);

export const executeViaGrpc = ({ code, language, input, timeout }) => {
  return new Promise((resolve, reject) => {
    client.ExecuteCode({ code, language, input, timeout }, (error, response) => {
      if (error) {
        console.error(error.message);
        return reject(error);
      }
      resolve(response);
    });
  });
};
