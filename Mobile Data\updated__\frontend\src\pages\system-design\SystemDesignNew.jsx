import React from "react";
import { CourseResourcesSection } from "../../components/ui";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";
import SystemDesignHeroNew from "./components/SystemDesignHeroNew";
import SystemDesignLayout from "./components/SystemDesignLayout";
import SystemDesignPremiumModal from "./components/SystemDesignPremiumModal";
import SystemDesignIntroductionNew from "./components/SystemDesignIntroductionNew";
import LabEnvironmentNew from "./components/LabEnvironmentNew";
import InterviewChecklistNew from "./components/InterviewChecklistNew";
import ProjectsComponent from "./components/ProjectsComponent";
import useSystemDesignData from "./hooks/useSystemDesignData";

const SystemDesignNew = () => {
  const { interviewChecklist } = useSystemDesignData();

  const courseConfig = {
    title: "System Design Learning Path",
    subtitle: "Select a resource category to start learning system design through theory, interactive coding, projects, or interview preparation",
    theme: {
      titleColor: "text-gray-100",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn system design fundamentals, patterns, and architectural concepts",
        icon: "📚",
        component: SystemDesignIntroductionNew,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice system design with interactive examples",
        icon: "💻",
        component: LabEnvironmentNew,
        props: {}
      },
      {
        id: "Projects",
        title: "Real-time Projects",
        description: "Build practical system design projects",
        icon: "🏗️",
        component: ProjectsComponent,
        props: {}
      },
      {
        id: "InterviewChecklist",
        title: "Interview Checklist",
        description: "Prepare for system design interviews",
        icon: "✅",
        component: ({ showPremiumOverlay }) => (
          <InterviewChecklistNew interviewChecklist={interviewChecklist} showPremiumOverlay={showPremiumOverlay} />
        ),
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="system-design-course"
            courseName="System Design Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={SystemDesignHeroNew}
      LayoutComponent={SystemDesignLayout}
      PremiumModalComponent={SystemDesignPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default SystemDesignNew;