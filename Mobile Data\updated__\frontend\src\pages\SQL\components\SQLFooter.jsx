import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const SQLFooter = () => {
  const handleScrollToTop = (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <motion.footer 
      className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 text-white py-16"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.8 }}
    >
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Column 1: About SQL */}
          <motion.div 
            className="col-span-1"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1, duration: 0.6 }}
          >
            <div className="flex items-center gap-3 mb-6">
              <img src="/images/logonew.png" alt="Logo" className="w-8 h-8" />
              <span className="font-bold text-xl text-white">UPCODING</span>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed">
              SQL (Structured Query Language) is the standard language for managing relational databases.
              Master SQL to excel in data management and analysis.
            </p>
            <div className="flex space-x-4">
              <Link to="#" className="text-gray-400 hover:text-white transition-colors transform hover:scale-110">
                <i className="fab fa-twitter text-xl"></i>
              </Link>
              <Link to="#" className="text-gray-400 hover:text-white transition-colors transform hover:scale-110">
                <i className="fab fa-linkedin text-xl"></i>
              </Link>
              <Link to="#" className="text-gray-400 hover:text-white transition-colors transform hover:scale-110">
                <i className="fab fa-github text-xl"></i>
              </Link>
            </div>
          </motion.div>
          
          {/* Column 2: Quick Links */}
          <motion.div 
            className="col-span-1"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <h4 className="font-bold text-lg mb-6 text-blue-300">Quick Links</h4>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  🏠 Home
                </Link>
              </li>
              <li>
                <Link to="/pythoncourse" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  🐍 Python Course
                </Link>
              </li>
              <li>
                <Link to="/data_strut" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  📊 DSA
                </Link>
              </li>
              <li>
                <Link to="/GG_75" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  💼 Interview Questions
                </Link>
              </li>
            </ul>
          </motion.div>
          
          {/* Column 3: SQL Resources */}
          <motion.div 
            className="col-span-1"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.6 }}
          >
            <h4 className="font-bold text-lg mb-6 text-green-300">SQL Resources</h4>
            <ul className="space-y-3">
              <li>
                <Link to="#" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  📋 SQL Cheat Sheet
                </Link>
              </li>
              <li>
                <Link to="#" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  💻 Practice Exercises
                </Link>
              </li>
              <li>
                <Link to="#" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  🎯 Interview Preparation
                </Link>
              </li>
              <li>
                <Link to="#" className="text-gray-300 hover:text-white transition-colors hover:translate-x-1 transform inline-block">
                  🏆 SQL Certification
                </Link>
              </li>
            </ul>
          </motion.div>
          
          {/* Column 4: Newsletter */}
          <motion.div 
            className="col-span-1"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            <h4 className="font-bold text-lg mb-6 text-purple-300">Stay Updated</h4>
            <p className="text-gray-300 mb-6 leading-relaxed">
              Subscribe to our newsletter for the latest SQL tips, tutorials and interview questions.
            </p>
            <form className="space-y-4">
              <input 
                type="email" 
                placeholder="Your email" 
                className="w-full bg-gray-800 border border-gray-600 text-white px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              />
              <motion.button 
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 py-3 rounded-lg font-medium transition-all transform hover:scale-105"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Subscribe Now
              </motion.button>
            </form>
          </motion.div>
        </div>
        
        <hr className="border-gray-700 my-8" />
        
        <motion.div 
          className="flex flex-col md:flex-row justify-between items-center"
          initial={{ y: 20, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.5, duration: 0.6 }}
        >
          <p className="text-gray-400 text-center md:text-left">
            &copy; {new Date().getFullYear()} Upcoding. All rights reserved. Made with ❤️ for developers.
          </p>
          <div className="mt-4 md:mt-0">
            <motion.button
              onClick={handleScrollToTop}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white w-12 h-12 rounded-full flex items-center justify-center transition-all transform hover:scale-110"
              whileHover={{ scale: 1.1, rotate: 360 }}
              whileTap={{ scale: 0.9 }}
              transition={{ duration: 0.3 }}
            >
              <i className="fas fa-arrow-up"></i>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
};

export default SQLFooter;
