import mongoose from "mongoose";

const leaderboardSchema = new mongoose.Schema(
  {
    contest: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Contest",
      required: true,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    totalPoints: {
      type: Number,
      default: 0,
    },
    problemsSolved: {
      type: Number,
      default: 0,
    },
    penalty: {
      type: Number,
      default: 0,
    },
    lastSubmissionTime: {
      type: Date,
    },
    rank: {
      type: Number,
      default: null,
    },
    customMeta: {
      type: mongoose.Schema.Types.Mixed,
      comment:
        "For future extensions like hackathon-specific scoring rules, tie-breakers, or additional metadata",
    },
  },
  { timestamps: true }
);

const Leaderboard = mongoose.model("Leaderboard", leaderboardSchema);
export default Leaderboard;
