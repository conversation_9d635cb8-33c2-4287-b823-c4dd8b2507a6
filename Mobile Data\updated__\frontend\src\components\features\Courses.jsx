import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { useState, useEffect, useRef } from "react";
import axiosInstance from "../../utils/axiosInstance";

const Courses = () => {
  const [labs, setLabs] = useState([]);
  const [loading, setLoading] = useState(true);
  const scrollContainerRef = useRef(null);

  useEffect(() => {
    const fetchLabs = async () => {
      try {
        setLoading(true);
        const response = await axiosInstance.get("/lab/all-labs");
        setLabs(response.data.labs);
      } catch (error) {
        console.error("Error fetching labs:", error?.response?.data?.message || error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchLabs();
  }, []);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -400,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 400,
        behavior: 'smooth'
      });
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="py-16 bg-[#1b2433]">
      <div className="w-11/12 mx-auto px-4 ">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="inline-flex items-center px-4 py-2 bg-blue-500/20 text-blue-300 rounded-full text-sm font-medium mb-6 border border-blue-500/30">
            <span className="mr-2">📚</span>
            All Courses
          </div>

          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Master Programming Skills
          </h2>

          <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Learn from industry experts with our comprehensive courses designed to help you 
            build real-world projects and advance your career in technology.
          </p>
        </motion.div>

        {/* Horizontal Scrollable Course List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="relative"
        >
          {/* Scroll Buttons */}
          <button
            onClick={scrollLeft}
            className=" hidden absolute -left-14 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg sm:flex items-center justify-center transition-all duration-300 hover:scale-110"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={scrollRight}
            className="hidden absolute -right-14 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg sm:flex items-center justify-center transition-all duration-300 hover:scale-110"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Scroll Container */}
          <div 
            ref={scrollContainerRef}
            className="flex gap-6 overflow-x-auto scrollbar-hide px-16"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              WebkitScrollbar: {
                display: 'none'
              }
            }}
          >
            {labs?.map((lab, index) => (
              <motion.div
                key={lab._id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="flex-shrink-0 w-80 bg-[#2a3441] rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-700"
              >
                {/* Course Header */}
                <div className="relative h-48 bg-gradient-to-br from-gray-600 to-gray-700 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                      <img
                        src={lab.icon?.secure_url}
                        alt={lab.name}
                        className="w-8 h-8 object-contain"
                      />
                    </div>
                    <span className="bg-white/20 text-white px-3 py-1 rounded-full text-xs font-semibold">
                      {lab.labels?.[0] || "New"}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    {lab.name}
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {lab.labType} Course
                  </p>
                </div>

                {/* Course Content */}
                <div className="p-6">
                  <p className="text-gray-300 mb-4 line-clamp-3">
                    {lab.description}
                  </p>

                  {/* Learning Points */}
                  {lab.learningPoints && lab.learningPoints.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-semibold text-white mb-2">
                        What you'll learn:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {lab.learningPoints.slice(0, 2).map((point, idx) => (
                          <span
                            key={idx}
                            className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded text-sm font-medium border border-blue-500/30"
                          >
                            {point}
                          </span>
                        ))}
                        {lab.learningPoints.length > 2 && (
                          <span className="text-gray-400 text-sm">
                            +{lab.learningPoints.length - 2} more
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Course Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-6">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                      <span>12 weeks</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                      </svg>
                      <span>1,000+ learners</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-4 h-4 mr-1 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span>4.8/5</span>
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Link
                    to={`/labs/${lab._id}`}
                    className="block w-full bg-[#3f75df] hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-semibold text-center transition-colors duration-300"
                  >
                    Start Learning
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Scroll Indicator */}
          <div className="flex justify-center mt-6">
            <div className="flex items-center gap-2 text-gray-400 text-sm">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Use arrows or scroll to explore all courses</span>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </motion.div>

        {/* View All Courses CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <Link
            to="/courses"
            className="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl"
          >
            View All Courses
            <svg
              className="ml-2 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </Link>
        </motion.div>
      </div>
    </div>
  );
};

export default Courses;
