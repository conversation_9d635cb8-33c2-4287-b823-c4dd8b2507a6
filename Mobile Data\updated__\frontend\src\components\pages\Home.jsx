import { LoadingSpinner } from "../ui";
import HomepageLayout from "../layout/HomepageLayout";
import useLoadingState from "../../hooks/useLoadingState";
import useSidebarState from "../../hooks/useSidebarState";

const Home = () => {
  const loading = useLoadingState(1000);
  const { toggleSidebar } = useSidebarState(true);

  // if (loading) {
  //   return <LoadingSpinner />;
  // }

  return (
    // <div className="text-black">hyyy</div>
    <div className="bg-gradient-to-br from-[#1a3c50] to-[#010509]">
      <HomepageLayout toggleSidebar={toggleSidebar} />
    </div>
  );
};

export default Home;
