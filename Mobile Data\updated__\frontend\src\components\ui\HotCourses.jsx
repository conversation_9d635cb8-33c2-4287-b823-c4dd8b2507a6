import React, { useState } from "react";
import { motion } from "framer-motion";
import { PaymentModal } from "./";

// Course type specific icons as components
const PythonIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-blue-400"
    fill="currentColor"
  >
    <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zm5.696 5.858c.093.003.178.014.256.036.119.033.237.087.348.17.21.157.368.392.403.7.036.308-.052.637-.27.942-.085.12-.195.23-.327.334l-.043.032c-.698.52-1.833.591-2.618.162-.83-.453-1.282-1.443-1.095-2.325.168-.788.886-1.328 1.766-1.363.146-.007.291-.002.433.016.423.054.798.192 1.092.406.076.055.137.103.181.142.106.094.158.142.172.146.031.008.059-.016.078-.072.013-.038.019-.085.018-.141 0-.113-.026-.274-.083-.444-.096-.287-.26-.507-.52-.675a1.96 1.96 0 00-.7-.27 2.432 2.432 0 00-.51-.051c-.09 0-.174.004-.254.012l.072-.003zm-9.697.597c-.731.004-1.35.447-1.62 1.149-.286.746-.172 1.633.298 2.298.494.7 1.343 1.106 2.225 1.064.882-.042 1.68-.517 2.093-1.249.31-.551.355-1.188.13-1.759a1.892 1.892 0 00-.094-.194l-.04-.069c-.01-.016-.014-.024-.015-.026a.26.26 0 01-.007-.02c-.006-.016-.003-.03.009-.04.042-.036.171-.03.347.024.209.064.44.187.666.36.067.051.131.104.191.16.305.284.492.62.557 1.005.066.39-.003.8-.205 1.204-.205.41-.533.77-.96 1.054-.115.076-.24.147-.37.211-.244.12-.497.209-.752.262-.37.077-.739.091-1.095.04-.698-.101-1.332-.441-1.77-.98a2.564 2.564 0 01-.517-.972c-.063-.204-.098-.41-.104-.611a2.27 2.27 0 01.062-.676c.063-.239.16-.462.287-.668.267-.433.66-.762 1.144-.96.52-.213 1.108-.26 1.682-.142.144.03.284.07.42.122.216.081.419.189.603.321.272.196.502.435.68.71.075.115.139.236.193.36.103.237.174.484.212.737" />
  </svg>
);

const AIIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-blue-400"
    fill="currentColor"
  >
    <path d="M12 2C7.58 2 4 5.58 4 10c0 2.03.76 3.87 2 5.28V20c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2v-4.72c1.24-1.41 2-3.25 2-5.28 0-4.42-3.58-8-8-8zm4 13.5c-.06.17-.15.33-.26.47l-.06.06c-.09.09-.19.16-.3.22-.12.06-.24.09-.38.09h-6c-.14 0-.26-.03-.38-.09-.11-.06-.21-.13-.3-.22l-.06-.06c-.11-.14-.2-.3-.26-.47-.06-.16-.09-.33-.09-.5V15c0-.28.11-.53.29-.71l.29-.29h6.86l.29.29c.18.18.29.43.29.71v.5c.01.17-.02.34-.08.5zm-4-4.5H8c-.55 0-1-.45-1-1s.45-1 1-1h8c.55 0 1 .45 1 1s-.45 1-1 1zm0-3H8c-.55 0-1-.45-1-1s.45-1 1-1h8c.55 0 1 .45 1 1s-.45 1-1 1z" />
  </svg>
);

const FullStackIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-blue-400"
    fill="currentColor"
  >
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
  </svg>
);

const DataScienceIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-blue-400"
    fill="currentColor"
  >
    <path d="M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zm0 6c-4.42 0-8-1.79-8-4v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4zm0 5c-4.42 0-8-1.79-8-4v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4z" />
  </svg>
);

const DefaultIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-blue-400"
    fill="currentColor"
  >
    <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z" />
  </svg>
);

// Additional course icons
const DSAIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-orange-400"
    fill="currentColor"
  >
    <path d="M12 2L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z" />
  </svg>
);

const JavaScriptIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-yellow-400"
    fill="currentColor"
  >
    <path d="M3 3h18v18H3V3zm16.525 13.707c-.131-.821-.666-1.511-2.252-2.155-.552-.259-1.165-.438-1.349-.854-.068-.248-.078-.382-.034-.529.113-.484.687-.629 1.137-.495.293.09.563.315.732.676.775-.507.775-.507 1.316-.844-.203-.314-.304-.451-.439-.586-.473-.528-1.103-.798-2.126-.77l-.528.067c-.507.124-.991.395-1.283.754-.855.968-.608 2.655.427 3.354 1.023.765 2.521.933 2.712 1.653.18.878-.652 1.159-1.475 1.058-.607-.136-.945-.439-1.316-1.002l-1.372.788c.157.359.337.517.607.832 1.305 1.316 4.568 1.249 5.153-.754.021-.067.18-.528.056-1.237l.034.049zm-6.737-5.434h-1.686c0 1.453-.007 2.898-.007 4.354 0 .924.047 1.772-.104 2.033-.247.517-.886.451-1.175.359-.297-.146-.448-.349-.623-.641-.047-.078-.082-.146-.095-.146l-1.368.844c.229.473.563.879.994 1.137.641.383 1.502.507 2.404.305.588-.17 1.095-.519 1.358-1.059.384-.697.302-1.553.299-2.509.008-1.541 0-3.083 0-4.635l.003-.042z" />
  </svg>
);

const DevOpsIcon = () => (
  <svg
    viewBox="0 0 24 24"
    className="w-6 h-6 text-green-400"
    fill="currentColor"
  >
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
  </svg>
);

const CourseIcons = {
  python: PythonIcon,
  ai: AIIcon,
  fullstack: FullStackIcon,
  datascience: DataScienceIcon,
  machinelearning: AIIcon,
  dsa: DSAIcon,
  javascript: JavaScriptIcon,
  devops: DevOpsIcon,
  default: DefaultIcon,
};

// Function to get icon component - moved outside main component
const getIconComponent = (courseType) => {
  if (!courseType) return <DefaultIcon />;
  const type = courseType.toLowerCase().replace(/[^a-z]/g, "");
  const IconComponent = CourseIcons[type] || CourseIcons.default;
  return <IconComponent />;
};

// Static fallback data for hot courses
const staticHotCourses = [
  {
    title: "Python Programming Mastery",
    type: "python",
    students: "3.2k+",
    rating: 4.9,
    trending: true,
  },
  {
    title: "Full Stack Development",
    type: "fullstack",
    students: "2.8k+",
    rating: 4.8,
    trending: true,
  },
  {
    title: "Data Science Complete",
    type: "datascience",
    students: "2.1k+",
    rating: 4.7,
    trending: true,
  },
  {
    title: "Data Structures & Algorithms",
    type: "dsa",
    students: "4.5k+",
    rating: 4.9,
    trending: true,
  },
  {
    title: "JavaScript Mastery",
    type: "javascript",
    students: "3.9k+",
    rating: 4.8,
    trending: true,
  },
];

const HotCourses = ({ hotLabs }) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);

  // Use API data if available, otherwise use static data
  const coursesToDisplay =
    hotLabs && hotLabs.length > 0 ? hotLabs : staticHotCourses;

  const handleCourseClick = (course) => {
    const courseData = {
      name: course.title,
      labType: course.type,
      _id: course.type,
    };
    setSelectedCourse(courseData);
    setIsPaymentModalOpen(true);
  };

  const closePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedCourse(null);
  };

  return (
    <section className="py-16 ">
      <div className="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center px-4 py-2 bg-orange-500/20 text-orange-300 rounded-full text-sm font-medium mb-6 border border-orange-500/30">
            <span className="mr-2">🔥</span>
            Trending Courses
          </div>

          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6">
            Most Popular Courses
          </h2>

          <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Join thousands of students learning from our most popular and highly-rated courses.
          </p>
        </motion.div>

        {/* Courses Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
          {coursesToDisplay?.map((course, index) => (
            <CourseCard 
              key={index} 
              course={course} 
              index={index}
              onClick={() => handleCourseClick(course)}
            />
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <button className="inline-flex items-center px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors duration-300 shadow-lg hover:shadow-xl">
            View All Courses
            <svg
              className="ml-2 w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </button>
        </motion.div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={closePaymentModal}
        courseData={selectedCourse}
      />
    </section>
  );
};

const CourseCard = ({ course, index, onClick }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.6, delay: 0.1 * index }}
    className="bg-[#2a3441] rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-700 group cursor-pointer"
    onClick={onClick}
  >
    {/* Card Header */}
    <div className="relative p-6 bg-gradient-to-br from-slate-700 to-gray-600">
      <div className="flex items-center justify-between mb-4">
        <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
          {getIconComponent(course.type)}
        </div>
        {course.trending && (
          <span className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full text-xs font-semibold border border-orange-500/30">
            🔥 Trending
          </span>
        )}
      </div>
      <h3 className="text-xl font-bold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
        {course.title}
      </h3>
    </div>

    {/* Card Content */}
    <div className="p-6">
      {/* Course Stats */}
      <div className="flex items-center justify-between text-sm text-gray-400 mb-6">
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
          </svg>
          <span>{course.students} students</span>
        </div>
        <div className="flex items-center">
          <svg className="w-4 h-4 mr-2 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
          <span>{course.rating}</span>
        </div>
      </div>

      {/* CTA Button */}
      <button className="block w-full bg-[#3d5c88] hover:bg-blue-900 text-white py-3 px-4 rounded-lg font-semibold text-center transition-colors duration-300">
        Enroll Now
      </button>
    </div>
  </motion.div>
);

export default HotCourses;
