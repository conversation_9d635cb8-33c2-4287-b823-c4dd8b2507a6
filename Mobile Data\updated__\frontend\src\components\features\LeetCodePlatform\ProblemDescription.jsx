import React from 'react';
import { motion } from 'framer-motion';

const ProblemDescription = ({ problem, isDarkMode }) => {
  const getDifficultyColor = (difficulty) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'text-green-500 bg-green-500/10 border-green-500/20';
      case 'medium':
        return 'text-yellow-500 bg-yellow-500/10 border-yellow-500/20';
      case 'hard':
        return 'text-red-500 bg-red-500/10 border-red-500/20';
      default:
        return 'text-gray-500 bg-gray-500/10 border-gray-500/20';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="p-6 h-full overflow-auto"
    >
      {/* Problem Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">{problem.title}</h1>
          <span
            className={`px-3 py-1 rounded-full text-sm font-medium border ${getDifficultyColor(
              problem.difficulty
            )}`}
          >
            {problem.difficulty}
          </span>
        </div>
        
        {/* Problem Stats */}
        <div className="flex items-center gap-6 text-sm">
          <div className={`flex items-center gap-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <span>👍</span>
            <span>12.5k</span>
          </div>
          <div className={`flex items-center gap-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <span>👎</span>
            <span>234</span>
          </div>
          <div className={`flex items-center gap-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <span>💬</span>
            <span>1.2k</span>
          </div>
          <div className={`flex items-center gap-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <span>📊</span>
            <span>Acceptance: 49.2%</span>
          </div>
        </div>
      </div>

      {/* Problem Description */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">Problem</h2>
        <p className={`leading-relaxed ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {problem.description}
        </p>
      </div>

      {/* Examples */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">Examples</h2>
        <div className="space-y-4">
          {problem.examples.map((example, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                isDarkMode 
                  ? 'bg-gray-800/50 border-gray-700' 
                  : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="mb-2">
                <span className="font-semibold">Example {index + 1}:</span>
              </div>
              
              <div className="space-y-2 font-mono text-sm">
                <div>
                  <span className={`font-semibold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                    Input:
                  </span>
                  <span className="ml-2">{example.input}</span>
                </div>
                
                <div>
                  <span className={`font-semibold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                    Output:
                  </span>
                  <span className="ml-2">{example.output}</span>
                </div>
                
                {example.explanation && (
                  <div>
                    <span className={`font-semibold ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>
                      Explanation:
                    </span>
                    <span className={`ml-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {example.explanation}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Constraints */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">Constraints</h2>
        <ul className={`space-y-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {problem.constraints.map((constraint, index) => (
            <li key={index} className="flex items-start">
              <span className="text-blue-500 mr-2">•</span>
              <span className="font-mono text-sm">{constraint}</span>
            </li>
          ))}
        </ul>
      </div>

      {/* Follow-up */}
      <div className={`p-4 rounded-lg border ${
        isDarkMode 
          ? 'bg-blue-900/20 border-blue-700/30' 
          : 'bg-blue-50 border-blue-200'
      }`}>
        <h3 className={`font-semibold mb-2 ${isDarkMode ? 'text-blue-400' : 'text-blue-700'}`}>
          💡 Follow-up
        </h3>
        <p className={`text-sm ${isDarkMode ? 'text-blue-300' : 'text-blue-600'}`}>
          Can you come up with an algorithm that is less than O(n²) time complexity?
        </p>
      </div>

      {/* Related Topics */}
      <div className="mt-6">
        <h3 className="text-sm font-semibold mb-3">Related Topics</h3>
        <div className="flex flex-wrap gap-2">
          {['Array', 'Hash Table'].map((topic) => (
            <span
              key={topic}
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              } cursor-pointer transition-colors`}
            >
              {topic}
            </span>
          ))}
        </div>
      </div>

      {/* Similar Questions */}
      <div className="mt-6">
        <h3 className="text-sm font-semibold mb-3">Similar Questions</h3>
        <div className="space-y-2">
          {[
            { title: 'Three Sum', difficulty: 'Medium' },
            { title: 'Four Sum', difficulty: 'Medium' },
            { title: 'Two Sum II - Input Array Is Sorted', difficulty: 'Medium' }
          ].map((question, index) => (
            <div
              key={index}
              className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
                isDarkMode
                  ? 'hover:bg-gray-800'
                  : 'hover:bg-gray-100'
              }`}
            >
              <span className="text-sm">{question.title}</span>
              <span
                className={`text-xs px-2 py-1 rounded ${getDifficultyColor(
                  question.difficulty
                )}`}
              >
                {question.difficulty}
              </span>
            </div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default ProblemDescription;
