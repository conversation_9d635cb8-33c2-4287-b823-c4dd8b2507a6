import mongoose from "mongoose";
import Lab from "../models/labModal.js";
import { CatchAsyncError } from "../middleware/CatchAsyncError.js";
import ErrorHandler from "../utils/ErrorHandler.js";
import {
  deleteFromCloudinary,
  deleteLocalFiles,
  uploadOnCloudinary,
} from "../utils/uploadOnCloudinary.js";
import { sanitizeObject } from "../utils/sanitizeInput.js";
import { redis } from "../utils/redisClient.js";
import cloudinary from "../utils/cloudinaryConfig.js";

export const createLab = CatchAsyncError(async (req, res, next) => {
  const file = req.file;

  if (!file) {
    return next(new ErrorHandler("Lab icon is required", 400));
  }

  const sanitizedData = sanitizeObject(req.body, {
    name: "string",
    description: "string",
    labType: "string",
    learningPoints: "array",
    labels: "array",
  });

  console.log(req.body);

  const { name, description, labType, learningPoints, labels } = sanitizedData;

  const allowedTypes = [
    "DSA",
    "Frontend",
    "Backend",
    "DevOps",
    "Database",
    "Microservices",
    "CICD",
    "Python",
    "System Design",
    "Data Science",
    "AIML",
  ];

  if (!allowedTypes.includes(labType)) {
    return next(new ErrorHandler("Invalid lab type", 400));
  }

  const allowedLabels = [
    "Hot",
    "Popular",
    "Career",
    "Advanced",
    "In Demand",
    "New",
  ];
  const validLabels = Array.isArray(labels)
    ? labels.map((l) => l.trim()).filter((l) => allowedLabels.includes(l))
    : [];

  const allowedMimeTypes = [
    "image/png",
    "image/jpeg",
    "image/jpg",
    "image/svg+xml",
    "image/webp",
  ];
  if (!allowedMimeTypes.includes(file.mimetype)) {
    return next(new ErrorHandler("Invalid image type", 400));
  }

  let uploadedIcon = null;

  try {
    const existing = await Lab.findOne({ name: name.trim() });
    if (existing) {
      return next(new ErrorHandler("Lab with this name already exists", 400));
    }

    uploadedIcon = await uploadOnCloudinary(file.path);
    if (!uploadedIcon?.secure_url) {
      return next(new ErrorHandler("Icon upload failed", 500));
    }

    const newLab = await Lab.create({
      name: name.trim(),
      description: description?.trim(),
      labType,
      learningPoints: Array.isArray(learningPoints)
        ? learningPoints.map((p) => p.trim()).filter(Boolean)
        : [],
      labels: validLabels,
      icon: {
        public_id: uploadedIcon.public_id,
        secure_url: uploadedIcon.secure_url,
      },
      createdBy: req.user._id,
    });

    await redis.del("labs:all");

    return res.status(201).json({
      success: true,
      message: "Lab created !!",
      lab: newLab,
    });
  } catch (err) {
    if (uploadedIcon?.public_id) {
      await deleteFromCloudinary(uploadedIcon.public_id);
    }
    return next(new ErrorHandler(err.message || "Something went wrong", 500));
  } finally {
    if (file?.path) {
      deleteLocalFiles(file.path);
    }
  }
});

export const updateLab = CatchAsyncError(async (req, res, next) => {
  const labId = req.params.id;

  if (!mongoose.Types.ObjectId.isValid(labId)) {
    return next(new ErrorHandler("Invalid Lab ID", 400));
  }

  const lab = await Lab.findById(labId);
  if (!lab) {
    return next(new ErrorHandler("Lab not found", 404));
  }

  const sanitized = sanitizeObject(req.body, {
    title: "string",
    name: "string",
    description: "string",
    labType: "string",
    isActive: "boolean",
    learningPoints: "array",
    labels: "array",
  });

  const {
    title,
    name,
    description,
    labType,
    isActive,
    learningPoints,
    labels,
  } = sanitized;

  const allowedLabTypes = [
    "DSA",
    "Frontend",
    "Backend",
    "DevOps",
    "Database",
    "Microservices",
    "CI/CD",
    "Python",
    "System Design",
    "Data Science",
  ];

  const allowedLabels = [
    "Hot",
    "Popular",
    "Career",
    "Advanced",
    "In Demand",
    "New",
  ];

  if (labType && !allowedLabTypes.includes(labType)) {
    return next(new ErrorHandler("Invalid lab type", 400));
  }

  if (labels && Array.isArray(labels)) {
    const invalid = labels.filter((lbl) => !allowedLabels.includes(lbl));
    if (invalid.length > 0) {
      return next(
        new ErrorHandler(`Invalid label(s): ${invalid.join(", ")}`, 400)
      );
    }
    lab.labels = labels;
  }

  if (req.file) {
    try {
      if (lab.icon?.public_id) {
        await deleteFromCloudinary(lab.icon.public_id);
      }

      const result = await uploadOnCloudinary(req.file.path);
      if (!result?.secure_url) {
        return next(new ErrorHandler("Image upload failed", 500));
      }

      lab.icon = {
        public_id: result.public_id,
        secure_url: result.secure_url,
      };
    } catch (uploadErr) {
      console.log(uploadErr.message);
      return next(new ErrorHandler("Image upload failed", 500));
    } finally {
      if (req.file?.path) {
        deleteLocalFiles(req.file.path);
      }
    }
  }

  if (title) lab.title = title.trim();
  if (name) lab.name = name.trim();
  if (description) lab.description = description.trim();
  if (labType) lab.labType = labType;
  if (typeof isActive === "boolean") lab.isActive = isActive;

  if (Array.isArray(learningPoints)) {
    lab.learningPoints = learningPoints.map((pt) => pt.trim()).filter(Boolean);
  }

  await lab.save();
  await redis.del("labs:all");

  res.status(200).json({
    success: true,
    message: "Lab updated !!",
    lab,
  });
});

export const getAllLabs = CatchAsyncError(async (req, res) => {
  const cached = await redis.get("labs:all");

  if (cached) {
    return res.status(200).json({
      success: true,
      message: "Labs fetched from cache",
      labs: cached,
    });
  }

  const labs = await Lab.find({ isActive: true })
    .select(
      "name description learningPoints labels labType icon slug createdAt updatedAt"
    )
    .sort({ createdAt: -1 })
    .lean();

  await redis.set("labs:all", labs, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: "Labs fetched from DB",
    labs,
  });
});

export const getPopularLabs = CatchAsyncError(async (req, res) => {
  const cached = await redis.get("labs:popular");

  if (cached) {
    return res.status(200).json({
      success: true,
      message: "Popular labs fetched from cache",
      labs: cached,
    });
  }

  const labs = await Lab.find({
    isActive: true,
    labels: "Popular",
  })
    .select(
      "name description learningPoints labels labType icon slug createdAt updatedAt"
    )
    .sort({ createdAt: -1 })
    .lean();

  await redis.set("labs:popular", labs, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: "Popular labs fetched from DB",
    labs,
  });
});

export const getAdvancedLabs = CatchAsyncError(async (req, res) => {
  const cached = await redis.get("labs:advanced");

  if (cached) {
    return res.status(200).json({
      success: true,
      message: "Advanced labs fetched from cache",
      labs: cached,
    });
  }

  const labs = await Lab.find({
    isActive: true,
    labels: "Advanced",
  })
    .select(
      "name description learningPoints labels labType icon slug createdAt updatedAt"
    )
    .sort({ createdAt: -1 })
    .lean();

  await redis.set("labs:advanced", labs, { ex: 3600 });

  res.status(200).json({
    success: true,
    message: "Popular labs fetched from DB",
    labs,
  });
});

export const getLabById = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Lab ID", 400));
  }

  const lab = await Lab.findById(id)
    .select("name description labType icon slug learningPoints title labels isActive createdAt updatedAt")
    .lean();

  if (!lab) {
    return next(new ErrorHandler("Lab not found", 404));
  }

  res.status(200).json({
    success: true,
    message: "Lab fetched successfully",
    lab,
  });
});

export const deleteLab = CatchAsyncError(async (req, res, next) => {
  const { id } = req.params;

  if (!mongoose.Types.ObjectId.isValid(id)) {
    return next(new ErrorHandler("Invalid Lab ID", 400));
  }

  const lab = await Lab.findById(id);

  if (!lab) {
    return next(new ErrorHandler("Lab not found", 404));
  }
  if (lab.icon?.public_id) {
    try {
      await cloudinary.uploader.destroy(lab.icon.public_id);
    } catch (error) {
      console.error(error.message);
    }
  }

  await Lab.findByIdAndDelete(id);

  await redis.del("labs:all");

  res.status(200).json({
    success: true,
    message: "Lab deleted successfully",
  });
});
