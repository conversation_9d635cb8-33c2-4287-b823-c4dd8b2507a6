{"aggregate": {"counters": {"vusers.created_by_name.Two Sum JS Execution": 150, "vusers.created": 150, "http.requests": 150, "http.codes.200": 7, "http.responses": 7, "http.downloaded_bytes": 2954, "plugins.metrics-by-endpoint./api/v1/problem/execute/run.codes.200": 7, "vusers.failed": 143, "vusers.completed": 7, "plugins.metrics-by-endpoint./api/v1/problem/execute/run.errors.ETIMEDOUT": 143, "errors.ETIMEDOUT": 143}, "rates": {"http.request_rate": 5}, "firstCounterAt": 1752743022630, "firstHistogramAt": 1752743029616, "lastCounterAt": 1752743052344, "lastHistogramAt": 1752743033522, "firstMetricAt": 1752743022630, "lastMetricAt": 1752743052344, "period": 1752743050000, "summaries": {"http.response_time": {"min": 6852, "max": 9981, "count": 7, "mean": 8502.3, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 6852, "max": 9981, "count": 7, "mean": 8502.3, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time./api/v1/problem/execute/run": {"min": 6852, "max": 9981, "count": 7, "mean": 8502.3, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 6872.6, "max": 9997.9, "count": 7, "mean": 8519, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}}, "histograms": {"http.response_time": {"min": 6852, "max": 9981, "count": 7, "mean": 8502.3, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 6852, "max": 9981, "count": 7, "mean": 8502.3, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time./api/v1/problem/execute/run": {"min": 6852, "max": 9981, "count": 7, "mean": 8502.3, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 6872.6, "max": 9997.9, "count": 7, "mean": 8519, "p50": 8520.7, "median": 8520.7, "p75": 9416.8, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}}}, "intermediate": [{"counters": {"vusers.created_by_name.Two Sum JS Execution": 46, "vusers.created": 46, "http.requests": 46, "http.codes.200": 2, "http.responses": 2, "http.downloaded_bytes": 819, "plugins.metrics-by-endpoint./api/v1/problem/execute/run.codes.200": 2, "vusers.failed": 0, "vusers.completed": 2}, "rates": {"http.request_rate": 7}, "http.request_rate": null, "firstCounterAt": 1752743022630, "firstHistogramAt": 1752743029616, "lastCounterAt": 1752743029925, "lastHistogramAt": 1752743029897, "firstMetricAt": 1752743022630, "lastMetricAt": 1752743029925, "period": "1752743020000", "summaries": {"http.response_time": {"min": 6852, "max": 7171, "count": 2, "mean": 7011.5, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}, "http.response_time.2xx": {"min": 6852, "max": 7171, "count": 2, "mean": 7011.5, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}, "plugins.metrics-by-endpoint.response_time./api/v1/problem/execute/run": {"min": 6852, "max": 7171, "count": 2, "mean": 7011.5, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}, "vusers.session_length": {"min": 6872.6, "max": 7190.6, "count": 2, "mean": 7031.6, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}}, "histograms": {"http.response_time": {"min": 6852, "max": 7171, "count": 2, "mean": 7011.5, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}, "http.response_time.2xx": {"min": 6852, "max": 7171, "count": 2, "mean": 7011.5, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}, "plugins.metrics-by-endpoint.response_time./api/v1/problem/execute/run": {"min": 6852, "max": 7171, "count": 2, "mean": 7011.5, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}, "vusers.session_length": {"min": 6872.6, "max": 7190.6, "count": 2, "mean": 7031.6, "p50": 6838, "median": 6838, "p75": 6838, "p90": 6838, "p95": 6838, "p99": 6838, "p999": 6838}}}, {"counters": {"http.codes.200": 5, "http.responses": 5, "http.downloaded_bytes": 2135, "plugins.metrics-by-endpoint./api/v1/problem/execute/run.codes.200": 5, "vusers.failed": 39, "vusers.completed": 5, "vusers.created_by_name.Two Sum JS Execution": 79, "vusers.created": 79, "http.requests": 79, "plugins.metrics-by-endpoint./api/v1/problem/execute/run.errors.ETIMEDOUT": 39, "errors.ETIMEDOUT": 39}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1752743030000, "firstHistogramAt": 1752743030311, "lastCounterAt": 1752743039942, "lastHistogramAt": 1752743033522, "firstMetricAt": 1752743030000, "lastMetricAt": 1752743039942, "period": "1752743030000", "summaries": {"http.response_time": {"min": 7655, "max": 9981, "count": 5, "mean": 9098.6, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 7655, "max": 9981, "count": 5, "mean": 9098.6, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time./api/v1/problem/execute/run": {"min": 7655, "max": 9981, "count": 5, "mean": 9098.6, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 7684.9, "max": 9997.9, "count": 5, "mean": 9113.9, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}}, "histograms": {"http.response_time": {"min": 7655, "max": 9981, "count": 5, "mean": 9098.6, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "http.response_time.2xx": {"min": 7655, "max": 9981, "count": 5, "mean": 9098.6, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time./api/v1/problem/execute/run": {"min": 7655, "max": 9981, "count": 5, "mean": 9098.6, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 7684.9, "max": 9997.9, "count": 5, "mean": 9113.9, "p50": 9416.8, "median": 9416.8, "p75": 9801.2, "p90": 9801.2, "p95": 9801.2, "p99": 9801.2, "p999": 9801.2}}}, {"counters": {"plugins.metrics-by-endpoint./api/v1/problem/execute/run.errors.ETIMEDOUT": 79, "errors.ETIMEDOUT": 79, "vusers.failed": 79, "vusers.created_by_name.Two Sum JS Execution": 25, "vusers.created": 25, "http.requests": 25}, "rates": {"http.request_rate": 15}, "http.request_rate": null, "firstCounterAt": 1752743040002, "lastCounterAt": 1752743049960, "firstMetricAt": 1752743040002, "lastMetricAt": 1752743049960, "period": "1752743040000", "summaries": {}, "histograms": {}}, {"counters": {"plugins.metrics-by-endpoint./api/v1/problem/execute/run.errors.ETIMEDOUT": 25, "errors.ETIMEDOUT": 25, "vusers.failed": 25}, "rates": {}, "firstCounterAt": 1752743050018, "lastCounterAt": 1752743052344, "firstMetricAt": 1752743050018, "lastMetricAt": 1752743052344, "period": "1752743050000", "summaries": {}, "histograms": {}}]}