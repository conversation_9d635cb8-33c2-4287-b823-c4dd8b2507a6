import { Worker } from "worker_threads";
import path from "path";
import { EventEmitter } from "events";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class WorkerPool extends EventEmitter {
  constructor(size = 4) {
    super();
    this.size = size;
    this.pool = [];
    this.idleWorkers = [];
    this.taskQueue = [];

    for (let i = 0; i < size; i++) {
      this.createWorker();
    }
  }

  createWorker() {
    const worker = new Worker(path.resolve(__dirname, "sandboxWorker.js"));

    worker.on("message", (result) => {
      worker.busy = false;
      this.idleWorkers.push(worker);
      this.emit("result", result);
      this.processQueue();
    });

    worker.on("error", (error) => {
      console.error(error.message);
      this.emit(error.message);
      this.pool = this.pool.filter((w) => w !== worker);
      this.createWorker();
      this.processQueue();
    });

    worker.on("exit", (code) => {
      if (code !== 0) {
        console.warn(`Worker exited with code ${code}`);
        this.pool = this.pool.filter((w) => w !== worker);
        this.createWorker();
      }
    });

    worker.busy = false;
    this.pool.push(worker);
    this.idleWorkers.push(worker);
  }

  async runTask(taskData) {
    return new Promise((resolve, reject) => {
      const task = { data: taskData, resolve, reject };
      this.taskQueue.push(task);
      this.processQueue();
    });
  }

  processQueue() {
    if (this.idleWorkers.length === 0 || this.taskQueue.length === 0) return;

    const worker = this.idleWorkers.shift();
    const task = this.taskQueue.shift();

    worker.busy = true;

    const timeout = setTimeout(() => {
      worker.terminate();
      this.emit("timeout", task.data);
      task.reject(new Error("Code execution timed out"));
    }, 5000);

    worker.once("message", (result) => {
      clearTimeout(timeout);
      task.resolve(result);
    });

    worker.once("error", (err) => {
      clearTimeout(timeout);
      task.reject(err);
    });

    worker.postMessage(task.data);
  }

  getStatus() {
    return {
      total: this.pool.length,
      idle: this.idleWorkers.length,
      busy: this.pool.filter((w) => w.busy).length,
      queued: this.taskQueue.length,
    };
  }

  shutdown() {
    this.pool.forEach((worker) => worker.terminate());
    this.pool = [];
    this.idleWorkers = [];
    this.taskQueue = [];
  }
}

const workerPool = new WorkerPool(4);
export default workerPool;
